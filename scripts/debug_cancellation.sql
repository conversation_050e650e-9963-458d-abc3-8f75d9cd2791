-- Script de diagnóstico para campos de cancelación
-- Ejecutar en tu base de datos para verificar el estado actual

-- 1. Verificar que los campos de cancelación existen
SELECT 'Verificando estructura de tabla ventas...' as paso;

SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'ventas' 
AND column_name IN ('cancelada', 'fecha_cancelacion', 'usuario_cancelacion', 'motivo_cancelacion')
ORDER BY column_name;

-- 2. Verificar datos actuales de ventas
SELECT 'Verificando datos de ventas...' as paso;

SELECT 
    COUNT(*) as total_ventas,
    COUNT(CASE WHEN cancelada = true THEN 1 END) as ventas_canceladas,
    COUNT(CASE WHEN cancelada = false THEN 1 END) as ventas_activas
FROM ventas;

-- 3. Mostrar últimas 10 ventas con estado de cancelación
SELECT 'Últimas 10 ventas con estado de cancelación...' as paso;

SELECT 
    id,
    numero_venta,
    cancelada,
    fecha_cancelacion,
    usuario_cancelacion,
    motivo_cancelacion,
    fecha_venta
FROM ventas 
ORDER BY id DESC 
LIMIT 10;

-- 4. Mostrar solo ventas canceladas (si las hay)
SELECT 'Ventas canceladas (si las hay)...' as paso;

SELECT 
    id,
    numero_venta,
    cancelada,
    fecha_cancelacion,
    usuario_cancelacion,
    motivo_cancelacion,
    fecha_venta,
    monto_total
FROM ventas 
WHERE cancelada = true
ORDER BY fecha_cancelacion DESC;

-- 5. Verificar usuarios disponibles para cancelación
SELECT 'Usuarios disponibles para cancelación...' as paso;

SELECT 
    username,
    nombre,
    nombre_display,
    activo
FROM usuarios 
WHERE activo = true
ORDER BY username;

-- 6. Verificar constraints de cancelación
SELECT 'Verificando constraints de cancelación...' as paso;

SELECT 
    constraint_name,
    check_clause
FROM information_schema.check_constraints 
WHERE constraint_name = 'chk_cancelacion_consistente';

-- 7. Verificar foreign keys de cancelación
SELECT 'Verificando foreign keys de cancelación...' as paso;

SELECT 
    constraint_name,
    table_name,
    column_name,
    foreign_table_name,
    foreign_column_name
FROM information_schema.key_column_usage 
WHERE constraint_name = 'fk_ventas_usuario_cancelacion';

-- 8. Test de actualización (solo para verificar, se hace rollback)
SELECT 'Preparando test de actualización...' as paso;

-- Buscar una venta no cancelada para probar
SELECT 
    id,
    numero_venta,
    cancelada,
    'Esta venta se puede usar para test de cancelación' as nota
FROM ventas 
WHERE cancelada = false 
LIMIT 1;

-- NOTA: Para probar la actualización, ejecutar manualmente:
-- BEGIN;
-- UPDATE ventas 
-- SET cancelada = true, 
--     fecha_cancelacion = NOW(), 
--     usuario_cancelacion = 'admin', 
--     motivo_cancelacion = 'Test manual'
-- WHERE id = [ID_DE_VENTA_NO_CANCELADA];
-- 
-- -- Verificar el resultado
-- SELECT id, numero_venta, cancelada, fecha_cancelacion, usuario_cancelacion, motivo_cancelacion
-- FROM ventas WHERE id = [ID_DE_VENTA_NO_CANCELADA];
-- 
-- ROLLBACK; -- Para no afectar datos reales

SELECT 'Diagnóstico completado. Revisar resultados arriba.' as resultado;
