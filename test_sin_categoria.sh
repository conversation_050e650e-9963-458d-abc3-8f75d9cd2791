#!/bin/bash

# Script para probar la funcionalidad de la categoría especial "Sin Categoría"

echo "=== Probando funcionalidad de categoría especial 'Sin Categoría' ==="
echo

# 1. Obtener todas las categorías para verificar que existe "Sin Categoría"
echo "1. Obteniendo todas las categorías..."
curl -s http://localhost:8080/api/categorias | jq '.categorias[] | select(.nombre == "Sin Categoría")'
echo

# 2. Obtener específicamente la categoría especial
echo "2. Obteniendo categoría especial directamente..."
curl -s http://localhost:8080/api/categorias/sin-categoria | jq '.'
echo

# 3. Intentar eliminar la categoría especial (debería fallar)
echo "3. Intentando eliminar categoría especial (debería fallar)..."
SIN_CATEGORIA_ID=$(curl -s http://localhost:8080/api/categorias/sin-categoria | jq -r '.id')
if [ "$SIN_CATEGORIA_ID" != "null" ] && [ "$SIN_CATEGORIA_ID" != "" ]; then
    echo "ID de 'Sin Categoría': $SIN_CATEGORIA_ID"
    curl -s -X DELETE http://localhost:8080/api/categorias/$SIN_CATEGORIA_ID | jq '.'
else
    echo "No se pudo obtener el ID de la categoría especial"
fi
echo

# 4. Intentar crear otra categoría con el mismo nombre (debería fallar)
echo "4. Intentando crear otra categoría 'Sin Categoría' (debería fallar)..."
curl -s -X POST http://localhost:8080/api/categorias \
  -H "Content-Type: application/json" \
  -d '{"nombre": "Sin Categoría", "descripcion": "Duplicada", "orden": 1}' | jq '.'
echo

# 5. Actualizar el orden de la categoría especial (debería funcionar)
echo "5. Actualizando orden de la categoría especial..."
if [ "$SIN_CATEGORIA_ID" != "null" ] && [ "$SIN_CATEGORIA_ID" != "" ]; then
    curl -s -X PUT http://localhost:8080/api/categorias/orden \
      -H "Content-Type: application/json" \
      -d "{\"categorias\": [{\"id\": $SIN_CATEGORIA_ID, \"orden\": 1}]}" | jq '.'
else
    echo "No se pudo actualizar porque no se obtuvo el ID"
fi
echo

# 6. Verificar que el orden se actualizó
echo "6. Verificando orden actualizado..."
curl -s http://localhost:8080/api/categorias/sin-categoria | jq '.orden'
echo

echo "=== Fin de las pruebas ==="
