# Funcionalidad de Descuentos en Ventas

## Resumen

Se ha implementado exitosamente la funcionalidad de descuentos en las ventas. Esta funcionalidad permite aplicar un porcentaje de descuento a nivel de venta que se aplica sobre el total de todos los items.

## Características Implementadas

### 1. **Base de Datos**
- **Nueva migración**: `V20__add_discount_to_ventas.sql`
- **Campo agregado**: `porcentaje_descuento` en tabla `ventas`
- **Tipo**: `DECIMAL(5,2)` (permite valores de 0.00 a 100.00)
- **Opcional**: Puede ser NULL (sin descuento)
- **Validación**: Constraint que asegura valores entre 0 y 100
- **Índice**: Para consultas eficientes por descuento

### 2. **Modelo de Dominio**
- **Sale**: Nuevo campo `porcentajeDescuento: BigDecimal?`
- **Validación**: El descuento debe estar entre 0 y 100
- **Cálculo**: Se aplica al total de la venta, no a items individuales
- **Factory methods**: Actualizados para manejar descuentos

### 3. **API**
- **SaleRequest**: Nuevo campo `porcentajeDescuento: Double?`
- **SaleResponse**: Nuevo campo `porcentajeDescuento: Double?`
- **Backward compatible**: El campo es opcional

## Uso de la API

### Crear una venta con descuento

```json
POST /api/sales
{
  "vendedor": "usuario1",
  "medioPago": "EFECTIVO",
  "porcentajeDescuento": 15.0,
  "items": [
    {
      "productoNombre": "Producto A",
      "cantidad": 2.0,
      "precioUnitario": 100.0,
      "tipoIvaId": 5,
      "unidadMedidaId": 1
    },
    {
      "productoNombre": "Producto B",
      "cantidad": 1.0,
      "precioUnitario": 50.0,
      "tipoIvaId": 5,
      "unidadMedidaId": 1
    }
  ],
  "imprimirTicket": false,
  "facturaOnline": false,
  "facturaOffline": false
}
```

### Respuesta

```json
{
  "id": 1,
  "numeroVenta": "V-20240703-001",
  "fechaVenta": "03/07/2024 14:30:00",
  "usuarioUsername": "usuario1",
  "usuarioNombre": "Usuario 1",
  "montoTotal": "212.50",
  "medioPago": "EFECTIVO",
  "porcentajeDescuento": 15.0,
  "comprobanteEmitido": false,
  "items": [...]
}
```

## Cálculo de Descuentos

### Ejemplo de Cálculo

**Items:**
- Producto A: 2 × $100.00 = $200.00
- Producto B: 1 × $50.00 = $50.00

**Con 15% de descuento aplicado a cada item:**
- Producto A con descuento: $200.00 - ($200.00 × 15%) = $200.00 - $30.00 = $170.00
- Producto B con descuento: $50.00 - ($50.00 × 15%) = $50.00 - $7.50 = $42.50
- **Total final**: $170.00 + $42.50 = $212.50

### Reglas de Negocio

1. **Rango válido**: 0% - 100%
2. **Aplicación**: Se aplica a cada item individual y se almacena en el subtotal
3. **Precisión**: 2 decimales en el resultado final
4. **Opcional**: Si no se especifica, no hay descuento
5. **Persistencia**: Se guarda tanto a nivel de venta como en cada item
6. **Comprobantes**: Los items en comprobantes fiscales muestran precios con descuento aplicado

## Casos de Uso

### 1. Venta sin descuento
```json
{
  "porcentajeDescuento": null
  // o simplemente omitir el campo
}
```

### 2. Descuento promocional
```json
{
  "porcentajeDescuento": 10.0  // 10% de descuento
}
```

### 3. Descuento máximo
```json
{
  "porcentajeDescuento": 100.0  // 100% de descuento (gratis)
}
```

### 4. Descuento mínimo
```json
{
  "porcentajeDescuento": 0.0  // Sin descuento efectivo
}
```

## Validaciones

### En el Frontend
- Validar que el valor esté entre 0 y 100
- Mostrar el total con descuento en tiempo real
- Permitir valores decimales (ej: 12.5%)

### En el Backend
- Validación automática en el modelo de dominio
- Error si el valor está fuera del rango válido
- Constraint de base de datos como respaldo

## Compatibilidad

### Backward Compatibility
- **APIs existentes**: Funcionan sin cambios
- **Base de datos**: Registros existentes tienen `porcentaje_descuento = NULL`
- **Respuestas**: El campo aparece como `null` en ventas sin descuento

### Migration Path
- La migración V20 se ejecuta automáticamente
- No requiere cambios en datos existentes
- Las aplicaciones cliente pueden adoptar gradualmente la nueva funcionalidad

## Testing

Se han implementado tests completos que cubren:

- ✅ Creación de ventas con descuento
- ✅ Creación de ventas sin descuento  
- ✅ Validación de rangos (0-100%)
- ✅ Cálculos correctos de totales
- ✅ Casos extremos (0%, 100%)
- ✅ Persistencia en base de datos
- ✅ Serialización/deserialización JSON

## Próximos Pasos Sugeridos

1. **Frontend**: Implementar UI para aplicar descuentos
2. **Reportes**: Incluir descuentos en reportes de ventas
3. **Auditoría**: Logs de quién aplica descuentos
4. **Límites**: Configurar límites máximos por usuario/rol
5. **Tipos**: Considerar diferentes tipos de descuento (fijo vs porcentual)

## Archivos Modificados

### Base de Datos
- `src/main/resources/db/migration/V20__add_discount_to_ventas.sql`

### Modelo de Dominio
- `src/main/kotlin/com/gnico/majo/application/domain/model/Sale.kt`

### DTOs
- `src/main/kotlin/com/gnico/majo/adapter/controller/dto/SaleDto.kt`

### Servicios
- `src/main/kotlin/com/gnico/majo/application/port/in/SaleService.kt`
- `src/main/kotlin/com/gnico/majo/application/usecase/SaleServiceImpl.kt`

### Controladores
- `src/main/kotlin/com/gnico/majo/adapter/controller/rest/SaleController.kt`

### Repositorios
- `src/main/kotlin/com/gnico/majo/adapter/persistence/JooqSaleRepository.kt`

### Tests
- `src/test/kotlin/com/gnico/majo/domain/model/SaleWithDiscountTest.kt`
- Actualizaciones en tests existentes para compatibilidad
