import java.util.Properties

val env = Properties().apply {
    val envFile = file(".env")
    if (envFile.exists()) {
        envFile.inputStream().use { load(it) }
    }
}

fun env(key: String): String = env[key] as String? ?: error("Missing env var: $key")

buildscript {
    dependencies {
        classpath(libs.flyway.postgresql)
    }
}

plugins {
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.ktor)
    alias(libs.plugins.kotlin.plugin.serialization)
    id("java")
    id("com.github.bjornvester.wsdl2java") version "2.0.2"
    alias(libs.plugins.flywaydb)
    alias(libs.plugins.jooq)

}

group = "com.gnico.majo"

kotlin {
    jvmToolchain(17)
}

java {
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(17))
    }
}

sourceSets {
    named("main") {
        java {
            srcDir(layout.buildDirectory.dir("generated/sources/wsdl2java"))
        }
    }
}

tasks.withType<JavaCompile> {
    options.encoding = "UTF-8"
}

application {
    mainClass.set("io.ktor.server.netty.EngineMain")

    val isDevelopment = project.findProperty("development") == "true"
    applicationDefaultJvmArgs = listOf("-Dio.ktor.development=$isDevelopment")
}

repositories {
    mavenCentral()
}

dependencies {
    // Ktor
    implementation(libs.ktor.server.core)
    implementation(libs.ktor.server.netty)
    implementation(libs.ktor.server.config.yaml)

    // Serialization and Content Negotiation
    implementation(libs.ktor.serialization.kotlinx.json)
    implementation(libs.ktor.server.content.negotiation)

    // CORS
    implementation(libs.ktor.server.cors)

    // Database
    implementation(libs.postgresql)
    implementation(libs.flyway.core)
    implementation(libs.flyway.postgresql)
    implementation(libs.hikaricp)
    implementation(libs.jooq)
    jooqCodegen(libs.jooq.codegen)
    jooqCodegen(libs.postgresql)

    // Logging
    implementation(libs.logback.classic)

    // Environment
    implementation("io.github.cdimascio:dotenv-kotlin:6.5.1")

    // Bouncy Castle (crypto)
    implementation(libs.bouncy.castle.bcprov)
    implementation(libs.bouncy.castle.bcpkix)

    implementation("com.sun.xml.ws:jaxws-rt:4.0.2")

    // Koin
    implementation(libs.koin.core)
    implementation(libs.koin.ktor)
    implementation(libs.koin.logger.slf4j)

    // Tests
    testImplementation(libs.ktor.server.test.host)
    testImplementation(libs.kotlin.test.junit)
    testImplementation(libs.koin.test)
    testImplementation(libs.koin.test.junit5)

    // Mockito
    testImplementation(libs.mockito.core)
    testImplementation(libs.mockito.kotlin)

    // ESC/POS Printer
    implementation("com.github.anastaciocintra:escpos-coffee:4.1.0")
}


tasks.withType<Test> {
    useJUnitPlatform()
}

wsdl2java {
    cxfVersion.set("4.0.2")
    verbose.set(true)
}

flyway {
    url = env("POSTGRES_DB_URL")
    user = env("POSTGRES_USER")
    password = env("POSTGRES_PASSWORD")
    locations = arrayOf("filesystem:src/main/resources/db/migration")
}

jooq {
    configuration {
        jdbc {
            driver   = "org.postgresql.Driver"
            url      = env("POSTGRES_DB_URL")
            user     = env("POSTGRES_USER")
            password = env("POSTGRES_PASSWORD")
        }

        generator {
            // Para generar código Kotlin en vez de Java (opcional)
            name = "org.jooq.codegen.KotlinGenerator"
            database {
                name         = "org.jooq.meta.postgres.PostgresDatabase"
                inputSchema  = "public"
                includes = ".*"
            }
            generate {
                isDeprecated = false
                isRecords = true       // Genera data classes
                isImmutablePojos = true
                isFluentSetters = true
            }
            target {
                packageName = "com.gnico.majo.jooq.generated"
                directory = "build/generated-jooq"
                //directory   = layout.buildDirectory.dir("generated-jooq").get().asFile.absolutePath
            }
        }
    }
}

tasks.named("jooqCodegen") {
    dependsOn("flywayMigrate")
}

kotlin {
    sourceSets.main {
        kotlin.srcDirs("src/main/kotlin", "build/generated-jooq")
    }
}
