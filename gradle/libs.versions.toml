
[versions]
kotlin-version = "2.1.20"
ktor-version = "3.1.2"
logback-version = "1.5.18"
bouncy-castle-version = "1.80"
flyway-version = "10.4.1"
postgres-version = "42.7.2"
jooq-version = "3.19.22"
hikaricp-version = "5.0.1"
koin-version = "4.0.3"
mockito-version = "5.12.0"
mockito-kotlin-version = "5.2.1"
# JUnit Jupiter ya está incluido en kotlin-test-junit

[libraries]
bouncy-castle-bcprov = { module = "org.bouncycastle:bcprov-jdk15to18", version.ref = "bouncy-castle-version" }
bouncy-castle-bcpkix = { module = "org.bouncycastle:bcpkix-jdk15to18", version.ref = "bouncy-castle-version" }
flyway-core = { module = "org.flywaydb:flyway-core", version.ref = "flyway-version" }
flyway-postgresql = { module = "org.flywaydb:flyway-database-postgresql", version.ref = "flyway-version" }
hikaricp = { module = "com.zaxxer:HikariCP", version.ref = "hikaricp-version" }
jooq = { module = "org.jooq:jooq", version.ref = "jooq-version" }
jooq-codegen = { module = "org.jooq:jooq-codegen", version.ref = "jooq-version" }
ktor-server-core = { module = "io.ktor:ktor-server-core-jvm", version.ref = "ktor-version" }
ktor-serialization-kotlinx-json = { module = "io.ktor:ktor-serialization-kotlinx-json-jvm", version.ref = "ktor-version" }
ktor-server-content-negotiation = { module = "io.ktor:ktor-server-content-negotiation-jvm", version.ref = "ktor-version" }
ktor-server-netty = { module = "io.ktor:ktor-server-netty", version.ref = "ktor-version" }
ktor-server-config-yaml = { module = "io.ktor:ktor-server-config-yaml", version.ref = "ktor-version" }
ktor-server-cors = { module = "io.ktor:ktor-server-cors", version.ref = "ktor-version" }
ktor-server-test-host = { module = "io.ktor:ktor-server-test-host", version.ref = "ktor-version" }
kotlin-test-junit = { module = "org.jetbrains.kotlin:kotlin-test-junit5", version.ref = "kotlin-version" }
logback-classic = { module = "ch.qos.logback:logback-classic", version.ref = "logback-version" }
postgresql = { module = "org.postgresql:postgresql", version.ref = "postgres-version" }
# Koin
koin-core = { module = "io.insert-koin:koin-core", version.ref = "koin-version" }
koin-ktor = { module = "io.insert-koin:koin-ktor", version.ref = "koin-version" }
koin-logger-slf4j = { module = "io.insert-koin:koin-logger-slf4j", version.ref = "koin-version" }
koin-test = { module = "io.insert-koin:koin-test", version.ref = "koin-version" }
koin-test-junit5 = { module = "io.insert-koin:koin-test-junit5", version.ref = "koin-version" }

# Testing
mockito-core = { module = "org.mockito:mockito-core", version.ref = "mockito-version" }
mockito-kotlin = { module = "org.mockito.kotlin:mockito-kotlin", version.ref = "mockito-kotlin-version" }
# JUnit Jupiter ya está incluido en kotlin-test-junit

[plugins]
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin-version" }
ktor = { id = "io.ktor.plugin", version.ref = "ktor-version" }
kotlin-plugin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin-version" }
flywaydb = { id = "org.flywaydb.flyway", version.ref = "flyway-version" }
jooq = { id = "org.jooq.jooq-codegen-gradle", version.ref = "jooq-version" }