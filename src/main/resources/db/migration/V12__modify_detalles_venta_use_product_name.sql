-- Migración para modificar detalles_venta para almacenar nombre del producto
-- en lugar del código, para preservar información histórica de ventas

-- 1. Eliminar la foreign key constraint que referencia productos
ALTER TABLE detalles_venta DROP CONSTRAINT IF EXISTS detalles_venta_producto_codigo_fkey;

-- 2. Agregar la nueva columna para el nombre del producto
ALTER TABLE detalles_venta ADD COLUMN producto_nombre VARCHAR(100) NOT NULL DEFAULT 'PRODUCTO_TEMPORAL';

-- 3. Eliminar la columna producto_codigo_ref ya que no la necesitamos más
ALTER TABLE detalles_venta DROP COLUMN IF EXISTS producto_codigo_ref;

-- 4. Agregar índice para mejorar consultas por nombre de producto
CREATE INDEX idx_detalles_venta_producto_nombre ON detalles_venta(producto_nombre);

-- 5. Agregar comentario para documentar el cambio
COMMENT ON COLUMN detalles_venta.producto_nombre IS 'Nombre del producto al momento de la venta para preservar información histórica';
