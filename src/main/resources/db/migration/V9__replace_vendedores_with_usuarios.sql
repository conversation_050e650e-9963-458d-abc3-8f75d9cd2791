-- Migración V9: Reemplazar tabla vendedores con tabla usuarios
-- Fecha: 2025-06-15

-- 1. Eliminar el índice relacionado con vendedor_id
DROP INDEX IF EXISTS idx_ventas_vendedor_id;

-- 2. Eliminar la restricción de clave foránea en la tabla ventas
ALTER TABLE ventas DROP CONSTRAINT IF EXISTS ventas_vendedor_id_fkey;

-- 3. Cambiar el tipo de la columna vendedor_id a VARCHAR para referenciar username
ALTER TABLE ventas ALTER COLUMN vendedor_id TYPE VARCHAR(50);

-- 4. Reno<PERSON>rar la columna para mayor claridad
ALTER TABLE ventas RENAME COLUMN vendedor_id TO usuario_username;

-- 5. Eliminar la tabla vendedores
DROP TABLE IF EXISTS vendedores;

-- 6. Crear la nueva tabla usuarios con username como clave primaria
CREATE TABLE usuarios (
    username VARCHAR(50) PRIMARY KEY,
    nombre VARCHAR(100) NOT NULL,
    nombre_display VARCHAR(100) NOT NULL,
    activo BOOLEAN NOT NULL DEFAULT TRUE,
    creado_en TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    actualizado_en TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 7. Crear índices para optimizar consultas
CREATE INDEX idx_usuarios_activo ON usuarios(activo);

-- 8. Crear la nueva restricción de clave foránea
ALTER TABLE ventas
ADD CONSTRAINT ventas_usuario_username_fkey
FOREIGN KEY (usuario_username) REFERENCES usuarios(username);

-- 9. Crear índice para la nueva referencia
CREATE INDEX idx_ventas_usuario_username ON ventas(usuario_username);

-- 10. Comentarios para documentación
COMMENT ON TABLE usuarios IS 'Tabla de usuarios del sistema';
COMMENT ON COLUMN usuarios.username IS 'Nombre de usuario único para login (clave primaria)';
COMMENT ON COLUMN usuarios.nombre IS 'Nombre completo del usuario';
COMMENT ON COLUMN usuarios.nombre_display IS 'Nombre para mostrar en la interfaz';
COMMENT ON COLUMN usuarios.activo IS 'Indica si el usuario está activo en el sistema';
