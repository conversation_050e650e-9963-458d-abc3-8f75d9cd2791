-- Migración para agregar funcionalidad de cancelación de ventas
-- Agregar campos para rastrear cancelaciones y permitir auditoría completa

-- Agregar campos de cancelación a la tabla ventas
ALTER TABLE ventas ADD COLUMN cancelada BOOLEAN NOT NULL DEFAULT FALSE;
ALTER TABLE ventas ADD COLUMN fecha_cancelacion TIMESTAMP NULL;
ALTER TABLE ventas ADD COLUMN usuario_cancelacion VARCHAR(50) NULL;
ALTER TABLE ventas ADD COLUMN motivo_cancelacion TEXT NULL;

-- Agregar constraint para asegurar consistencia de datos de cancelación
ALTER TABLE ventas ADD CONSTRAINT chk_cancelacion_consistente 
    CHECK (
        (cancelada = FALSE AND fecha_cancelacion IS NULL AND usuario_cancelacion IS NULL AND motivo_cancelacion IS NULL) OR
        (cancelada = TRUE AND fecha_cancelacion IS NOT NULL AND usuario_cancelacion IS NOT NULL AND motivo_cancelacion IS NOT NULL)
    );

-- Agregar foreign key para usuario_cancelacion
ALTER TABLE ventas ADD CONSTRAINT fk_ventas_usuario_cancelacion 
    FOREIGN KEY (usuario_cancelacion) REFERENCES usuarios(username);

-- Crear índices para mejorar rendimiento en consultas
CREATE INDEX idx_ventas_cancelada ON ventas(cancelada);
CREATE INDEX idx_ventas_fecha_cancelacion ON ventas(fecha_cancelacion) WHERE fecha_cancelacion IS NOT NULL;
CREATE INDEX idx_ventas_usuario_cancelacion ON ventas(usuario_cancelacion) WHERE usuario_cancelacion IS NOT NULL;

-- Comentarios para documentación
COMMENT ON COLUMN ventas.cancelada IS 'Indica si la venta ha sido cancelada';
COMMENT ON COLUMN ventas.fecha_cancelacion IS 'Fecha y hora de cancelación de la venta';
COMMENT ON COLUMN ventas.usuario_cancelacion IS 'Usuario que canceló la venta';
COMMENT ON COLUMN ventas.motivo_cancelacion IS 'Motivo de la cancelación de la venta';
