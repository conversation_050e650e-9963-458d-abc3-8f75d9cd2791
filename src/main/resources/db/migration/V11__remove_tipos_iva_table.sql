-- Migración para eliminar la tabla tipos_iva y sus foreign keys
-- Los tipos de IVA ahora se manejan como enum en el código

-- 1. Eliminar foreign key constraints que referencian tipos_iva
ALTER TABLE productos DROP CONSTRAINT IF EXISTS productos_tipo_iva_id_fkey;
ALTER TABLE productos DROP CONSTRAINT IF EXISTS productos_temp_tipo_iva_id_fkey;
ALTER TABLE detalles_venta DROP CONSTRAINT IF EXISTS detalles_venta_tipo_iva_id_fkey;

-- 2. Eliminar la tabla tipos_iva
DROP TABLE IF EXISTS tipos_iva;

-- 3. Agregar comentario a la columna tipo_iva_id para documentar los valores válidos
COMMENT ON COLUMN productos.tipo_iva_id IS 'ID del tipo de IVA (enum): 3=0%, 4=10.5%, 5=21%, 6=27%, 8=5%, 9=2.5%';
COMMENT ON COLUMN detalles_venta.tipo_iva_id IS 'ID del tipo de IVA (enum): 3=0%, 4=10.5%, 5=21%, 6=27%, 8=5%, 9=2.5%';
