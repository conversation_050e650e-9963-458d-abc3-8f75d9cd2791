-- Migración para modificar la tabla unidades_medida:
-- 1. Cambiar id de SERIAL a INTEGER (permitiendo 0)
-- 2. Renombrar nombre_corto a nombre
-- 3. Descartar datos existentes
-- 4. Inicializar con datos específicos

-- <PERSON><PERSON>, eliminar las foreign key constraints que referencian esta tabla
-- Verificar el nombre exacto de la constraint y eliminarla
ALTER TABLE productos DROP CONSTRAINT IF EXISTS productos_unidad_medida_id_fkey;
ALTER TABLE productos DROP CONSTRAINT IF EXISTS productos_temp_unidad_medida_id_fkey;

-- Crear una tabla temporal con la nueva estructura
CREATE TABLE unidades_medida_temp (
    id INTEGER PRIMARY KEY,
    codigo VARCHAR(10) NOT NULL UNIQUE,
    nombre VARCHAR(50) NOT NULL,
    descripcion VARCHAR(100)
);

-- Insertar los datos iniciales
INSERT INTO unidades_medida_temp (id, codigo, nombre, descripcion) VALUES
(0, 'mx', 'un/kg', 'mixto'),
(1, 'un', 'un', 'unidad'),
(2, 'kg', 'kg', 'kilogramo');

-- Eliminar la tabla original
DROP TABLE unidades_medida;

-- Renombrar la tabla temporal
ALTER TABLE unidades_medida_temp RENAME TO unidades_medida;

-- Recrear la foreign key constraint
ALTER TABLE productos
ADD CONSTRAINT productos_unidad_medida_id_fkey
FOREIGN KEY (unidad_medida_id) REFERENCES unidades_medida(id);

-- Crear índices
CREATE INDEX idx_unidades_medida_codigo ON unidades_medida(codigo);
