-- Corregir la restricción de unicidad en wsaa_credenciales
-- Solo debe permitir una credencial ACTIVA por servicio, pero múltiples EXPIRADAS

-- 1. Eliminar la restricción actual que es incorrecta
ALTER TABLE wsaa_credenciales DROP CONSTRAINT unique_servicio_activo;

-- 2. Crear una restricción parcial que solo aplique para credenciales ACTIVAS
-- Esto permite múltiples credenciales EXPIRADAS por servicio pero solo una ACTIVA
CREATE UNIQUE INDEX unique_servicio_activo_only 
ON wsaa_credenciales (servicio) 
WHERE estado = 'ACTIVO';
