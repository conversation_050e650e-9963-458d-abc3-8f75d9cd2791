-- Migración para agregar funcionalidad de descuentos a las ventas
-- Agregar campo porcentaje_descuento a la tabla ventas

-- Agregar campo para porcentaje de descuento (opcional)
ALTER TABLE ventas ADD COLUMN porcentaje_descuento DECIMAL(5,2) DEFAULT NULL;

-- Agregar constraint para asegurar que el descuento esté entre 0 y 100
ALTER TABLE ventas ADD CONSTRAINT chk_porcentaje_descuento_valido 
    CHECK (porcentaje_descuento IS NULL OR (porcentaje_descuento >= 0 AND porcentaje_descuento <= 100));

-- Agregar índice para consultas por descuento
CREATE INDEX idx_ventas_porcentaje_descuento ON ventas(porcentaje_descuento) WHERE porcentaje_descuento IS NOT NULL;

-- Comentario para documentación
COMMENT ON COLUMN ventas.porcentaje_descuento IS 'Porcentaje de descuento aplicado a la venta (0-100). NULL si no hay descuento';
