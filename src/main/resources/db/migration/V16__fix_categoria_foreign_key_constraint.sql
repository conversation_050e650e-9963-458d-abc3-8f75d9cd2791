-- Migración para corregir el nombre de la foreign key constraint de categorías
-- El problema es que la constraint se llama productos_temp_categoria_id_fkey desde la migración V3

-- Eliminar ambas restricciones de foreign key que puedan existir
ALTER TABLE productos DROP CONSTRAINT IF EXISTS productos_temp_categoria_id_fkey;
ALTER TABLE productos DROP CONSTRAINT IF EXISTS productos_categoria_id_fkey;

-- <PERSON><PERSON>r todas las referencias de categorías en productos (establecer a NULL)
-- Esto es necesario para permitir la eliminación física de categorías
UPDATE productos SET categoria_id = NULL WHERE categoria_id IS NOT NULL;

-- Recrear la restricción de foreign key con ON DELETE SET NULL y nombre correcto
ALTER TABLE productos
ADD CONSTRAINT productos_categoria_id_fkey
FOREIGN KEY (categoria_id) REFERENCES categorias(id) ON DELETE SET NULL;

-- Comentario para documentación
COMMENT ON CONSTRAINT productos_categoria_id_fkey ON productos IS 'Foreign key con ON DELETE SET NULL para limpiar referencias automáticamente cuando se elimina una categoría';
