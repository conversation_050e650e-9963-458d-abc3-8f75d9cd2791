-- Migración para cambiar la tabla productos:
-- 1. <PERSON>uitar el campo id
-- 2. Cambiar codigo de VARCHAR a INTEGER de 4 dígitos
-- 3. <PERSON><PERSON> codigo la nueva clave primaria

-- <PERSON><PERSON>, necesitamos actualizar las referencias en detalles_venta
-- Agregar una columna temporal para el nuevo codigo
ALTER TABLE detalles_venta ADD COLUMN producto_codigo INTEGER;

-- Actualizar la columna temporal con los códigos de productos
-- Asumiendo que los códigos actuales son numéricos o pueden convertirse
UPDATE detalles_venta 
SET producto_codigo = CAST(p.codigo AS INTEGER)
FROM productos p 
WHERE detalles_venta.producto_id = p.id;

-- Crear una tabla temporal para productos con la nueva estructura
CREATE TABLE productos_temp (
    codigo INTEGER PRIMARY KEY,
    nombre VARCHAR(100) NOT NULL,
    descripcion TEXT,
    unidad_medida_id INTEGER NOT NULL REFERENCES unidades_medida(id),
    tipo_iva_id INTEGER NOT NULL REFERENCES tipos_iva(id),
    categoria_id INTEGER REFERENCES categorias(id),
    precio_unitario DECIMAL(15, 2),
    stock_actual INTEGER,
    activo BOOLEAN NOT NULL DEFAULT TRUE,
    creado_en TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    actualizado_en TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Migrar datos existentes a la tabla temporal
-- Solo migrar productos que tengan códigos que puedan convertirse a enteros de 4 dígitos
INSERT INTO productos_temp (
    codigo, nombre, descripcion, unidad_medida_id, tipo_iva_id, 
    categoria_id, precio_unitario, stock_actual, activo, creado_en, actualizado_en
)
SELECT 
    CASE 
        WHEN codigo ~ '^[0-9]+$' AND LENGTH(codigo) <= 4 THEN CAST(codigo AS INTEGER)
        WHEN codigo = 'VARIOS' THEN 9999  -- Código especial para VARIOS
        ELSE NULL
    END as codigo,
    nombre, descripcion, unidad_medida_id, tipo_iva_id,
    categoria_id, precio_unitario, stock_actual, activo, creado_en, actualizado_en
FROM productos 
WHERE (codigo ~ '^[0-9]+$' AND LENGTH(codigo) <= 4) OR codigo = 'VARIOS';

-- Eliminar la foreign key constraint de detalles_venta
ALTER TABLE detalles_venta DROP CONSTRAINT detalles_venta_producto_id_fkey;

-- Eliminar la columna producto_id de detalles_venta
ALTER TABLE detalles_venta DROP COLUMN producto_id;

-- Renombrar la columna temporal
ALTER TABLE detalles_venta RENAME COLUMN producto_codigo TO producto_codigo_ref;

-- Eliminar la tabla productos original
DROP TABLE productos;

-- Renombrar la tabla temporal
ALTER TABLE productos_temp RENAME TO productos;

-- Agregar la nueva foreign key constraint
ALTER TABLE detalles_venta 
ADD CONSTRAINT detalles_venta_producto_codigo_fkey 
FOREIGN KEY (producto_codigo_ref) REFERENCES productos(codigo);

-- Recrear índices
CREATE INDEX idx_productos_unidad_medida_id ON productos(unidad_medida_id);
CREATE INDEX idx_productos_tipo_iva_id ON productos(tipo_iva_id);
CREATE INDEX idx_productos_categoria_id ON productos(categoria_id);

-- Agregar constraint para asegurar que el código sea de 4 dígitos máximo
ALTER TABLE productos ADD CONSTRAINT chk_codigo_4_digits CHECK (codigo >= 0 AND codigo <= 9999);
