-- Migración para agregar campo de seguimiento de nota de crédito
-- Permite rastrear si se ha generado exitosamente la nota de crédito para ventas canceladas

-- Agregar campo para rastrear si se generó la nota de crédito
ALTER TABLE ventas ADD COLUMN nota_credito_generada BOOLEAN NOT NULL DEFAULT FALSE;

-- Actualizar el campo basándose en ventas canceladas que ya tienen nota de crédito
UPDATE ventas 
SET nota_credito_generada = TRUE 
WHERE cancelada = TRUE 
  AND comprobante_emitido = TRUE 
  AND id IN (
    SELECT DISTINCT v.id 
    FROM ventas v
    INNER JOIN comprobantes c ON v.id = c.venta_id
    WHERE c.tipo_comprobante = 'NOTA_CREDITO_B'
      AND c.estado = 'EMITIDO'
      AND v.cancelada = TRUE
  );

-- Agregar constraint para asegurar consistencia lógica
-- Solo las ventas canceladas con comprobante pueden tener nota de crédito generada
ALTER TABLE ventas ADD CONSTRAINT chk_nota_credito_consistente 
    CHECK (
        (nota_credito_generada = FALSE) OR
        (nota_credito_generada = TRUE AND cancelada = TRUE AND comprobante_emitido = TRUE)
    );

-- Crear índice para mejorar rendimiento en consultas de ventas pendientes de nota de crédito
CREATE INDEX idx_ventas_pendientes_nota_credito ON ventas(cancelada, comprobante_emitido, nota_credito_generada) 
    WHERE cancelada = TRUE AND comprobante_emitido = TRUE AND nota_credito_generada = FALSE;

-- Comentario para documentación
COMMENT ON COLUMN ventas.nota_credito_generada IS 'Indica si se ha generado exitosamente la nota de crédito para esta venta cancelada';
