-- Migración para agregar el estado 'INFORMADO' a la tabla caea_codes
-- Esto permite marcar CAEAs como informados a AFIP después de reportarlos

-- Eliminar la restricción CHECK existente
ALTER TABLE caea_codes DROP CONSTRAINT IF EXISTS check_estado;

-- Agregar la nueva restricción CHECK que incluye 'INFORMADO'
ALTER TABLE caea_codes ADD CONSTRAINT check_estado 
    CHECK (estado IN ('ACTIVO', 'AGOTADO', 'VENCIDO', 'INFORMADO'));

-- Comentario actualizado para documentación
COMMENT ON COLUMN caea_codes.estado IS 'ACTIVO: disponible para usar, AGOTADO: numeración agotada, VENCIDO: fuera del período de validez, INFORMADO: ya fue reportado a AFIP';
