-- Migración para aumentar el tamaño del campo tipo_comprobante
-- Los tipos de comprobante como "NOTA_CREDITO_B" y "NOTA_DEBITO_B" requieren más de 10 caracteres

-- Aumentar el tamaño del campo tipo_comprobante
ALTER TABLE comprobantes ALTER COLUMN tipo_comprobante TYPE VARCHAR(20);

-- Comentario para documentación
COMMENT ON COLUMN comprobantes.tipo_comprobante IS 'Tipo de comprobante: FACTURA_B, NOTA_CREDITO_B, NOTA_DEBITO_B';
