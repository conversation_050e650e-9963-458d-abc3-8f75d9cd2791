-- Migración para modificar la tabla ventas
-- Quitar el campo "estado" y reemplazarlo por un boolean que indique si se realizó un comprobante
-- Agregar campos para medio de pago y tickets de balanza

-- Primero agregar los nuevos campos
ALTER TABLE ventas ADD COLUMN comprobante_emitido BOOLEAN NOT NULL DEFAULT FALSE;
ALTER TABLE ventas ADD COLUMN medio_pago VARCHAR(50) NOT NULL DEFAULT 'EFECTIVO';
ALTER TABLE ventas ADD COLUMN codigo_ticket_balanza VARCHAR(255);
ALTER TABLE ventas ADD COLUMN id_ticket_balanza VARCHAR(255);

-- Actualizar comprobante_emitido basándose en si existe un comprobante para la venta
UPDATE ventas 
SET comprobante_emitido = TRUE 
WHERE id IN (
    SELECT DISTINCT venta_id 
    FROM comprobantes 
    WHERE estado = 'EMITIDO'
);

-- Eliminar el campo estado
ALTER TABLE ventas DROP COLUMN estado;

-- Crear índices para mejorar rendimiento en consultas
CREATE INDEX idx_ventas_comprobante_emitido ON ventas(comprobante_emitido);
CREATE INDEX idx_ventas_medio_pago ON ventas(medio_pago);
CREATE INDEX idx_ventas_codigo_ticket_balanza ON ventas(codigo_ticket_balanza) WHERE codigo_ticket_balanza IS NOT NULL;
CREATE INDEX idx_ventas_id_ticket_balanza ON ventas(id_ticket_balanza) WHERE id_ticket_balanza IS NOT NULL;
