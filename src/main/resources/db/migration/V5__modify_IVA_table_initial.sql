-- Limpiar datos en orden correcto para evitar violaciones de foreign key

-- 1. Primero eliminar datos de detalles_venta (referencia tipos_iva)
DELETE FROM detalles_venta;

-- 2. Temporalmente deshabilitar las foreign key constraints de productos
ALTER TABLE productos DROP CONSTRAINT IF EXISTS productos_temp_tipo_iva_id_fkey;
ALTER TABLE productos DROP CONSTRAINT IF EXISTS productos_tipo_iva_id_fkey;

-- 3. Luego eliminar datos de productos
DELETE FROM productos;

-- 4. Ahora podemos eliminar datos de tipos_iva
DELETE FROM tipos_iva;

-- 5. Reiniciar secuencias
ALTER SEQUENCE tipos_iva_id_seq RESTART WITH 1;
-- Nota: productos ya no usa secuencia SERIAL, usa INTEGER como PK

-- 6. Insertar nuevos datos para tipos_iva con IDs específicos
INSERT INTO tipos_iva (id, porcentaje, descripcion) VALUES
(3, 0.00, 'Exento'),
(4, 10.50, '10.5%'),
(5, 21.00, '21%'),
(6, 27.00, '27%'),
(8, 5.00, '5%'),
(9, 2.50, '2.5%');

-- 7. Actualizar la secuencia para que el próximo ID sea mayor que el máximo insertado
SELECT setval('tipos_iva_id_seq', (SELECT MAX(id) FROM tipos_iva));

-- 8. Recrear la foreign key constraint de productos
ALTER TABLE productos
ADD CONSTRAINT productos_tipo_iva_id_fkey
FOREIGN KEY (tipo_iva_id) REFERENCES tipos_iva(id);