-- Migración para cambiar el comportamiento de eliminación de categorías
-- Cambiar de soft delete a hard delete y limpiar referencias en productos

-- Primero, eliminar la restricción de foreign key existente
ALTER TABLE productos DROP CONSTRAINT IF EXISTS productos_categoria_id_fkey;

-- <PERSON><PERSON><PERSON> todas las referencias de categorías en productos (establecer a NULL)
-- Esto es necesario para permitir la eliminación física de categorías
UPDATE productos SET categoria_id = NULL WHERE categoria_id IS NOT NULL;

-- Recrear la restricción de foreign key con ON DELETE SET NULL
-- Esto asegura que cuando se elimine una categoría, las referencias en productos se establezcan a NULL automáticamente
ALTER TABLE productos 
ADD CONSTRAINT productos_categoria_id_fkey 
FOREIGN KEY (categoria_id) REFERENCES categorias(id) ON DELETE SET NULL;

-- Comentario para documentación
COMMENT ON CONSTRAINT productos_categoria_id_fkey ON productos IS 'Foreign key con ON DELETE SET NULL para limpiar referencias automáticamente cuando se elimina una categoría';
