-- Migración para corregir la restricción de unicidad de comprobantes
-- AFIP maneja numeración independiente por tipo de comprobante
-- La restricción debe incluir tipo_comprobante además de punto_venta y numero_comprobante

-- Eliminar la restricción actual
ALTER TABLE comprobantes DROP CONSTRAINT unique_comprobante;

-- Crear nueva restricción que incluye tipo_comprobante
ALTER TABLE comprobantes ADD CONSTRAINT unique_comprobante_por_tipo 
    UNIQUE (punto_venta, numero_comprobante, tipo_comprobante);

-- Comentario para documentación
COMMENT ON CONSTRAINT unique_comprobante_por_tipo ON comprobantes IS 
    'Garantiza unicidad por punto de venta, número y tipo de comprobante. AFIP maneja numeración independiente por tipo.';

-- Crear índice para optimizar consultas por punto de venta y número
CREATE INDEX idx_comprobantes_punto_venta_numero ON comprobantes(punto_venta, numero_comprobante);

-- Comentario adicional en la tabla
COMMENT ON TABLE comprobantes IS 
    'Comprobantes fiscales de AFIP. La numeración es independiente por tipo de comprobante según las reglas de AFIP.';
