-- Migración para simplificar el sistema a solo comprobantes tipo B
-- Agregar campo tipo_autorizacion para distinguir entre CAE y CAEA
-- Hacer cliente_id nullable en ventas ya que solo manejamos consumidor final

-- Agregar campo tipo_autorizacion a comprobantes
ALTER TABLE comprobantes ADD COLUMN tipo_autorizacion VARCHAR(20) NOT NULL DEFAULT 'CAE';

-- Actualizar registros existentes basándose en la tabla comprobante_attempts
UPDATE comprobantes 
SET tipo_autorizacion = (
    SELECT CASE 
        WHEN ca.tipo_operacion = 'CAE_ONLINE' THEN 'CAE'
        WHEN ca.tipo_operacion = 'CAEA_OFFLINE' THEN 'CAEA'
        ELSE 'CAE'
    END
    FROM comprobante_attempts ca 
    WHERE ca.comprobante_id = comprobantes.id 
    AND ca.estado = 'EXITOSO'
    LIMIT 1
);

-- Hacer cliente_id nullable en ventas (para futuras ventas sin cliente)
ALTER TABLE ventas ALTER COLUMN cliente_id DROP NOT NULL;

-- Comentarios para documentación
COMMENT ON COLUMN comprobantes.tipo_autorizacion IS 'Tipo de autorización: CAE (online) o CAEA (offline)';
COMMENT ON COLUMN ventas.cliente_id IS 'Cliente asociado a la venta. NULL para ventas a consumidor final (tipo B)';

-- Índice para mejorar consultas por tipo de autorización
CREATE INDEX idx_comprobantes_tipo_autorizacion ON comprobantes(tipo_autorizacion);
