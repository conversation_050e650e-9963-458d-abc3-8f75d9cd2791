-- Agregar campos para tracking de informes CAEA
-- V23__add_caea_tracking_fields.sql

-- Agregar campos para rastrear si un CAEA fue informado a AFIP
ALTER TABLE caea_codes 
ADD COLUMN informado BOOLEAN NOT NULL DEFAULT FALSE,
ADD COLUMN tipo_informe VARCHAR(20) NULL, -- 'SIN_MOVIMIENTO' o 'CON_MOVIMIENTOS'
ADD COLUMN fecha_informe TIMESTAMP NULL; -- Fecha cuando se informó a AFIP

-- Agregar campo para almacenar el CAEA utilizado en comprobantes
-- Esto permitirá rastrear qué comprobantes fueron emitidos con cada CAEA
ALTER TABLE comprobantes 
ADD COLUMN caea_utilizado VARCHAR(14) NULL; -- C<PERSON>digo CAEA de 14 dígitos

-- Crear índice para mejorar consultas de comprobantes por CAEA
CREATE INDEX idx_comprobantes_caea_utilizado ON comprobantes(caea_utilizado);

-- Comentarios para documentación
COMMENT ON COLUMN caea_codes.informado IS 'Indica si el CAEA fue informado a AFIP';
COMMENT ON COLUMN caea_codes.tipo_informe IS 'Tipo de informe realizado: SIN_MOVIMIENTO o CON_MOVIMIENTOS';
COMMENT ON COLUMN caea_codes.fecha_informe IS 'Fecha y hora cuando se informó el CAEA a AFIP';
COMMENT ON COLUMN comprobantes.caea_utilizado IS 'Código CAEA utilizado para generar este comprobante (solo para comprobantes offline)';
