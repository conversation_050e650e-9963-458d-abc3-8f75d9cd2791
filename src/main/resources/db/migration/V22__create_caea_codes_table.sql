-- Migración para crear tabla de códigos CAEA (Código de Autorización Electrónico Anticipado)
-- Los CAEA se obtienen de AFIP con anticipación para usar en modo offline

CREATE TABLE caea_codes (
    id SERIAL PRIMARY KEY,
    caea VARCHAR(50) NOT NULL UNIQUE, -- Código CAEA obtenido de AFIP
    punto_venta INTEGER NOT NULL, -- Punto de venta para el cual es válido el CAEA
    periodo VARCHAR(10) NOT NULL, -- Per<PERSON><PERSON> de validez (YYYYMM)
    fecha_desde DATE NOT NULL, -- Fecha desde la cual es válido
    fecha_hasta DATE NOT NULL, -- Fecha hasta la cual es válido
    orden INTEGER NOT NULL, -- Orden del CAEA (1 o 2 por período)
    estado VARCHAR(20) NOT NULL DEFAULT 'ACTIVO', -- ACTIVO, AGOTADO, VENCIDO
    
    -- Contadores para numeración secuencial
    ultimo_numero_factura_b BIGINT NOT NULL DEFAULT 0,
    ultimo_numero_nota_credito_b BIGINT NOT NULL DEFAULT 0,
    ultimo_numero_nota_debito_b BIGINT NOT NULL DEFAULT 0,
    
    -- Metadatos
    creado_en TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    actualizado_en TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT unique_caea_punto_venta_periodo_orden UNIQUE (punto_venta, periodo, orden),
    CONSTRAINT check_estado CHECK (estado IN ('ACTIVO', 'AGOTADO', 'VENCIDO')),
    CONSTRAINT check_orden CHECK (orden IN (1, 2)),
    CONSTRAINT check_fechas CHECK (fecha_hasta >= fecha_desde)
);

-- Índices para optimizar consultas
CREATE INDEX idx_caea_codes_punto_venta_estado ON caea_codes(punto_venta, estado);
CREATE INDEX idx_caea_codes_periodo_estado ON caea_codes(periodo, estado);
CREATE INDEX idx_caea_codes_fechas ON caea_codes(fecha_desde, fecha_hasta);

-- Comentarios para documentación
COMMENT ON TABLE caea_codes IS 'Códigos CAEA para facturación offline. Los CAEA se obtienen de AFIP con anticipación y permiten generar comprobantes sin conexión';
COMMENT ON COLUMN caea_codes.caea IS 'Código CAEA de 14 dígitos obtenido de AFIP';
COMMENT ON COLUMN caea_codes.punto_venta IS 'Punto de venta offline (diferente al punto de venta online)';
COMMENT ON COLUMN caea_codes.periodo IS 'Período de validez en formato YYYYMM (ej: 202507)';
COMMENT ON COLUMN caea_codes.orden IS 'Orden del CAEA: 1 para el primer CAEA del período, 2 para el segundo';
COMMENT ON COLUMN caea_codes.estado IS 'ACTIVO: disponible para usar, AGOTADO: numeración agotada, VENCIDO: fuera del período de validez';
COMMENT ON COLUMN caea_codes.ultimo_numero_factura_b IS 'Último número de factura B generado con este CAEA';
COMMENT ON COLUMN caea_codes.ultimo_numero_nota_credito_b IS 'Último número de nota de crédito B generado con este CAEA';
COMMENT ON COLUMN caea_codes.ultimo_numero_nota_debito_b IS 'Último número de nota de débito B generado con este CAEA';

-- Trigger para actualizar timestamp
CREATE OR REPLACE FUNCTION update_caea_codes_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.actualizado_en = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_update_caea_codes_updated_at
    BEFORE UPDATE ON caea_codes
    FOR EACH ROW
    EXECUTE FUNCTION update_caea_codes_updated_at();

-- Ejemplo de inserción manual de CAEA (para testing)
-- INSERT INTO caea_codes (caea, punto_venta, periodo, fecha_desde, fecha_hasta, orden)
-- VALUES ('21234567890123', 2, '202507', '2025-07-01', '2025-07-31', 1);
