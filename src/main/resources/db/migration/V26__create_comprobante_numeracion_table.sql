-- Tabla para trackear la numeración de comprobantes por punto de venta y tipo
-- Similar a caea_codes pero para CAE y numeración general

CREATE TABLE comprobante_numeracion (
    id SERIAL PRIMARY KEY,
    punto_venta INTEGER NOT NULL,
    tipo_comprobante VARCHAR(20) NOT NULL,
    ultimo_numero INTEGER NOT NULL DEFAULT 0,
    creado_en TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    actualizado_en TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Restricción de unicidad por punto de venta y tipo
    CONSTRAINT unique_numeracion_por_tipo UNIQUE (punto_venta, tipo_comprobante)
);

-- Índice para optimizar consultas
CREATE INDEX idx_comprobante_numeracion_lookup ON comprobante_numeracion(punto_venta, tipo_comprobante);

-- Comentarios para documentación
COMMENT ON TABLE comprobante_numeracion IS 
    'Trackea el último número de comprobante emitido por punto de venta y tipo. Evita consultas a AFIP y conflictos de numeración.';

COMMENT ON COLUMN comprobante_numeracion.punto_venta IS 
    'Punto de venta (debe coincidir con configuración .env)';

COMMENT ON COLUMN comprobante_numeracion.tipo_comprobante IS 
    'Tipo de comprobante: FACTURA_B, NOTA_CREDITO_B, NOTA_DEBITO_B';

COMMENT ON COLUMN comprobante_numeracion.ultimo_numero IS 
    'Último número de comprobante emitido para este punto de venta y tipo';

-- Inicializar con datos existentes si los hay
-- Obtener el máximo número por punto de venta y tipo de los comprobantes existentes
INSERT INTO comprobante_numeracion (punto_venta, tipo_comprobante, ultimo_numero)
SELECT 
    punto_venta,
    tipo_comprobante,
    COALESCE(MAX(numero_comprobante), 0) as ultimo_numero
FROM comprobantes 
WHERE numero_comprobante > 0  -- Excluir números temporales negativos
GROUP BY punto_venta, tipo_comprobante
ON CONFLICT (punto_venta, tipo_comprobante) DO NOTHING;

-- Trigger para actualizar timestamp automáticamente
CREATE OR REPLACE FUNCTION update_comprobante_numeracion_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.actualizado_en = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_comprobante_numeracion_timestamp
    BEFORE UPDATE ON comprobante_numeracion
    FOR EACH ROW
    EXECUTE FUNCTION update_comprobante_numeracion_timestamp();
