DROP TABLE users;

-- Tabla para tipos de IVA
CREATE TABLE tipos_iva (
    id SERIAL PRIMARY KEY,
    porcentaje DECIMAL(5, 2) NOT NULL UNIQUE, -- Ej. 0.00, 10.50, 21.00
    descripcion VARCHAR(50) NOT NULL
);


-- Tabla para unidades de medida
CREATE TABLE unidades_medida (
    id SERIAL PRIMARY KEY,
    codigo VARCHAR(10) NOT NULL UNIQUE, -- C<PERSON><PERSON> según AFIP (ej. 'UN', 'KG', 'LT')
    nombre_corto VARCHAR(10) NOT NULL, -- Nombre corto para mostrar
    descripcion VARCHAR(50) NOT NULL
);

-- Tabla para categorías de productos
CREATE TABLE categorias (
    id SERIAL PRIMARY KEY,
    nombre VARCHAR(50) NOT NULL UNIQUE,
    descripcion TEXT,
    activo BOOLEAN NOT NULL DEFAULT TRUE,
    creado_en TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Tabla para productos
CREATE TABLE productos (
    id SERIAL PRIMARY KEY,
    codigo VARCHAR(50) NOT NULL UNIQUE, -- Código interno o SKU
    nombre VARCHAR(100) NOT NULL,
    descripcion TEXT,
    unidad_medida_id INTEGER NOT NULL REFERENCES unidades_medida(id),
    tipo_iva_id INTEGER NOT NULL REFERENCES tipos_iva(id), -- Tipo de IVA del producto
    categoria_id INTEGER REFERENCES categorias(id), -- Categoría opcional
    precio_unitario DECIMAL(15, 2), -- Precio con IVA, opcional para productos como VARIOS
    stock_actual INTEGER, -- NULL si no se controla stock
    activo BOOLEAN NOT NULL DEFAULT TRUE,
    creado_en TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    actualizado_en TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Tabla para vendedores
CREATE TABLE vendedores (
    id SERIAL PRIMARY KEY,
    legajo VARCHAR(20),
    nombre VARCHAR(100) NOT NULL,
    activo BOOLEAN NOT NULL DEFAULT TRUE,
    creado_en TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Tabla para clientes
CREATE TABLE clientes (
    id SERIAL PRIMARY KEY,
    cuit VARCHAR(20), -- CUIT o DNI, obligatorio para Factura A
    razon_social VARCHAR(100), -- Obligatorio para Factura A
    email VARCHAR(100),
    telefono VARCHAR(50),
    direccion TEXT,
    creado_en TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Tabla para ventas
CREATE TABLE ventas (
    id SERIAL PRIMARY KEY,
    numero_venta VARCHAR(20) NOT NULL UNIQUE, -- Número interno de venta
    cliente_id INTEGER REFERENCES clientes(id), -- Obligatorio para Factura A
    vendedor_id INTEGER NOT NULL REFERENCES vendedores(id), -- Vendedor que realizó la venta
    fecha_venta TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    monto_total DECIMAL(15, 2) NOT NULL, -- Monto total con IVA
    estado VARCHAR(20) NOT NULL DEFAULT 'COMPLETADA', -- Ej. PENDIENTE, COMPLETADA, ANULADA
    creado_en TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Tabla para detalles de venta (ítems de la venta)
CREATE TABLE detalles_venta (
    id SERIAL PRIMARY KEY,
    venta_id INTEGER NOT NULL REFERENCES ventas(id),
    producto_id INTEGER NOT NULL REFERENCES productos(id),
    cantidad DECIMAL(15, 3) NOT NULL, -- Soporta cantidades fraccionarias (ej. 1.5 kg)
    precio_unitario DECIMAL(15, 2) NOT NULL, -- Precio al momento de la venta (permite personalización)
    tipo_iva_id INTEGER NOT NULL REFERENCES tipos_iva(id), -- IVA al momento de la venta
    subtotal DECIMAL(15, 2) NOT NULL, -- cantidad * precio_unitario (con IVA)
    base_imp DECIMAL(15, 2) NOT NULL, -- Base imponible (sin IVA)
    importe_iva DECIMAL(15, 2) NOT NULL, -- Importe de IVA (base imponible * tipo_iva)
    creado_en TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Tabla para credenciales WSAA (token y sign)
CREATE TABLE wsaa_credenciales (
    id SERIAL PRIMARY KEY,
    servicio VARCHAR(50) NOT NULL, -- Ej. wsfe, ws_sr_constancia_inscripcion
    token TEXT NOT NULL, -- Token generado por WSAA
    sign TEXT NOT NULL, -- Firma generada por WSAA
    fecha_generacion TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    fecha_expiracion TIMESTAMP NOT NULL, -- Fecha de vencimiento del token
    estado VARCHAR(20) NOT NULL DEFAULT 'ACTIVO', -- Ej. ACTIVO, EXPIRADO
    creado_en TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_servicio_activo UNIQUE (servicio, estado),
    CONSTRAINT check_estado CHECK (estado IN ('ACTIVO', 'EXPIRADO'))
);

-- Tabla para comprobantes AFIP
CREATE TABLE comprobantes (
    id SERIAL PRIMARY KEY,
    venta_id INTEGER NOT NULL REFERENCES ventas(id),
    tipo_comprobante VARCHAR(10) NOT NULL, -- Ej. FACTURA_A, FACTURA_B, NOTA_CREDITO, NOTA_DEBITO
    punto_venta INTEGER NOT NULL, -- Punto de venta según AFIP
    numero_comprobante INTEGER NOT NULL, -- Número asignado por AFIP
    cae VARCHAR(50) UNIQUE, -- Código de Autorización Electrónica
    fecha_emision TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    fecha_vencimiento_cae DATE, -- Fecha de vencimiento del CAE
    imp_total DECIMAL(15, 2) NOT NULL, -- Importe total (con IVA)
    imp_tot_conc DECIMAL(15, 2) NOT NULL DEFAULT 0.00, -- Importe no gravado
    imp_neto DECIMAL(15, 2) NOT NULL, -- Importe neto gravado
    imp_iva DECIMAL(15, 2) NOT NULL, -- Importe total IVA
    imp_trib DECIMAL(15, 2) NOT NULL DEFAULT 0.00, -- Otros impuestos
    mon_id VARCHAR(3) NOT NULL DEFAULT 'PES', -- Moneda (PES para pesos argentinos)
    mon_cotiz DECIMAL(10, 4) NOT NULL DEFAULT 1.0, -- Cotización (1 para PES)
    estado VARCHAR(20) NOT NULL DEFAULT 'EMITIDO', -- Ej. EMITIDO, ANULADO
    comprobante_relacionado_id INTEGER REFERENCES comprobantes(id), -- Para notas de crédito/débito
    creado_en TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_comprobante UNIQUE (punto_venta, numero_comprobante)
);

-- Índices para mejorar rendimiento
CREATE INDEX idx_ventas_fecha_venta ON ventas(fecha_venta);
CREATE INDEX idx_ventas_vendedor_id ON ventas(vendedor_id);
CREATE INDEX idx_detalles_venta_venta_id ON detalles_venta(venta_id);
CREATE INDEX idx_comprobantes_venta_id ON comprobantes(venta_id);
CREATE INDEX idx_comprobantes_relacionado_id ON comprobantes(comprobante_relacionado_id);
CREATE INDEX idx_productos_unidad_medida_id ON productos(unidad_medida_id);
CREATE INDEX idx_productos_tipo_iva_id ON productos(tipo_iva_id);
CREATE INDEX idx_productos_categoria_id ON productos(categoria_id);
CREATE INDEX idx_detalles_venta_tipo_iva_id ON detalles_venta(tipo_iva_id);
CREATE INDEX idx_wsaa_credenciales_servicio ON wsaa_credenciales(servicio, estado);

-- Datos iniciales para tipos de IVA
INSERT INTO tipos_iva (porcentaje, descripcion) VALUES
(0.00, 'Exento'),
(10.50, 'IVA 10.5%'),
(21.00, 'IVA 21%'),
(27.00, 'IVA 27%');

-- Datos iniciales para unidades de medida (basado en códigos AFIP)
INSERT INTO unidades_medida (codigo, nombre_corto, descripcion) VALUES
('UN', 'U', 'Unidad'),
('KG', 'Kg', 'Kilogramo'),
('LT', 'L', 'Litro'),
('DO', 'D', 'Docena');

-- Datos iniciales para categorías
INSERT INTO categorias (nombre, descripcion) VALUES
('Bebidas', 'Bebidas frías y calientes'),
('Postres', 'Postres y dulces'),
('Comidas', 'Platos principales y acompañamientos');


-- Producto genérico para "Varios"
INSERT INTO productos (codigo, nombre, unidad_medida_id, tipo_iva_id, precio_unitario, stock_actual, activo)
SELECT 'VARIOS', 'Otros', id, (SELECT id FROM tipos_iva WHERE porcentaje = 21.00), NULL, NULL, TRUE
FROM unidades_medida WHERE codigo = 'UN';


-- Ejemplo de consulta para total de ventas con/sin comprobante
-- SELECT
--     COUNT(v.id) as total_ventas,
--     COUNT(c.id) as ventas_con_comprobante,
--     COUNT(v.id) - COUNT(c.id) as ventas_sin_comprobante,
--     SUM(CASE WHEN c.id IS NOT NULL THEN v.monto_total ELSE 0 END) as monto_con_comprobante,
--     SUM(CASE WHEN c.id IS NULL THEN v.monto_total ELSE 0 END) as monto_sin_comprobante
-- FROM ventas v
-- LEFT JOIN comprobantes c ON v.id = c.venta_id
-- WHERE v.fecha_venta BETWEEN '2025-01-01' AND '2025-12-31';


-- Ejemplo de consulta para mapear datos al request de AFIP
-- SELECT
--     c.punto_venta AS PuntoVta,
--     c.numero_comprobante AS Cbte,
--     c.tipo_comprobante AS TipoCbte,
--     c.imp_total AS ImpTotal,
--     c.imp_tot_conc AS ImpTotConc,
--     c.imp_neto AS ImpNeto,
--     c.imp_iva AS ImpIVA,
--     c.imp_trib AS ImpTrib,
--     c.mon_id AS MonId,
--     c.mon_cotiz AS MonCotiz,
--     cl.cuit AS CuitComprador,
--     cl.razon_social AS RazonSocialComprador,
--     json_agg(
--         json_build_object(
--             'cantidad', dv.cantidad,
--             'precio_unitario', dv.precio_unitario,
--             'base_imp', dv.base_imp,
--             'importe_iva', dv.importe_iva,
--             'alicuota_iva', ti.porcentaje
--         )
--     ) AS Items
-- FROM comprobantes c
-- JOIN ventas v ON c.venta_id = v.id
-- LEFT JOIN clientes cl ON v.cliente_id = cl.id
-- JOIN detalles_venta dv ON dv.venta_id = v.id
-- JOIN tipos_iva ti ON dv.tipo_iva_id = ti.id
-- WHERE c.id = :comprobante_id
-- GROUP BY c.id, cl.cuit, cl.razon_social;