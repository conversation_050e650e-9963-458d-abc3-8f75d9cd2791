-- Migración para eliminar el constraint UNIQUE del campo cae
-- Los códigos CAEA se repiten entre múltiples comprobantes del mismo período
-- Solo los CAE deben ser únicos, pero los CAEA por naturaleza se duplican

-- Eliminar el constraint UNIQUE del campo cae
ALTER TABLE comprobantes DROP CONSTRAINT IF EXISTS comprobantes_cae_key;

-- Comentario para documentación
COMMENT ON COLUMN comprobantes.cae IS 'Código de Autorización Electrónica (CAE) o Código de Autorización Electrónica Anticipada (CAEA). Los CAE son únicos, pero los CAEA se repiten entre comprobantes del mismo período';

-- Crear índice no único para mejorar consultas por CAE/CAEA
CREATE INDEX IF NOT EXISTS idx_comprobantes_cae ON comprobantes(cae);

-- Comentario adicional en la tabla
COMMENT ON TABLE comprobantes IS 
    'Comprobantes fiscales de AFIP. Los CAE son únicos por comprobante, pero los CAEA se repiten entre múltiples comprobantes del mismo período CAEA.';
