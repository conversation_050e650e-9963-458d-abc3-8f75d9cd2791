-- Migración para agregar campo orden a la tabla categorias
-- El orden se utilizará para facilitar la visualización de productos en el frontend

ALTER TABLE categorias 
ADD COLUMN orden INTEGER;

-- Inicializar el orden basado en el orden alfabético actual
-- Esto preserva el orden actual que se muestra en el frontend
UPDATE categorias 
SET orden = subquery.row_num
FROM (
    SELECT id, ROW_NUMBER() OVER (ORDER BY nombre) as row_num
    FROM categorias
    WHERE activo = true
) as subquery
WHERE categorias.id = subquery.id;

-- Establecer orden por defecto para categorías inactivas
UPDATE categorias 
SET orden = 999
WHERE orden IS NULL;

-- Agregar constraint para asegurar que el orden sea positivo
ALTER TABLE categorias 
ADD CONSTRAINT chk_orden_positive CHECK (orden > 0);

-- Comentario para documentación
COMMENT ON COLUMN categorias.orden IS 'Orden de visualización de las categorías en el frontend, valores más bajos aparecen primero';
