-- Migración para agregar categoría especial "Sin Categoría"
-- Esta categoría se usa únicamente para definir el orden de visualización 
-- de los productos sin categoría en el frontend

-- Insertar la categoría especial "Sin Categoría"
-- Usamos orden 999 por defecto para que aparezca al final
INSERT INTO categorias (nombre, descripcion, orden, activo, creado_en) 
VALUES (
    'Sin Categoría', 
    'Categoría especial para definir el orden de productos sin categoría. No eliminar.',
    999,
    true,
    CURRENT_TIMESTAMP
);

-- Comentario para documentación
COMMENT ON TABLE categorias IS 'Tabla de categorías. La categoría "Sin Categoría" es especial y no debe eliminarse, se usa solo para ordenamiento en frontend';
