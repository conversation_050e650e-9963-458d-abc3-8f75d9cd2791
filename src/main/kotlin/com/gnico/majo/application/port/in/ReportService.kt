package com.gnico.majo.application.port.`in`

import com.gnico.majo.application.domain.model.*
import java.time.LocalDateTime

/**
 * Servicio para generación de reportes y estadísticas de negocio
 */
interface ReportService {

    /**
     * Obtiene un reporte completo de ventas con todos los desgloses
     * @param filters Filtros a aplicar al reporte
     * @return Reporte completo de ventas
     */
    suspend fun getSalesReport(filters: ReportFilters): SalesReport

    /**
     * Obtiene comparativa entre dos períodos
     * @param currentPeriod Período actual
     * @param previousPeriod Período anterior para comparar
     * @param filters Filtros adicionales
     * @return Comparativa entre períodos
     */
    suspend fun getComparativeReport(
        currentPeriod: ReportPeriod,
        previousPeriod: ReportPeriod,
        filters: ReportFilters? = null
    ): ComparativeSummary



    /**
     * Valida filtros de reporte
     * @param filters Filtros a validar
     * @throws IllegalArgumentException si los filtros no son válidos
     */
    fun validateReportFilters(filters: ReportFilters)
}

/**
 * Extensiones para facilitar el uso de períodos comunes
 */
object ReportServiceExtensions {
    
    /**
     * Crea filtros básicos para un período
     */
    fun createBasicFilters(period: ReportPeriod): ReportFilters {
        return ReportFilters(
            periodo = period,
            incluirCanceladas = false
        )
    }

    /**
     * Crea filtros para ventas electrónicas pendientes
     */
    fun createElectronicPendingFilters(period: ReportPeriod): ReportFilters {
        return ReportFilters(
            periodo = period,
            soloConComprobante = false, // Solo sin comprobante
            soloMediosPagoElectronicos = true,
            incluirCanceladas = false
        )
    }

    /**
     * Crea filtros para un vendedor específico
     */
    fun createSellerFilters(period: ReportPeriod, username: String): ReportFilters {
        return ReportFilters(
            periodo = period,
            usuarios = listOf(username),
            incluirCanceladas = false
        )
    }

    /**
     * Crea filtros para un medio de pago específico
     */
    fun createPaymentMethodFilters(period: ReportPeriod, medioPago: String): ReportFilters {
        return ReportFilters(
            periodo = period,
            mediosPago = listOf(medioPago),
            incluirCanceladas = false
        )
    }
}
