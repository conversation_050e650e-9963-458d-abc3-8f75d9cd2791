package com.gnico.majo.application.port.`in`

import com.gnico.majo.application.domain.model.Id

/**
 * Servicio para manejar la cancelación de ventas
 */
interface SaleCancellationService {
    
    /**
     * Cancela una venta y genera automáticamente una nota de crédito B si la venta tenía comprobante emitido
     * @param ventaId ID de la venta a cancelar
     * @param usuarioCancelacion Usuario que realiza la cancelación
     * @param motivo Motivo de la cancelación
     * @param generarNotaCreditoOnline Si true, genera la nota de crédito online (CAE), si false offline (CAEA)
     * @return Resultado de la cancelación con información de la nota de crédito si se generó
     */
    suspend fun cancelSale(
        ventaId: Id,
        usuarioCancelacion: String,
        motivo: String,
        generarNotaCreditoOnline: Boolean = true
    ): SaleCancellationResult
    
    /**
     * Obtiene el historial de ventas canceladas
     * @param limit Límite de resultados
     * @return Lista de información de ventas canceladas
     */
    fun getCancelledSalesHistory(limit: Int = 100): List<CancelledSaleInfo>
    
    /**
     * Obtiene estadísticas de cancelaciones
     * @return Estadísticas de cancelaciones
     */
    fun getCancellationStats(): CancellationStats

    /**
     * Obtiene ventas canceladas que necesitan nota de crédito
     * @param limit Límite de resultados
     * @return Lista de ventas canceladas pendientes de nota de crédito
     */
    fun getSalesPendingNotaCredito(limit: Int = 100): List<CancelledSaleInfo>

    /**
     * Obtiene estadísticas de ventas pendientes de nota de crédito
     * @return Número de ventas canceladas que necesitan nota de crédito
     */
    fun countSalesPendingNotaCredito(): Int
}

/**
 * Resultado de la operación de cancelación
 */
data class SaleCancellationResult(
    val success: Boolean,
    val message: String,
    val ventaId: Int,
    val numeroVenta: String,
    val notaCreditoGenerada: Boolean = false,
    val notaCreditoId: Int? = null,
    val notaCreditoNumero: String? = null,
    val error: String? = null
)

/**
 * Información de una venta cancelada
 */
data class CancelledSaleInfo(
    val ventaId: Int,
    val numeroVenta: String,
    val fechaVenta: String,
    val fechaCancelacion: String,
    val usuarioVenta: String,
    val usuarioCancelacion: String,
    val motivoCancelacion: String,
    val montoTotal: String,
    val teniaComprobante: Boolean,
    val notaCreditoGenerada: Boolean,
    val notaCreditoNumero: String?
)

/**
 * Estadísticas de cancelaciones
 */
data class CancellationStats(
    val totalCancelaciones: Int,
    val cancelacionesConNotaCredito: Int,
    val cancelacionesSinNotaCredito: Int
)
