package com.gnico.majo.application.port.`in`

import com.gnico.majo.application.domain.model.Usuario

/**
 * Puerto de entrada para el servicio de usuarios
 */
interface UsuarioService {
    
    /**
     * Obtiene todos los usuarios activos
     * @return Lista de usuarios activos
     */
    suspend fun getAllActiveUsers(): List<Usuario>
    
    /**
     * Obtiene todos los usuarios (activos e inactivos)
     * @return Lista de todos los usuarios
     */
    suspend fun getAllUsers(): List<Usuario>
    
    /**
     * Busca un usuario por su username
     * @param username Username del usuario a buscar
     * @return Usuario si existe, null en caso contrario
     */
    suspend fun getUserByUsername(username: String): Usuario?
    
    /**
     * Crea un nuevo usuario
     * @param username Username único del usuario
     * @param nombre Nombre completo del usuario
     * @param nombreDisplay Nombre para mostrar en la interfaz
     * @param activo Estado inicial del usuario
     * @return Username del usuario creado
     */
    suspend fun createUser(
        username: String,
        nombre: String,
        nombreDisplay: String,
        activo: Boolean = true
    ): String
    
    /**
     * Actualiza un usuario existente
     * @param usuario Usuario con los datos actualizados
     * @return true si se actualizó correctamente, false en caso contrario
     */
    suspend fun updateUser(usuario: Usuario): Boolean
    
    /**
     * Elimina un usuario (soft delete - marca como inactivo)
     * @param username Username del usuario a eliminar
     * @return true si se eliminó correctamente, false en caso contrario
     */
    suspend fun deleteUser(username: String): Boolean


}
