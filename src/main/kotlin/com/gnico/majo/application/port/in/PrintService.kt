package com.gnico.majo.application.port.`in`

import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.ReportFilters

/**
 * Servicio especializado para impresión de tickets
 *
 * Para búsquedas de ventas/comprobantes, usar SaleService:
 * - findSaleByNumero() - Buscar venta por número
 * - findSalesWithFilters() - Buscar ventas con filtros
 * - findSaleById() - Buscar venta por ID
 */
interface PrintService {

    /**
     * Imprime un ticket térmico para una venta
     * @param ventaId ID de la venta a imprimir
     * @param comprobanteId ID del comprobante (opcional, null para ticket genérico)
     * @throws IllegalArgumentException si la venta no existe
     */
    fun imprimirTicket(ventaId: Id, comprobanteId: Id? = null)

    /**
     * Imprime un ticket térmico con el reporte de ventas
     * @param filters Filtros para generar el reporte de ventas
     * @throws IllegalArgumentException si los filtros son inválidos
     */
    suspend fun imprimirReporteVentas(filters: ReportFilters)
}
