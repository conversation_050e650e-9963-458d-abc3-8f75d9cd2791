package com.gnico.majo.application.port.`in`

import com.gnico.majo.application.domain.model.Categoria
import com.gnico.majo.application.domain.model.Id

/**
 * Puerto de entrada para el servicio de categorías
 */
interface CategoriaService {
    
    /**
     * Obtiene todas las categorías activas
     */
    fun getAllCategorias(): List<Categoria>
    
    /**
     * Obtiene una categoría por su ID
     */
    fun getCategoriaById(id: Id): Categoria?
    
    /**
     * Crea una nueva categoría
     * @return el ID de la categoría creada
     * @throws IllegalArgumentException si ya existe una categoría con el mismo nombre
     */
    fun createCategoria(categoria: Categoria): Id
    
    /**
     * Actualiza una categoría existente
     * @return true si se actualizó correctamente
     * @throws IllegalArgumentException si no existe la categoría o si el nombre ya está en uso por otra categoría
     */
    fun updateCategoria(categoria: Categoria): Boolean
    
    /**
     * Elimina una categoría físicamente de la base de datos
     * También limpia automáticamente todas las referencias a esta categoría en productos
     * @return true si se eliminó correctamente
     * @throws IllegalArgumentException si no existe la categoría
     */
    fun deleteCategoria(id: Id): Boolean

    /**
     * Actualiza el orden de múltiples categorías
     * @param ordenMap mapa de ID de categoría a nuevo orden
     * @return número de categorías actualizadas
     */
    fun updateOrdenCategorias(ordenMap: Map<Int, Int>): Int

    /**
     * Obtiene la categoría especial "Sin Categoría" usada para ordenamiento
     * @return la categoría especial o null si no existe
     */
    fun getSinCategoriaEspecial(): Categoria?
}
