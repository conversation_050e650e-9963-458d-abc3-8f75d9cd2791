package com.gnico.majo.application.port.`in`

import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Producto

interface ProductoService {
    /**
     * Obtiene todos los productos
     */
    fun getAllProductos(): List<Producto>

    /**
     * Obtiene un producto por su código
     */
    fun getProductoByCodigo(codigo: Int): Producto?

    /**
     * Crea un nuevo producto
     * @return código del producto creado
     */
    fun createProducto(producto: Producto): Int

    /**
     * Actualiza un producto existente
     * @return true si se actualizó correctamente, false si no se encontró el producto
     */
    fun updateProducto(producto: Producto): Boolean

    /**
     * Elimina un producto por su código
     * @return true si se eliminó correctamente, false si no se encontró el producto
     */
    fun deleteProducto(codigo: Int): Boolean

    /**
     * Elimina múltiples productos por sus códigos
     * @param codigos Lista de códigos de productos a eliminar
     * @return Número de productos eliminados
     */
    fun deleteMultipleProductos(codigos: List<Int>): Int

    /**
     * Actualiza múltiples productos con los campos especificados
     * @param codigos Lista de códigos de productos a actualizar
     * @param categoriaId Nueva categoría (opcional)
     * @param precioUnitario Nuevo precio unitario (opcional)
     * @param unidadMedidaId Nueva unidad de medida (opcional)
     * @param stockActual Nuevo stock actual (opcional)
     * @return Número de productos actualizados
     */
    fun updateMultipleProductos(
        codigos: List<Int>,
        categoriaId: Id? = null,
        precioUnitario: java.math.BigDecimal? = null,
        unidadMedidaId: Id? = null,
        stockActual: Int? = null
    ): Int
}