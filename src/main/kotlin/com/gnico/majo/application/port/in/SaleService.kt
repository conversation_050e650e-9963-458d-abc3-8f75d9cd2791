package com.gnico.majo.application.port.`in`

import com.gnico.majo.adapter.controller.dto.SaleItemRequest
import com.gnico.majo.adapter.controller.dto.SaleFilterRequest
import com.gnico.majo.adapter.controller.dto.SalePageResponse

import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Sale

interface SaleService {

    /**
     * Crea una nueva venta y sus ítems.
     * @return ID de la venta creada
     */
    suspend fun createSale(
        vendedor: String,
        itemsRequest: List<SaleItemRequest>,
        medioPago: String = "EFECTIVO",
        porcentajeDescuento: java.math.BigDecimal? = null,
        codigoTicketBalanza: String? = null,
        idTicketBalanza: String? = null,
        imprimirTicket: Boolean = false,
        facturaOnline: Boolean = false,
        facturaOffline: Boolean = false
    ) : Id





    // Nuevos métodos para búsqueda y consulta de ventas

    /**
     * Busca una venta por su ID
     * @param ventaId ID de la venta
     * @return Venta encontrada o null si no existe
     */
    fun findSaleById(ventaId: Id): Sale?

    /**
     * Busca una venta por su número
     * @param numeroVenta Número de venta
     * @return Venta encontrada o null si no existe
     */
    fun findSaleByNumero(numeroVenta: String): Sale?

    /**
     * Busca ventas por rango de fechas
     * @param fechaDesde Fecha desde (formato: yyyy-MM-dd HH:mm:ss)
     * @param fechaHasta Fecha hasta (formato: yyyy-MM-dd HH:mm:ss)
     * @return Lista de ventas en el rango especificado
     */
    fun findSalesByDateRange(fechaDesde: String, fechaHasta: String): List<Sale>

    /**
     * Busca ventas por usuario
     * @param username Username del usuario
     * @return Lista de ventas del usuario
     */
    fun findSalesByUsuario(username: String): List<Sale>

    /**
     * Busca ventas con filtros y paginación
     * @param filterRequest Criterios de filtrado y paginación
     * @return Página de ventas que coinciden con los criterios
     */
    fun findSalesWithFilters(filterRequest: SaleFilterRequest): SalePageResponse

    /**
     * Cancela una venta
     * @param ventaId ID de la venta a cancelar
     * @param usuarioCancelacion Usuario que realiza la cancelación
     * @param motivo Motivo de la cancelación
     * @param generarNotaCreditoOnline Si true, genera nota de crédito online (CAE), si false offline (CAEA)
     * @return Resultado de la cancelación
     */
    suspend fun cancelSale(
        ventaId: Id,
        usuarioCancelacion: String,
        motivo: String,
        generarNotaCreditoOnline: Boolean = true
    ): SaleCancellationResult

}