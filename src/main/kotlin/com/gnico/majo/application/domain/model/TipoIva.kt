package com.gnico.majo.application.domain.model

import java.math.BigDecimal

/**
 * Enum que representa los tipos de IVA disponibles en el sistema.
 * Cada tipo tiene un ID específico que coincide con los códigos de AFIP
 * y un porcentaje asociado.
 */
enum class TipoIva(val id: Int, val porcentaje: BigDecimal) {
    EXENTO(3, BigDecimal("0.00")),
    IVA_10_5(4, <PERSON><PERSON><PERSON><PERSON><PERSON>("10.50")),
    IVA_21(5, BigDecimal("21.00")),
    IVA_27(6, BigDecimal("27.00")),
    IVA_5(8, BigDecimal("5.00")),
    IVA_2_5(9, BigD<PERSON>imal("2.50"));

    companion object {
        /**
         * Busca un tipo de IVA por su ID
         */
        fun fromId(id: Int): TipoIva? = entries.find { it.id == id }

        /**
         * Busca un tipo de IVA por su ID, lanzando excepción si no se encuentra
         */
        fun fromIdOrThrow(id: Int): TipoIva = 
            fromId(id) ?: throw IllegalArgumentException("Tipo IVA con ID $id no encontrado")

        /**
         * Obtiene todos los tipos de IVA disponibles
         */
        fun getAll(): List<TipoIva> = entries

        /**
         * Mapea el tipo de IVA al código de AFIP
         */
        fun mapToAfipCode(tipoIva: TipoIva): Int = tipoIva.id
    }

    /**
     * Obtiene el porcentaje como decimal para cálculos (ej: 21% = 0.21)
     */
    fun getPorcentajeDecimal(): BigDecimal = porcentaje.divide(BigDecimal("100"))

    /**
     * Descripción legible del tipo de IVA
     */
    fun getDescripcion(): String = when (this) {
        EXENTO -> "Exento"
        IVA_10_5 -> "10.5%"
        IVA_21 -> "21%"
        IVA_27 -> "27%"
        IVA_5 -> "5%"
        IVA_2_5 -> "2.5%"
    }
}
