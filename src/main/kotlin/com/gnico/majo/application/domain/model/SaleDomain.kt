package com.gnico.majo.application.domain.model

import java.math.BigDecimal

/**
 * Value object que encapsula los montos de impuestos calculados para una venta
 */
data class TaxAmounts(
    val impNeto: BigDecimal,
    val impIva: BigDecimal,
    val impTotal: BigDecimal,
    val impTotConc: BigDecimal,
    val impTrib: BigDecimal
)

/**
 * Enumeración de tipos de comprobantes válidos
 * Simplificado para solo manejar comprobantes tipo B (consumidor final)
 */
enum class TipoComprobante(val codigo: String, val descripcion: String) {
    FACTURA_B("FACTURA_B", "Factura B"),
    NOTA_CREDITO_B("NOTA_CREDITO_B", "Nota de Crédito B"),
    NOTA_DEBITO_B("NOTA_DEBITO_B", "Nota de Débito B");

    companion object {
        fun fromString(codigo: String): TipoComprobante? {
            return values().find { it.codigo == codigo }
        }

        /**
         * Obtiene el tipo de comprobante por defecto (siempre FACTURA_B)
         */
        fun getDefault(): TipoComprobante = FACTURA_B
    }
}

/**
 * Enumeración de medios de pago válidos
 */
enum class MedioPago(val codigo: String, val descripcion: String) {
    EFECTIVO("EFECTIVO", "Efectivo"),
    TARJETA_DEBITO("TARJETA_DEBITO", "Tarjeta de Débito"),
    TARJETA_CREDITO("TARJETA_CREDITO", "Tarjeta de Crédito"),
    QR("QR", "Código QR"),
    TRANSFERENCIA("TRANSFERENCIA", "Transferencia Bancaria");
    
    companion object {
        fun fromString(codigo: String): MedioPago? {
            return values().find { it.codigo == codigo }
        }
    }
}
