package com.gnico.majo.application.domain.model

import java.time.LocalDate
import java.time.LocalDateTime

/**
 * Modelo de dominio para registrar intentos de generación de comprobantes
 * Almacena tanto intentos exitosos como fallidos para auditoría y seguimiento
 */
@ConsistentCopyVisibility
data class ComprobanteAttempt private constructor(
    val id: Id? = null,
    val ventaId: Id,
    val tipoComprobante: String,
    val puntoVenta: Int,
    val tipoOperacion: TipoOperacionAfip,
    val estado: EstadoIntento,
    
    // Datos de respuesta exitosa
    val cae: String? = null,
    val numeroComprobante: Long? = null,
    val fechaVencimientoCae: LocalDate? = null,
    
    // Datos de error
    val codigoError: String? = null,
    val mensajeError: String? = null,
    val observacionesAfip: List<String> = emptyList(),
    
    // Metadatos
    val fechaIntento: LocalDateTime = LocalDateTime.now(),
    val tiempoRespuestaMs: Int? = null,
    val comprobanteId: Id? = null
) {
    
    /**
     * Verifica si el intento fue exitoso
     */
    fun isExitoso(): Boolean = estado == EstadoIntento.EXITOSO
    
    /**
     * Verifica si el intento falló
     */
    fun isFallido(): Boolean = estado == EstadoIntento.FALLIDO
    
    /**
     * Verifica si el intento está pendiente
     */
    fun isPendiente(): Boolean = estado == EstadoIntento.PENDIENTE
    
    /**
     * Obtiene un resumen del error para mostrar al usuario
     */
    fun getErrorSummary(): String? = when {
        isFallido() && !mensajeError.isNullOrBlank() -> mensajeError
        isFallido() && observacionesAfip.isNotEmpty() -> observacionesAfip.joinToString("; ")
        isFallido() -> "Error desconocido en la generación del comprobante"
        else -> null
    }
    
    /**
     * Obtiene el detalle completo del error
     */
    fun getErrorDetail(): String? = when {
        isFallido() -> buildString {
            if (!codigoError.isNullOrBlank()) {
                append("Código: $codigoError\n")
            }
            if (!mensajeError.isNullOrBlank()) {
                append("Mensaje: $mensajeError\n")
            }
            if (observacionesAfip.isNotEmpty()) {
                append("Observaciones AFIP: ${observacionesAfip.joinToString("; ")}")
            }
        }.takeIf { it.isNotBlank() }
        else -> null
    }
    
    companion object {
        /**
         * Crea un intento exitoso a partir de una respuesta de AFIP
         */
        fun createExitoso(
            ventaId: Id,
            tipoComprobante: String,
            puntoVenta: Int,
            afipResponse: AfipResponse,
            tiempoRespuestaMs: Int? = null,
            comprobanteId: Id? = null
        ): ComprobanteAttempt {
            require(afipResponse.isApproved()) { "La respuesta de AFIP debe estar aprobada" }
            
            return ComprobanteAttempt(
                ventaId = ventaId,
                tipoComprobante = tipoComprobante,
                puntoVenta = puntoVenta,
                tipoOperacion = afipResponse.tipoOperacion,
                estado = EstadoIntento.EXITOSO,
                cae = afipResponse.cae,
                numeroComprobante = afipResponse.numeroComprobante,
                fechaVencimientoCae = afipResponse.fechaVencimientoCae,
                observacionesAfip = afipResponse.observaciones,
                tiempoRespuestaMs = tiempoRespuestaMs,
                comprobanteId = comprobanteId
            )
        }
        
        /**
         * Crea un intento fallido a partir de una respuesta de AFIP rechazada
         */
        fun createFallidoAfip(
            ventaId: Id,
            tipoComprobante: String,
            puntoVenta: Int,
            afipResponse: AfipResponse,
            tiempoRespuestaMs: Int? = null
        ): ComprobanteAttempt {
            require(afipResponse.isRejected()) { "La respuesta de AFIP debe estar rechazada" }
            
            return ComprobanteAttempt(
                ventaId = ventaId,
                tipoComprobante = tipoComprobante,
                puntoVenta = puntoVenta,
                tipoOperacion = afipResponse.tipoOperacion,
                estado = EstadoIntento.FALLIDO,
                codigoError = "AFIP_REJECTED",
                mensajeError = "Comprobante rechazado por AFIP",
                observacionesAfip = afipResponse.observaciones,
                tiempoRespuestaMs = tiempoRespuestaMs
            )
        }
        
        /**
         * Crea un intento fallido por error de sistema
         */
        fun createFallidoSistema(
            ventaId: Id,
            tipoComprobante: String,
            puntoVenta: Int,
            tipoOperacion: TipoOperacionAfip,
            codigoError: String,
            mensajeError: String,
            tiempoRespuestaMs: Int? = null
        ): ComprobanteAttempt {
            return ComprobanteAttempt(
                ventaId = ventaId,
                tipoComprobante = tipoComprobante,
                puntoVenta = puntoVenta,
                tipoOperacion = tipoOperacion,
                estado = EstadoIntento.FALLIDO,
                codigoError = codigoError,
                mensajeError = mensajeError,
                tiempoRespuestaMs = tiempoRespuestaMs
            )
        }
        
        /**
         * Crea un intento pendiente
         */
        fun createPendiente(
            ventaId: Id,
            tipoComprobante: String,
            puntoVenta: Int,
            tipoOperacion: TipoOperacionAfip
        ): ComprobanteAttempt {
            return ComprobanteAttempt(
                ventaId = ventaId,
                tipoComprobante = tipoComprobante,
                puntoVenta = puntoVenta,
                tipoOperacion = tipoOperacion,
                estado = EstadoIntento.PENDIENTE
            )
        }

        /**
         * Crea un ComprobanteAttempt desde datos de persistencia
         */
        fun fromPersistence(
            id: Id,
            ventaId: Id,
            tipoComprobante: String,
            puntoVenta: Int,
            tipoOperacion: TipoOperacionAfip,
            estado: EstadoIntento,
            cae: String?,
            numeroComprobante: Long?,
            fechaVencimientoCae: LocalDate?,
            codigoError: String?,
            mensajeError: String?,
            observacionesAfip: List<String>,
            fechaIntento: LocalDateTime,
            tiempoRespuestaMs: Int?,
            comprobanteId: Id?
        ): ComprobanteAttempt {
            return ComprobanteAttempt(
                id = id,
                ventaId = ventaId,
                tipoComprobante = tipoComprobante,
                puntoVenta = puntoVenta,
                tipoOperacion = tipoOperacion,
                estado = estado,
                cae = cae,
                numeroComprobante = numeroComprobante,
                fechaVencimientoCae = fechaVencimientoCae,
                codigoError = codigoError,
                mensajeError = mensajeError,
                observacionesAfip = observacionesAfip,
                fechaIntento = fechaIntento,
                tiempoRespuestaMs = tiempoRespuestaMs,
                comprobanteId = comprobanteId
            )
        }
    }
}

/**
 * Estados posibles de un intento de comprobante
 */
enum class EstadoIntento {
    EXITOSO,
    FALLIDO,
    PENDIENTE
}
