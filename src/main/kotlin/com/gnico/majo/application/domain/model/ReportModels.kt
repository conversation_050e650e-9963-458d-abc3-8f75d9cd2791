package com.gnico.majo.application.domain.model

import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.LocalDate
import java.time.DayOfWeek
import java.time.temporal.TemporalAdjusters

/**
 * Período de reporte con factory methods para casos comunes
 */
data class ReportPeriod(
    val desde: LocalDateTime,
    val hasta: LocalDateTime,
    val descripcion: String
) {
    init {
        require(desde <= hasta) { "La fecha desde debe ser menor o igual a la fecha hasta" }
    }

    companion object {
        /**
         * Reporte del día actual (desde las 00:00 hasta ahora)
         */
        fun today(): ReportPeriod {
            val now = LocalDateTime.now()
            val startOfDay = now.toLocalDate().atStartOfDay()
            return ReportPeriod(startOfDay, now, "Hoy")
        }

        /**
         * Reporte del día anterior completo (desde las 00:00 hasta las 23:59:59)
         */
        fun yesterday(): ReportPeriod {
            val yesterday = LocalDateTime.now().minusDays(1)
            val startOfYesterday = yesterday.toLocalDate().atStartOfDay()
            val endOfYesterday = yesterday.toLocalDate().atTime(23, 59, 59)
            return ReportPeriod(startOfYesterday, endOfYesterday, "Ayer")
        }

        /**
         * Reporte del mes actual (desde el día 1 hasta ahora)
         */
        fun currentMonth(): ReportPeriod {
            val now = LocalDateTime.now()
            val startOfMonth = now.toLocalDate().withDayOfMonth(1).atStartOfDay()
            return ReportPeriod(startOfMonth, now, "Mes actual")
        }

        /**
         * Reporte del último mes completo
         */
        fun lastMonth(): ReportPeriod {
            val now = LocalDateTime.now()
            val lastMonth = now.minusMonths(1)
            val startOfLastMonth = lastMonth.toLocalDate().withDayOfMonth(1).atStartOfDay()
            val endOfLastMonth = lastMonth.toLocalDate()
                .with(TemporalAdjusters.lastDayOfMonth())
                .atTime(23, 59, 59)
            return ReportPeriod(startOfLastMonth, endOfLastMonth, "Último mes")
        }

        /**
         * Reporte de esta semana (desde el último lunes hasta ahora)
         */
        fun currentWeek(): ReportPeriod {
            val now = LocalDateTime.now()
            val startOfWeek = now.toLocalDate()
                .with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
                .atStartOfDay()
            return ReportPeriod(startOfWeek, now, "Esta semana")
        }

        /**
         * Reporte personalizado por rango de fechas
         */
        fun custom(desde: LocalDateTime, hasta: LocalDateTime): ReportPeriod {
            return ReportPeriod(desde, hasta, "Período personalizado")
        }

        /**
         * Factory method desde strings para API
         */
        fun fromString(periodo: String): ReportPeriod {
            return when (periodo.lowercase()) {
                "today", "hoy" -> today()
                "current_month", "currentmonth", "mes_actual" -> currentMonth()
                "last_month", "lastmonth", "ultimo_mes" -> lastMonth()
                "current_week", "currentweek", "esta_semana" -> currentWeek()
                "yesterday", "ayer" -> yesterday()
                else -> throw IllegalArgumentException("Período '$periodo' no válido. Opciones: today, yesterday, current_week, current_month, last_month")
            }
        }

        /**
         * Método auxiliar para thisWeek (alias de currentWeek)
         */
        fun thisWeek(): ReportPeriod = currentWeek()

        /**
         * Método auxiliar para thisMonth (alias de currentMonth)
         */
        fun thisMonth(): ReportPeriod = currentMonth()
    }

    /**
     * Verifica si este período corresponde al día de hoy
     */
    fun esHoy(): Boolean {
        val today = LocalDate.now()
        val startOfToday = today.atStartOfDay()
        return desde.toLocalDate() == today && desde == startOfToday
    }

    /**
     * Verifica si este período corresponde al día de ayer
     */
    fun esAyer(): Boolean {
        val yesterday = LocalDate.now().minusDays(1)
        return desde.toLocalDate() == yesterday && hasta.toLocalDate() == yesterday
    }

    /**
     * Verifica si este período corresponde a la semana actual
     */
    fun esSemanaActual(): Boolean {
        val now = LocalDateTime.now()
        val startOfWeek = now.toLocalDate()
            .with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
            .atStartOfDay()
        return desde == startOfWeek && hasta.toLocalDate() == now.toLocalDate()
    }

    /**
     * Verifica si este período corresponde al mes actual
     */
    fun esMesActual(): Boolean {
        val now = LocalDateTime.now()
        val startOfMonth = now.toLocalDate().withDayOfMonth(1).atStartOfDay()
        return desde == startOfMonth && hasta.toLocalDate() == now.toLocalDate()
    }
}

/**
 * Filtros para reportes con validaciones
 */
data class ReportFilters(
    val periodo: ReportPeriod,
    val usuarios: List<String>? = null,
    val mediosPago: List<String>? = null,
    val incluirCanceladas: Boolean = false,
    val soloConComprobante: Boolean? = null, // null = todos, true = solo con comprobante, false = todas las ventas
    val soloMediosPagoElectronicos: Boolean = false
) {
    init {
        // Validar medios de pago si se especifican
        mediosPago?.forEach { medio ->
            MedioPago.fromString(medio)
                ?: throw IllegalArgumentException("Medio de pago '$medio' no válido")
        }
    }

    /**
     * Obtiene los medios de pago electrónicos (todos excepto efectivo)
     */
    fun getMediosPagoElectronicos(): List<String> {
        return MedioPago.values()
            .filter { it != MedioPago.EFECTIVO }
            .map { it.codigo }
    }

    /**
     * Verifica si debe filtrar solo medios electrónicos
     */
    fun shouldFilterElectronicPayments(): Boolean {
        return soloMediosPagoElectronicos
    }
}

/**
 * Reporte general de ventas
 */
data class SalesReport(
    val periodo: ReportPeriod,
    val filtros: ReportFilters,
    val resumenGeneral: SalesGeneralSummary,
    val desglosePorMedioPago: List<PaymentMethodBreakdown>,
    val desglosePorVendedor: List<SellerBreakdown>,
    val productosTopVentas: List<ProductBreakdown>,
    val estadisticasComprobantes: ComprobanteStatistics,
    val alertas: ReportAlerts,
    val histograma: SalesHistogram
)

/**
 * Tipos de agrupación para histogramas
 */
enum class HistogramBucketType {
    HOUR,   // Agrupación por hora
    DAY,    // Agrupación por día
    MONTH   // Agrupación por mes
}

/**
 * Bucket individual del histograma de ventas
 */
data class SalesHistogramBucket(
    val periodo: String,           // Etiqueta del período (ej: "2024-01-15 14:00", "2024-01-15", "2024-01")
    val fechaInicio: LocalDateTime, // Fecha de inicio del bucket
    val fechaFin: LocalDateTime,   // Fecha de fin del bucket
    val cantidadVentas: Int,       // Número de ventas en este bucket
    val montoTotal: BigDecimal     // Monto total de ventas en este bucket
)

/**
 * Histograma de ventas agrupado por tiempo
 */
data class SalesHistogram(
    val periodo: ReportPeriod,
    val filtros: ReportFilters,
    val bucketType: HistogramBucketType,
    val buckets: List<SalesHistogramBucket>,
    val resumen: SalesHistogramSummary
)

/**
 * Resumen del histograma de ventas
 */
data class SalesHistogramSummary(
    val totalVentas: Int,
    val montoTotal: BigDecimal,
    val promedioVentasPorBucket: Double,
    val promedioMontoPorBucket: BigDecimal,
    val bucketConMasVentas: SalesHistogramBucket?,
    val bucketConMayorMonto: SalesHistogramBucket?
)

/**
 * Resumen general de ventas
 */
data class SalesGeneralSummary(
    val totalVentas: Int,
    val montoTotal: BigDecimal,
    val ticketPromedio: BigDecimal,
    val ventasConComprobante: Int,
    val ventasSinComprobante: Int,
    val montoConComprobante: BigDecimal,
    val montoSinComprobante: BigDecimal,
    val ventasCanceladas: Int,
    val montoCancelado: BigDecimal,
    val ventasElectronicasPendientes: Int, // Ventas con medio electrónico sin comprobante
    val montoElectronicosPendientes: BigDecimal
)

/**
 * Desglose por medio de pago
 */
data class PaymentMethodBreakdown(
    val medioPago: String,
    val descripcion: String,
    val cantidadVentas: Int,
    val montoTotal: BigDecimal,
    val porcentajeDelTotal: BigDecimal,
    val ticketPromedio: BigDecimal,
    val esElectronico: Boolean
)

/**
 * Desglose por vendedor
 */
data class SellerBreakdown(
    val username: String,
    val nombreDisplay: String,
    val cantidadVentas: Int,
    val montoTotal: BigDecimal,
    val ticketPromedio: BigDecimal,
    val porcentajeDelTotal: BigDecimal,
    val ventasConComprobante: Int,
    val ventasSinComprobante: Int
)

/**
 * Desglose de productos más vendidos
 */
data class ProductBreakdown(
    val productoNombre: String,
    val cantidadVendida: BigDecimal,
    val unidadMedida: String,
    val montoTotal: BigDecimal,
    val cantidadTransacciones: Int
)

/**
 * Estadísticas de comprobantes
 */
data class ComprobanteStatistics(
    val totalComprobantes: Int,
    val montoTotalFacturado: BigDecimal,
    val montoTotalIva: BigDecimal,
    val desglosePorTipo: List<ComprobanteTypeBreakdown>,
    val notasCredito: CreditNotesSummary
)

/**
 * Desglose por tipo de comprobante
 */
data class ComprobanteTypeBreakdown(
    val tipoComprobante: String,
    val cantidad: Int,
    val montoTotal: BigDecimal,
    val montoIva: BigDecimal
)

/**
 * Resumen de notas de crédito
 */
data class CreditNotesSummary(
    val cantidadNotas: Int,
    val montoTotal: BigDecimal,
    val montoIva: BigDecimal,
    val ventasPendientesNotaCredito: Int
)

/**
 * Alertas del reporte
 */
data class ReportAlerts(
    val ventasSinComprobante: Int,
    val montoSinComprobante: BigDecimal,
    val ventasElectronicasSinComprobante: Int,
    val montoElectronicosSinComprobante: BigDecimal,
    val ventasCanceladasPendientesNC: Int
)

/**
 * Reporte específico de comprobantes fiscales
 */
data class ComprobanteReport(
    val periodo: ReportPeriod,
    val filtros: ReportFilters,
    val totalFacturado: BigDecimal,
    val totalIva: BigDecimal,
    val balanceNeto: BigDecimal, // Facturado - Notas de Crédito
    val desglosePorTipo: List<ComprobanteTypeBreakdown>,
    val notasCredito: CreditNotesSummary,
    val comparativaPeriodoAnterior: ComparativeSummary?
)



/**
 * Comparativa con período anterior
 */
data class ComparativeSummary(
    val ventasAnterior: Int,
    val montoAnterior: BigDecimal,
    val variacionVentas: BigDecimal, // Porcentaje de cambio
    val variacionMonto: BigDecimal
)
