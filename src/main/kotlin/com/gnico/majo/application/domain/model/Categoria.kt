package com.gnico.majo.application.domain.model

import java.time.LocalDateTime
import kotlin.jvm.JvmInline

/**
 * Modelo de dominio para Categoria de productos
 */
data class Categoria(
    val id: Id? = null,
    val nombre: String,
    val descripcion: String? = null,
    val color: String? = null,
    val orden: Int? = null,
    val activo: Boolean = true,
    val creadoEn: LocalDateTime? = null
) {
    init {
        require(nombre.isNotBlank()) { "El nombre no puede estar vacío" }
        require(nombre.length <= 50) { "El nombre no puede exceder 50 caracteres" }
        descripcion?.let {
            require(it.isNotBlank()) { "La descripción no puede estar vacía si se proporciona" }
        }
        orden?.let {
            require(it > 0) { "El orden debe ser un número positivo" }
        }
    }



    /**
     * Crea una copia de la categoría con timestamp de creación
     */
    fun withCreationTimestamp(): Categoria {
        return copy(creadoEn = LocalDateTime.now())
    }

    /**
     * Verifica si esta categoría es la categoría especial "Sin Categoría"
     */
    fun esCategoriaEspecialSinCategoria(): Boolean {
        return nombre == SIN_CATEGORIA_NOMBRE
    }

    companion object {
        /**
         * Nombre de la categoría especial para productos sin categoría
         */
        const val SIN_CATEGORIA_NOMBRE = "Sin Categoría"

        /**
         * Valida y sanitiza un color hexadecimal.
         * Retorna null si el color no tiene el formato "rrggbb" válido.
         */
        fun sanitizeColor(color: String?): String? {
            return if (color != null && color.matches(Regex("^[0-9a-fA-F]{6}$"))) {
                color
            } else {
                null
            }
        }

        fun create(
            nombre: String,
            descripcion: String? = null,
            color: String? = null,
            orden: Int? = null,
            activo: Boolean = true
        ): Categoria {
            return Categoria(
                nombre = nombre,
                descripcion = descripcion,
                color = sanitizeColor(color),
                orden = orden,
                activo = activo,
                creadoEn = LocalDateTime.now()
            )
        }
    }
}
