package com.gnico.majo.application.domain.model

import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

@ConsistentCopyVisibility
data class Comprobante private constructor(
    val id: Id? = null,
    val venta: Id,
    val tipoComprobante: String,
    val puntoVenta: Int,
    val numeroComprobante: Int,
    val cae: String,
    val fechaEmision: LocalDateTime,
    val fechaVencimientoCae: LocalDate,
    val impTotal: BigDecimal,
    val impTotConc: BigDecimal,
    val impNeto: BigDecimal,
    val impIva: BigDecimal,
    val impTrib: BigDecimal,
    val monId: String,
    val monCotiz: BigDecimal,
    val estado: String,
    val tipoAutorizacion: TipoAutorizacion = TipoAutorizacion.CAE,
    val comprobanteAsociado: ComprobanteAsociado? = null,
    val caeaUtilizado: String? = null // Código CAEA utilizado para comprobantes offline
) {
    companion object {
        fun createFromSale(
            sale: Sale,
            tipoComprobante: String,
            puntoVenta: Int,
            ventaId: Id,
            numeroComprobante: Int, // Número real obtenido del repositorio de numeración
            tipoAutorizacion: TipoAutorizacion = TipoAutorizacion.CAE,
            comprobanteAsociado: ComprobanteAsociado? = null,
            comprobanteOriginal: Comprobante? = null
        ): Comprobante {
            // Solo permitir comprobantes tipo B (consumidor final)
            require(tipoComprobante in listOf("FACTURA_B", "NOTA_CREDITO_B", "NOTA_DEBITO_B")) {
                "Solo se permiten comprobantes tipo B (FACTURA_B, NOTA_CREDITO_B, NOTA_DEBITO_B). Tipo recibido: $tipoComprobante"
            }

            // Para notas de crédito/débito, usar los importes exactos del comprobante original
            // para evitar diferencias de redondeo que rechace AFIP
            val taxAmounts = if (tipoComprobante in listOf("NOTA_CREDITO_B", "NOTA_DEBITO_B") && comprobanteOriginal != null) {
                // Usar exactamente los mismos importes de la factura original
                TaxAmounts(
                    impNeto = comprobanteOriginal.impNeto,
                    impIva = comprobanteOriginal.impIva,
                    impTotal = comprobanteOriginal.impTotal,
                    impTotConc = comprobanteOriginal.impTotConc,
                    impTrib = comprobanteOriginal.impTrib
                )
            } else {
                // Para facturas, calcular normalmente desde la venta
                sale.calculateTaxAmounts()
            }

            return Comprobante(
                venta = ventaId,
                tipoComprobante = tipoComprobante,
                puntoVenta = puntoVenta,
                numeroComprobante = numeroComprobante, // Número real obtenido del repositorio de numeración
                cae = "PENDING", // Será actualizado tras WSFE
                fechaEmision = LocalDateTime.now(),
                fechaVencimientoCae = LocalDate.now().plusDays(10),
                impTotal = taxAmounts.impTotal,
                impTotConc = taxAmounts.impTotConc,
                impNeto = taxAmounts.impNeto,
                impIva = taxAmounts.impIva,
                impTrib = taxAmounts.impTrib,
                monId = "PES",
                monCotiz = BigDecimal("1.0"),
                estado = "PENDIENTE",
                tipoAutorizacion = tipoAutorizacion,
                comprobanteAsociado = comprobanteAsociado,
                caeaUtilizado = null // Se asignará después si es CAEA
            )
        }

        // Factory method para crear desde datos persistidos (usado por repositorios)
        fun fromPersistence(
            id: Id?,
            venta: Id,
            tipoComprobante: String,
            puntoVenta: Int,
            numeroComprobante: Int,
            cae: String,
            fechaEmision: LocalDateTime,
            fechaVencimientoCae: LocalDate,
            impTotal: BigDecimal,
            impTotConc: BigDecimal,
            impNeto: BigDecimal,
            impIva: BigDecimal,
            impTrib: BigDecimal,
            monId: String,
            monCotiz: BigDecimal,
            estado: String,
            tipoAutorizacion: TipoAutorizacion = TipoAutorizacion.CAE,
            comprobanteAsociado: ComprobanteAsociado? = null,
            caeaUtilizado: String? = null
        ): Comprobante {
            return Comprobante(
                id = id,
                venta = venta,
                tipoComprobante = tipoComprobante,
                puntoVenta = puntoVenta,
                numeroComprobante = numeroComprobante,
                cae = cae,
                fechaEmision = fechaEmision,
                fechaVencimientoCae = fechaVencimientoCae,
                impTotal = impTotal,
                impTotConc = impTotConc,
                impNeto = impNeto,
                impIva = impIva,
                impTrib = impTrib,
                monId = monId,
                monCotiz = monCotiz,
                estado = estado,
                tipoAutorizacion = tipoAutorizacion,
                comprobanteAsociado = comprobanteAsociado,
                caeaUtilizado = caeaUtilizado
            )
        }
    }

    /**
     * Actualiza el CAE y estado del comprobante
     */
    fun withCAE(cae: String, estado: String): Comprobante {
        return Comprobante(
            id = this.id,
            venta = this.venta,
            tipoComprobante = this.tipoComprobante,
            puntoVenta = this.puntoVenta,
            numeroComprobante = this.numeroComprobante,
            cae = cae,
            fechaEmision = this.fechaEmision,
            fechaVencimientoCae = this.fechaVencimientoCae,
            impTotal = this.impTotal,
            impTotConc = this.impTotConc,
            impNeto = this.impNeto,
            impIva = this.impIva,
            impTrib = this.impTrib,
            monId = this.monId,
            monCotiz = this.monCotiz,
            estado = estado,
            tipoAutorizacion = this.tipoAutorizacion,
            comprobanteAsociado = this.comprobanteAsociado,
            caeaUtilizado = this.caeaUtilizado
        )
    }

    /**
     * Actualiza el CAE, estado y CAEA utilizado del comprobante (para comprobantes offline)
     */
    fun withCAEA(cae: String, estado: String, caeaUtilizado: String): Comprobante {
        return Comprobante(
            id = this.id,
            venta = this.venta,
            tipoComprobante = this.tipoComprobante,
            puntoVenta = this.puntoVenta,
            numeroComprobante = this.numeroComprobante,
            cae = cae,
            fechaEmision = this.fechaEmision,
            fechaVencimientoCae = this.fechaVencimientoCae,
            impTotal = this.impTotal,
            impTotConc = this.impTotConc,
            impNeto = this.impNeto,
            impIva = this.impIva,
            impTrib = this.impTrib,
            monId = this.monId,
            monCotiz = this.monCotiz,
            estado = estado,
            tipoAutorizacion = this.tipoAutorizacion,
            comprobanteAsociado = this.comprobanteAsociado,
            caeaUtilizado = caeaUtilizado
        )
    }

    /**
     * Verifica si este comprobante requiere un comprobante asociado
     * (aplica para notas de crédito y débito)
     */
    fun requiereComprobanteAsociado(): Boolean {
        return tipoComprobante in listOf("NOTA_CREDITO_B", "NOTA_DEBITO_B")
    }
}

/**
 * Información del comprobante asociado para notas de crédito y débito
 * Requerido por AFIP para la estructura CbteAsoc
 */
@ConsistentCopyVisibility
data class ComprobanteAsociado private constructor(
    val tipo: String,           // Tipo de comprobante asociado (ej: "FACTURA_B")
    val puntoVenta: Int,        // Punto de venta del comprobante asociado
    val numeroComprobante: Int  // Número del comprobante asociado
) {
    companion object {
        fun create(
            tipo: String,
            puntoVenta: Int,
            numeroComprobante: Int
        ): ComprobanteAsociado {
            require(puntoVenta > 0) { "El punto de venta debe ser positivo" }
            require(numeroComprobante > 0) { "El número de comprobante debe ser positivo" }
            require(tipo.isNotBlank()) { "El tipo de comprobante no puede estar vacío" }

            return ComprobanteAsociado(
                tipo = tipo,
                puntoVenta = puntoVenta,
                numeroComprobante = numeroComprobante
            )
        }

        fun fromComprobante(comprobante: Comprobante): ComprobanteAsociado {
            return create(
                tipo = comprobante.tipoComprobante,
                puntoVenta = comprobante.puntoVenta,
                numeroComprobante = comprobante.numeroComprobante
            )
        }
    }

    /**
     * Convierte el tipo de comprobante al código AFIP correspondiente
     */
    fun getTipoComprobanteAfipCodigo(): Int {
        return when (tipo) {
            "FACTURA_B" -> 6
            "NOTA_CREDITO_B" -> 8
            "NOTA_DEBITO_B" -> 7
            else -> throw IllegalArgumentException("Tipo de comprobante no soportado: $tipo")
        }
    }
}

/**
 * Enum para tipos de autorización de comprobantes
 */
enum class TipoAutorizacion(val descripcion: String) {
    CAE("Código de Autorización Electrónica - Online"),
    CAEA("Código de Autorización Electrónica Anticipada - Offline");

    companion object {
        fun fromString(tipo: String): TipoAutorizacion? {
            return entries.find { it.name == tipo }
        }
    }
}