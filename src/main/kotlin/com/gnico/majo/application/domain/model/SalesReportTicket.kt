package com.gnico.majo.application.domain.model

import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * Modelo de dominio para ticket de reporte de ventas
 * Contiene los datos necesarios para imprimir un reporte de ventas en ticket térmico
 */
data class SalesReportTicket(
    val periodo: ReportPeriod,
    val fechaGeneracion: LocalDateTime,
    val montoTotalVentas: BigDecimal,
    val montoTotalFacturado: BigDecimal,
    val montoTotalIva: BigDecimal,
    val ventasEfectivo: PaymentSummary,
    val ventasElectronicas: PaymentSummary,
    val resumenGeneral: TicketGeneralSummary
) {
    companion object {
        /**
         * Crea un SalesReportTicket a partir de un SalesReport
         */
        fun fromSalesReport(salesReport: SalesReport): SalesReportTicket {
            // Calcular monto facturado neto (facturado - notas de crédito)
            val montoFacturadoNeto = salesReport.estadisticasComprobantes.montoTotalFacturado
                .subtract(salesReport.estadisticasComprobantes.notasCredito.montoTotal)
            
            // Calcular IVA neto (IVA facturado - IVA de notas de crédito)
            val montoIvaNeto = salesReport.estadisticasComprobantes.montoTotalIva
                .subtract(salesReport.estadisticasComprobantes.notasCredito.montoIva)
            
            // Separar medios de pago en efectivo y electrónicos
            val efectivo = salesReport.desglosePorMedioPago
                .find { it.medioPago == "EFECTIVO" }
                ?: PaymentMethodBreakdown(
                    medioPago = "EFECTIVO",
                    descripcion = "Efectivo",
                    cantidadVentas = 0,
                    montoTotal = BigDecimal.ZERO,
                    porcentajeDelTotal = BigDecimal.ZERO,
                    ticketPromedio = BigDecimal.ZERO,
                    esElectronico = false
                )
            
            val electronicos = salesReport.desglosePorMedioPago
                .filter { it.esElectronico }
            
            val montoElectronicos = electronicos.sumOf { it.montoTotal }
            val cantidadElectronicos = electronicos.sumOf { it.cantidadVentas }
            val porcentajeElectronicos = electronicos.sumOf { it.porcentajeDelTotal }
            
            return SalesReportTicket(
                periodo = salesReport.periodo,
                fechaGeneracion = LocalDateTime.now(),
                montoTotalVentas = salesReport.resumenGeneral.montoTotal,
                montoTotalFacturado = montoFacturadoNeto,
                montoTotalIva = montoIvaNeto,
                ventasEfectivo = PaymentSummary(
                    descripcion = "Efectivo",
                    cantidadVentas = efectivo.cantidadVentas,
                    montoTotal = efectivo.montoTotal,
                    porcentajeDelTotal = efectivo.porcentajeDelTotal
                ),
                ventasElectronicas = PaymentSummary(
                    descripcion = "Electrónico",
                    cantidadVentas = cantidadElectronicos,
                    montoTotal = montoElectronicos,
                    porcentajeDelTotal = porcentajeElectronicos
                ),
                resumenGeneral = TicketGeneralSummary(
                    totalVentas = salesReport.resumenGeneral.totalVentas,
                    ventasCanceladas = salesReport.resumenGeneral.ventasCanceladas,
                    ventasConComprobante = salesReport.resumenGeneral.ventasConComprobante,
                    ventasSinComprobante = salesReport.resumenGeneral.ventasSinComprobante
                )
            )
        }
    }
}

/**
 * Resumen de un medio de pago para el ticket
 */
data class PaymentSummary(
    val descripcion: String,
    val cantidadVentas: Int,
    val montoTotal: BigDecimal,
    val porcentajeDelTotal: BigDecimal
)

/**
 * Resumen general para el ticket
 */
data class TicketGeneralSummary(
    val totalVentas: Int,
    val ventasCanceladas: Int,
    val ventasConComprobante: Int,
    val ventasSinComprobante: Int
)
