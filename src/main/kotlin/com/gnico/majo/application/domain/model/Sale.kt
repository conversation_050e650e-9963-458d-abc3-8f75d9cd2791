package com.gnico.majo.application.domain.model

import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

data class Id(val value: Int) // Identificador genérico para el dominio

@ConsistentCopyVisibility
data class Sale private constructor(
    val id: Id? = null,
    val numeroVenta: String,
    val usuario: Usuario,
    val fechaVenta: LocalDateTime,
    val montoTotal: BigDecimal,
    val comprobanteEmitido: Boolean,
    val medioPago: String,
    val porcentajeDescuento: BigDecimal? = null,
    val codigoTicketBalanza: String? = null,
    val idTicketBalanza: String? = null,
    val cancelada: Boolean = false,
    val fechaCancelacion: LocalDateTime? = null,
    val usuarioCancelacion: String? = null,
    val motivoCancelacion: String? = null,
    val notaCreditoGenerada: Boolean = false,
    val items: List<SaleItem>
) {
    companion object {
        fun create(
            usuario: Usuario,
            items: List<SaleItem>,
            medioPago: String,
            porcentajeDescuento: BigDecimal? = null,
            codigoTicketBalanza: String? = null,
            idTicketBalanza: String? = null
        ): Sale {
            require(items.isNotEmpty()) { "Una venta debe tener al menos un item" }
            require(medioPago.isNotBlank()) { "El medio de pago no puede estar vacío" }

            // Validar descuento si se proporciona
            porcentajeDescuento?.let { descuento ->
                require(descuento >= BigDecimal.ZERO && descuento <= BigDecimal(100)) {
                    "El porcentaje de descuento debe estar entre 0 y 100"
                }
            }

            // Validar que el medio de pago sea válido
            MedioPago.fromString(medioPago)
                ?: throw IllegalArgumentException("Medio de pago '$medioPago' no válido")

            val numeroVenta = generateSaleNumber()
            val montoTotal = calculateTotal(items)

            return Sale(
                numeroVenta = numeroVenta,
                usuario = usuario,
                fechaVenta = LocalDateTime.now(),
                montoTotal = montoTotal,
                comprobanteEmitido = false,
                medioPago = medioPago,
                porcentajeDescuento = porcentajeDescuento,
                codigoTicketBalanza = codigoTicketBalanza,
                idTicketBalanza = idTicketBalanza,
                cancelada = false,
                fechaCancelacion = null,
                usuarioCancelacion = null,
                motivoCancelacion = null,
                notaCreditoGenerada = false,
                items = items
            )
        }

        private fun generateSaleNumber(): String {
            return "V-${UUID.randomUUID().toString().substring(0, 8)}"
        }

        private fun calculateTotal(items: List<SaleItem>): BigDecimal {
            // Los items ya vienen con descuento aplicado en sus subtotales
            return items.sumOf { it.totalWithTax() }
        }

        // Factory method para crear desde datos persistidos (usado por repositorios)
        fun fromPersistence(
            id: Id?,
            numeroVenta: String,
            usuario: Usuario,
            fechaVenta: LocalDateTime,
            montoTotal: BigDecimal,
            comprobanteEmitido: Boolean,
            medioPago: String,
            porcentajeDescuento: BigDecimal?,
            codigoTicketBalanza: String?,
            idTicketBalanza: String?,
            cancelada: Boolean = false,
            fechaCancelacion: LocalDateTime? = null,
            usuarioCancelacion: String? = null,
            motivoCancelacion: String? = null,
            notaCreditoGenerada: Boolean = false,
            items: List<SaleItem>
        ): Sale {
            return Sale(
                id = id,
                numeroVenta = numeroVenta,
                usuario = usuario,
                fechaVenta = fechaVenta,
                montoTotal = montoTotal,
                comprobanteEmitido = comprobanteEmitido,
                medioPago = medioPago,
                porcentajeDescuento = porcentajeDescuento,
                codigoTicketBalanza = codigoTicketBalanza,
                idTicketBalanza = idTicketBalanza,
                cancelada = cancelada,
                fechaCancelacion = fechaCancelacion,
                usuarioCancelacion = usuarioCancelacion,
                motivoCancelacion = motivoCancelacion,
                notaCreditoGenerada = notaCreditoGenerada,
                items = items
            )
        }
    }

    fun canCreateComprobante(tipoComprobante: String): Boolean {
        // Solo permitir comprobantes tipo B (consumidor final)
        return tipoComprobante in listOf("FACTURA_B", "NOTA_CREDITO_B", "NOTA_DEBITO_B")
    }

    fun validateComprobanteCreation(tipoComprobante: String) {
        // Solo permitir comprobantes tipo B (consumidor final)
        if (tipoComprobante !in listOf("FACTURA_B", "NOTA_CREDITO_B", "NOTA_DEBITO_B")) {
            throw IllegalArgumentException("Solo se permiten comprobantes tipo B (FACTURA_B, NOTA_CREDITO_B, NOTA_DEBITO_B). Tipo recibido: $tipoComprobante")
        }
    }

    /**
     * Valida si la venta puede ser cancelada
     */
    fun canBeCancelled(): Boolean {
        return !cancelada
    }

    /**
     * Valida la cancelación de la venta
     */
    fun validateCancellation() {
        if (cancelada) {
            throw IllegalArgumentException("La venta ${numeroVenta} ya está cancelada")
        }
    }

    /**
     * Crea una nueva instancia de Sale marcada como cancelada
     */
    fun cancel(usuarioCancelacion: String, motivo: String): Sale {
        validateCancellation()
        require(usuarioCancelacion.isNotBlank()) { "El usuario de cancelación no puede estar vacío" }
        require(motivo.isNotBlank()) { "El motivo de cancelación no puede estar vacío" }

        return Sale(
            id = this.id,
            numeroVenta = this.numeroVenta,
            usuario = this.usuario,
            fechaVenta = this.fechaVenta,
            montoTotal = this.montoTotal,
            comprobanteEmitido = this.comprobanteEmitido,
            medioPago = this.medioPago,
            porcentajeDescuento = this.porcentajeDescuento,
            codigoTicketBalanza = this.codigoTicketBalanza,
            idTicketBalanza = this.idTicketBalanza,
            cancelada = true,
            fechaCancelacion = LocalDateTime.now(),
            usuarioCancelacion = usuarioCancelacion,
            motivoCancelacion = motivo,
            notaCreditoGenerada = this.notaCreditoGenerada, // Mantener el valor actual
            items = this.items
        )
    }

    /**
     * Marca que se ha generado exitosamente la nota de crédito para esta venta cancelada
     */
    fun markNotaCreditoGenerada(): Sale {
        if (!cancelada) {
            throw IllegalArgumentException("Solo se puede marcar nota de crédito generada en ventas canceladas")
        }
        if (!comprobanteEmitido) {
            throw IllegalArgumentException("Solo se puede generar nota de crédito para ventas que tenían comprobante emitido")
        }

        return Sale(
            id = this.id,
            numeroVenta = this.numeroVenta,
            usuario = this.usuario,
            fechaVenta = this.fechaVenta,
            montoTotal = this.montoTotal,
            comprobanteEmitido = this.comprobanteEmitido,
            medioPago = this.medioPago,
            porcentajeDescuento = this.porcentajeDescuento,
            codigoTicketBalanza = this.codigoTicketBalanza,
            idTicketBalanza = this.idTicketBalanza,
            cancelada = this.cancelada,
            fechaCancelacion = this.fechaCancelacion,
            usuarioCancelacion = this.usuarioCancelacion,
            motivoCancelacion = this.motivoCancelacion,
            notaCreditoGenerada = true,
            items = this.items
        )
    }

    /**
     * Verifica si esta venta cancelada necesita nota de crédito
     */
    fun needsNotaCredito(): Boolean {
        return cancelada && comprobanteEmitido && !notaCreditoGenerada
    }

    fun calculateTaxAmounts(): TaxAmounts {
        val impNeto = items.sumOf { it.baseImp }
        val impIva = items.sumOf { it.importeIva }
        val impTotal = impNeto.add(impIva)

        return TaxAmounts(
            impNeto = impNeto,
            impIva = impIva,
            impTotal = impTotal,
            impTotConc = BigDecimal.ZERO,
            impTrib = BigDecimal.ZERO
        )
    }

    /**
     * Obtiene los detalles de IVA agrupados por tipo para AFIP
     */
    fun getIvaDetails(): List<IvaDetail> {
        return items.groupBy { it.tipoIva }
            .map { (tipoIva, itemsGroup) ->
                val baseImponible = itemsGroup.sumOf { it.baseImp }
                val importeIva = itemsGroup.sumOf { it.importeIva }

                IvaDetail(
                    id = TipoIva.mapToAfipCode(tipoIva),
                    baseImponible = baseImponible,
                    importe = importeIva
                )
            }
            .filter { it.baseImponible > BigDecimal.ZERO }
    }

    /**
     * Calcula el subtotal sin descuento aplicado (precio original de todos los items)
     */
    fun calculateSubtotalSinDescuento(): BigDecimal {
        return items.sumOf { item ->
            item.cantidad.multiply(item.precioUnitario)
        }
    }

    /**
     * Calcula el monto total del descuento aplicado
     */
    fun calculateMontoDescuento(): BigDecimal {
        return if (porcentajeDescuento != null && porcentajeDescuento > BigDecimal.ZERO) {
            val subtotalSinDescuento = calculateSubtotalSinDescuento()
            subtotalSinDescuento.subtract(montoTotal)
        } else {
            BigDecimal.ZERO
        }
    }

    /**
     * Verifica si la venta tiene descuento aplicado
     */
    fun hasDescuento(): Boolean {
        return porcentajeDescuento != null && porcentajeDescuento > BigDecimal.ZERO
    }
}

@ConsistentCopyVisibility
data class SaleItem private constructor(
    val productoNombre: String,
    val cantidad: BigDecimal,
    val precioUnitario: BigDecimal,
    val tipoIva: TipoIva,
    val subtotal: BigDecimal,
    val baseImp: BigDecimal,
    val importeIva: BigDecimal
) {
    companion object {
        /**
         * Crea un SaleItem donde el precio unitario incluye IVA
         */
        fun create(
            productoNombre: String,
            cantidad: BigDecimal,
            precioUnitario: BigDecimal,
            tipoIva: TipoIva,
            porcentajeDescuento: BigDecimal? = null
        ): SaleItem {
            require(productoNombre.isNotBlank()) { "El nombre del producto no puede estar vacío" }
            require(cantidad > BigDecimal.ZERO) { "La cantidad debe ser mayor a cero" }
            require(precioUnitario >= BigDecimal.ZERO) { "El precio unitario no puede ser negativo" }

            // Validar descuento si se proporciona
            porcentajeDescuento?.let { descuento ->
                require(descuento >= BigDecimal.ZERO && descuento <= BigDecimal(100)) {
                    "El porcentaje de descuento debe estar entre 0 y 100"
                }
            }

            // Calcular subtotal base
            val subtotalBase = cantidad.multiply(precioUnitario)

            // Aplicar descuento si existe
            val subtotal = if (porcentajeDescuento != null && porcentajeDescuento > BigDecimal.ZERO) {
                val descuentoDecimal = porcentajeDescuento.divide(BigDecimal(100), 4, java.math.RoundingMode.HALF_UP)
                val montoDescuento = subtotalBase.multiply(descuentoDecimal)
                subtotalBase.subtract(montoDescuento).setScale(2, java.math.RoundingMode.HALF_UP)
            } else {
                subtotalBase
            }

            // Calcular base imponible (precio sin IVA)
            val divisor = BigDecimal.ONE.add(tipoIva.getPorcentajeDecimal())
            val baseImp = subtotal.divide(divisor, 2, java.math.RoundingMode.HALF_UP)
            val importeIva = subtotal.subtract(baseImp)

            return SaleItem(
                productoNombre = productoNombre,
                cantidad = cantidad,
                precioUnitario = precioUnitario,
                tipoIva = tipoIva,
                subtotal = subtotal,
                baseImp = baseImp,
                importeIva = importeIva
            )
        }

        // Factory method para crear desde datos persistidos (usado por repositorios)
        fun fromPersistence(
            productoNombre: String,
            cantidad: BigDecimal,
            precioUnitario: BigDecimal,
            tipoIvaId: Int,
            subtotal: BigDecimal,
            baseImp: BigDecimal,
            importeIva: BigDecimal
        ): SaleItem {
            val tipoIva = TipoIva.fromIdOrThrow(tipoIvaId)
            return SaleItem(
                productoNombre = productoNombre,
                cantidad = cantidad,
                precioUnitario = precioUnitario,
                tipoIva = tipoIva,
                subtotal = subtotal,
                baseImp = baseImp,
                importeIva = importeIva
            )
        }
    }

    fun totalWithTax(): BigDecimal = subtotal
}