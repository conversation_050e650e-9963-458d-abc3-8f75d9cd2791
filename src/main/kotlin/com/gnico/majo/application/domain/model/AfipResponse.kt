package com.gnico.majo.application.domain.model

import java.time.LocalDate

/**
 * Modelo de dominio rico para las respuestas de AFIP
 * Encapsula tanto respuestas de CAE como CAEA con lógica de negocio
 */
@ConsistentCopyVisibility
data class AfipResponse private constructor(
    val cae: String,
    val fechaVencimientoCae: LocalDate,
    val resultado: String, // "A" = Aprobado, "R" = Rechazado
    val numeroComprobante: Long,
    val observaciones: List<String> = emptyList(),
    val tipoOperacion: TipoOperacionAfip = TipoOperacionAfip.CAE_ONLINE
) {
    
    /**
     * Verifica si la respuesta de AFIP fue aprobada
     */
    fun isApproved(): Boolean = resultado == "A"
    
    /**
     * Verifica si la respuesta de AFIP fue rechazada
     */
    fun isRejected(): Boolean = resultado == "R"
    
    /**
     * Verifica si el CAE/CAEA está vencido
     */
    fun isExpired(): Boolean = fechaVencimientoCae.isBefore(LocalDate.now())
    
    /**
     * Verifica si el CAE/CAEA es válido (aprobado y no vencido)
     */
    fun isValid(): Boolean = isApproved() && !isExpired()
    
    /**
     * Obtiene el estado descriptivo de la respuesta
     */
    fun getEstadoDescriptivo(): String = when {
        isRejected() -> "RECHAZADO"
        isExpired() -> "VENCIDO"
        isApproved() -> when (tipoOperacion) {
            TipoOperacionAfip.CAE_ONLINE -> "AUTORIZADO"
            TipoOperacionAfip.CAEA_OFFLINE -> "AUTORIZADO_OFFLINE"
            TipoOperacionAfip.CAEA_SOLICITUD -> "CAEA_OBTENIDO"
            TipoOperacionAfip.CAEA_INFORMATIVO -> "INFORMADO"
        }
        else -> "PENDIENTE"
    }
    
    /**
     * Obtiene un resumen de las observaciones
     */
    fun getObservacionesResumen(): String = when {
        observaciones.isEmpty() -> "Sin observaciones"
        observaciones.size == 1 -> observaciones.first()
        else -> "${observaciones.first()} (+${observaciones.size - 1} más)"
    }
    
    /**
     * Verifica si es una operación online (CAE)
     */
    fun isOnlineOperation(): Boolean = tipoOperacion == TipoOperacionAfip.CAE_ONLINE
    
    /**
     * Verifica si es una operación offline (CAEA)
     */
    fun isOfflineOperation(): Boolean = tipoOperacion == TipoOperacionAfip.CAEA_OFFLINE
    
    companion object {
        /**
         * Crea una respuesta aprobada para CAE online
         */
        fun createApprovedCAE(
            cae: String,
            fechaVencimiento: LocalDate,
            numeroComprobante: Long,
            observaciones: List<String> = emptyList()
        ): AfipResponse {
            require(cae.isNotBlank()) { "El CAE no puede estar vacío" }
            require(numeroComprobante > 0) { "El número de comprobante debe ser positivo" }
            
            return AfipResponse(
                cae = cae,
                fechaVencimientoCae = fechaVencimiento,
                resultado = "A",
                numeroComprobante = numeroComprobante,
                observaciones = observaciones,
                tipoOperacion = TipoOperacionAfip.CAE_ONLINE
            )
        }
        
        /**
         * Crea una respuesta aprobada para CAEA offline
         */
        fun createApprovedCAEA(
            caea: String,
            fechaVencimiento: LocalDate,
            numeroComprobante: Long,
            observaciones: List<String> = emptyList()
        ): AfipResponse {
            require(caea.isNotBlank()) { "El CAEA no puede estar vacío" }
            require(numeroComprobante > 0) { "El número de comprobante debe ser positivo" }
            
            return AfipResponse(
                cae = caea,
                fechaVencimientoCae = fechaVencimiento,
                resultado = "A",
                numeroComprobante = numeroComprobante,
                observaciones = observaciones,
                tipoOperacion = TipoOperacionAfip.CAEA_OFFLINE
            )
        }
        
        /**
         * Crea una respuesta rechazada
         */
        fun createRejected(
            observaciones: List<String>,
            tipoOperacion: TipoOperacionAfip = TipoOperacionAfip.CAE_ONLINE
        ): AfipResponse {
            require(observaciones.isNotEmpty()) { "Debe haber al menos una observación para un rechazo" }

            return AfipResponse(
                cae = "REJECTED",
                fechaVencimientoCae = LocalDate.now(),
                resultado = "R",
                numeroComprobante = 0,
                observaciones = observaciones,
                tipoOperacion = tipoOperacion
            )
        }

        /**
         * Crea una respuesta aprobada genérica
         */
        fun createApproved(
            observaciones: List<String>,
            tipoOperacion: TipoOperacionAfip
        ): AfipResponse {
            return AfipResponse(
                cae = "APPROVED",
                fechaVencimientoCae = LocalDate.now().plusDays(10),
                resultado = "A",
                numeroComprobante = 0L,
                observaciones = observaciones,
                tipoOperacion = tipoOperacion
            )
        }

        /**
         * Crea una respuesta aprobada con CAEA específico
         */
        fun createApprovedWithCAEA(
            caea: String,
            fechaVencimiento: LocalDate,
            numeroComprobante: Long,
            observaciones: List<String>,
            tipoOperacion: TipoOperacionAfip
        ): AfipResponse {
            return AfipResponse(
                cae = caea,
                fechaVencimientoCae = fechaVencimiento,
                resultado = "A",
                numeroComprobante = numeroComprobante,
                observaciones = observaciones,
                tipoOperacion = tipoOperacion
            )
        }
    }
}

/**
 * Enum para distinguir entre operaciones CAE y CAEA
 */
enum class TipoOperacionAfip {
    CAE_ONLINE,
    CAEA_OFFLINE,
    CAEA_SOLICITUD,
    CAEA_INFORMATIVO
}
