package com.gnico.majo.application.domain.model

import java.time.LocalDate
import java.time.LocalDateTime

/**
 * Modelo de dominio para códigos CAEA (Código de Autorización Electrónico Anticipado)
 * Los CAEA permiten generar comprobantes fiscales en modo offline
 */
data class CaeaCode(
    val id: Int? = null,
    val caea: String,
    val puntoVenta: Int,
    val periodo: String,
    val fechaDesde: LocalDate,
    val fechaHasta: LocalDate,
    val orden: Int,
    val estado: EstadoCaea,
    val ultimoNumeroFacturaB: Long = 0,
    val ultimoNumeroNotaCreditoB: Long = 0,
    val ultimoNumeroNotaDebitoB: Long = 0,
    val creadoEn: LocalDateTime = LocalDateTime.now(),
    val actualizadoEn: LocalDateTime = LocalDateTime.now()
) {
    
    /**
     * Verifica si el CAEA está activo y dentro del período de validez
     */
    fun isValid(fecha: LocalDate = LocalDate.now()): Boolean {
        return estado == EstadoCaea.ACTIVO && 
               fecha >= fechaDesde && 
               fecha <= fechaHasta
    }
    
    /**
     * Verifica si el CAEA está vencido
     */
    fun isExpired(fecha: LocalDate = LocalDate.now()): Boolean {
        return fecha > fechaHasta
    }

    /**
     * Verifica si el CAEA está vencido (alias para compatibilidad)
     */
    fun estaVencido(fecha: LocalDate = LocalDate.now()): Boolean {
        return isExpired(fecha)
    }

    /**
     * Verifica si el CAEA puede ser informado (dentro de los 8 días posteriores al vencimiento)
     */
    fun puedeInformarse(fecha: LocalDate = LocalDate.now()): Boolean {
        val fechaLimiteInforme = fechaHasta.plusDays(8)
        return estaVencido(fecha) && fecha <= fechaLimiteInforme
    }
    
    /**
     * Obtiene el último número usado para un tipo de comprobante específico
     */
    fun getLastNumberForType(tipoComprobante: String): Long {
        return when (tipoComprobante) {
            "FACTURA_B" -> ultimoNumeroFacturaB
            "NOTA_CREDITO_B" -> ultimoNumeroNotaCreditoB
            "NOTA_DEBITO_B" -> ultimoNumeroNotaDebitoB
            else -> throw IllegalArgumentException("Tipo de comprobante no soportado: $tipoComprobante")
        }
    }
    
    /**
     * Obtiene el siguiente número disponible para un tipo de comprobante
     */
    fun getNextNumberForType(tipoComprobante: String): Long {
        return getLastNumberForType(tipoComprobante) + 1
    }
    
    /**
     * Crea una copia con el número actualizado para un tipo de comprobante
     */
    fun withUpdatedNumber(tipoComprobante: String, numeroComprobante: Long): CaeaCode {
        return when (tipoComprobante) {
            "FACTURA_B" -> copy(
                ultimoNumeroFacturaB = numeroComprobante,
                actualizadoEn = LocalDateTime.now()
            )
            "NOTA_CREDITO_B" -> copy(
                ultimoNumeroNotaCreditoB = numeroComprobante,
                actualizadoEn = LocalDateTime.now()
            )
            "NOTA_DEBITO_B" -> copy(
                ultimoNumeroNotaDebitoB = numeroComprobante,
                actualizadoEn = LocalDateTime.now()
            )
            else -> throw IllegalArgumentException("Tipo de comprobante no soportado: $tipoComprobante")
        }
    }
    
    /**
     * Marca el CAEA como agotado
     */
    fun markAsExhausted(): CaeaCode {
        return copy(
            estado = EstadoCaea.AGOTADO,
            actualizadoEn = LocalDateTime.now()
        )
    }
    
    /**
     * Marca el CAEA como vencido
     */
    fun markAsExpired(): CaeaCode {
        return copy(
            estado = EstadoCaea.VENCIDO,
            actualizadoEn = LocalDateTime.now()
        )
    }

    /**
     * Marca el CAEA como informado a AFIP
     */
    fun markAsInformed(): CaeaCode {
        return copy(
            estado = EstadoCaea.INFORMADO,
            actualizadoEn = LocalDateTime.now()
        )
    }
    
    companion object {
        /**
         * Crea un nuevo CAEA
         */
        fun create(
            caea: String,
            puntoVenta: Int,
            periodo: String,
            fechaDesde: LocalDate,
            fechaHasta: LocalDate,
            orden: Int
        ): CaeaCode {
            require(caea.length == 14) { "CAEA debe tener 14 dígitos" }
            require(puntoVenta > 0) { "Punto de venta debe ser mayor a 0" }
            require(orden in 1..2) { "Orden debe ser 1 o 2" }
            require(fechaHasta >= fechaDesde) { "Fecha hasta debe ser mayor o igual a fecha desde" }
            require(periodo.matches(Regex("\\d{6}"))) { "Período debe tener formato YYYYMM" }
            
            return CaeaCode(
                caea = caea,
                puntoVenta = puntoVenta,
                periodo = periodo,
                fechaDesde = fechaDesde,
                fechaHasta = fechaHasta,
                orden = orden,
                estado = EstadoCaea.ACTIVO
            )
        }
    }
}

/**
 * Estados posibles de un CAEA
 */
enum class EstadoCaea {
    ACTIVO,     // Disponible para usar
    AGOTADO,    // Numeración agotada
    VENCIDO,    // Fuera del período de validez
    INFORMADO   // Ya fue informado a AFIP
}
