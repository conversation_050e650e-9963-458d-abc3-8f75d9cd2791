package com.gnico.majo.application.domain.model

import java.time.LocalDateTime

/**
 * Modelo de dominio para credenciales WSAA almacenadas en base de datos
 */
@ConsistentCopyVisibility
data class WsaaCredentials private constructor(
    val id: Id? = null,
    val servicio: String,
    val token: String,
    val sign: String,
    val fechaGeneracion: LocalDateTime,
    val fechaExpiracion: LocalDateTime,
    val estado: EstadoCredencial,
    val creadoEn: LocalDateTime = LocalDateTime.now()
) {
    
    /**
     * Verifica si las credenciales están vigentes
     */
    fun isValid(): Boolean {
        return estado == EstadoCredencial.ACTIVO && 
               fechaExpiracion.isAfter(LocalDateTime.now())
    }
    
    /**
     * Verifica si las credenciales están próximas a vencer (menos de 1 hora)
     */
    fun isNearExpiry(): Boolean {
        return fechaExpiracion.isBefore(LocalDateTime.now().plusHours(1))
    }
    
    /**
     * Verifica si las credenciales están expiradas
     */
    fun isExpired(): Boolean {
        return fechaExpiracion.isBefore(LocalDateTime.now()) || 
               estado == EstadoCredencial.EXPIRADO
    }
    
    /**
     * Convierte a AfipCredentials para uso en webservices
     */
    fun toAfipCredentials(cuit: Long): AfipCredentials {
        return AfipCredentials(
            token = token,
            sign = sign,
            cuit = cuit
        )
    }
    
    /**
     * Marca las credenciales como expiradas
     */
    fun markAsExpired(): WsaaCredentials {
        return copy(estado = EstadoCredencial.EXPIRADO)
    }
    
    /**
     * Obtiene el tiempo restante hasta la expiración en minutos
     */
    fun getMinutesUntilExpiry(): Long {
        val now = LocalDateTime.now()
        return if (fechaExpiracion.isAfter(now)) {
            java.time.Duration.between(now, fechaExpiracion).toMinutes()
        } else {
            0L
        }
    }
    
    companion object {
        /**
         * Crea nuevas credenciales WSAA
         */
        fun create(
            servicio: String,
            token: String,
            sign: String,
            fechaGeneracion: LocalDateTime = LocalDateTime.now(),
            fechaExpiracion: LocalDateTime
        ): WsaaCredentials {
            require(servicio.isNotBlank()) { "El servicio no puede estar vacío" }
            require(token.isNotBlank()) { "El token no puede estar vacío" }
            require(sign.isNotBlank()) { "El sign no puede estar vacío" }
            require(fechaExpiracion.isAfter(fechaGeneracion)) { 
                "La fecha de expiración debe ser posterior a la fecha de generación" 
            }
            
            return WsaaCredentials(
                servicio = servicio,
                token = token,
                sign = sign,
                fechaGeneracion = fechaGeneracion,
                fechaExpiracion = fechaExpiracion,
                estado = EstadoCredencial.ACTIVO
            )
        }
        
        /**
         * Crea credenciales desde respuesta de AFIP con duración estándar (12 horas)
         */
        fun fromAfipResponse(
            servicio: String,
            token: String,
            sign: String,
            duracionHoras: Long = 12L
        ): WsaaCredentials {
            val fechaGeneracion = LocalDateTime.now()
            val fechaExpiracion = fechaGeneracion.plusHours(duracionHoras)
            
            return create(
                servicio = servicio,
                token = token,
                sign = sign,
                fechaGeneracion = fechaGeneracion,
                fechaExpiracion = fechaExpiracion
            )
        }
        
        /**
         * Reconstruye desde base de datos
         */
        fun fromDatabase(
            id: Int,
            servicio: String,
            token: String,
            sign: String,
            fechaGeneracion: LocalDateTime,
            fechaExpiracion: LocalDateTime,
            estado: String,
            creadoEn: LocalDateTime
        ): WsaaCredentials {
            return WsaaCredentials(
                id = Id(id),
                servicio = servicio,
                token = token,
                sign = sign,
                fechaGeneracion = fechaGeneracion,
                fechaExpiracion = fechaExpiracion,
                estado = EstadoCredencial.fromString(estado),
                creadoEn = creadoEn
            )
        }
    }
}

/**
 * Estados posibles de las credenciales WSAA
 */
enum class EstadoCredencial(val value: String) {
    ACTIVO("ACTIVO"),
    EXPIRADO("EXPIRADO");
    
    companion object {
        fun fromString(value: String): EstadoCredencial {
            return values().find { it.value == value.uppercase() }
                ?: throw IllegalArgumentException("Estado de credencial inválido: $value")
        }
    }
}
