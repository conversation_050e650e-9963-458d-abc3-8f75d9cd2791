package com.gnico.majo.application.domain.model

import java.time.LocalDate
import java.time.format.DateTimeFormatter

/**
 * Modelos de dominio para webservices CAEA de AFIP
 */

/**
 * Solicitud para obtener un CAEA de AFIP
 */
data class AfipCAEARequest(
    val puntoVenta: Int,
    val periodo: String,
    val orden: Int,
    val credentials: AfipCredentials
) {
    init {
        require(puntoVenta > 0) { "El punto de venta debe ser positivo" }
        require(periodo.matches(Regex("\\d{6}"))) { "El período debe tener formato YYYYMM" }
        require(orden in 1..2) { "El orden debe ser 1 o 2" }
        require(credentials.isValid()) { "Las credenciales deben ser válidas" }
    }
    
    /**
     * Convierte la solicitud al formato requerido por el webservice AFIP
     */
    fun toAfipCAEARequestData(): AfipCAEARequestData {
        return AfipCAEARequestData(
            puntoVenta = puntoVenta,
            periodo = periodo,
            orden = orden
        )
    }
}

/**
 * Datos de solicitud CAEA para el webservice
 */
data class AfipCAEARequestData(
    val puntoVenta: Int,
    val periodo: String,
    val orden: Int
)

/**
 * Respuesta del webservice CAEA de AFIP
 */
data class WsfeCAEAResponse(
    val caea: String,
    val periodo: String,
    val orden: Int,
    val fechaDesde: String,
    val fechaHasta: String,
    val fechaTopeInforme: String,
    val resultado: String,
    val observaciones: List<String> = emptyList()
) {
    /**
     * Convierte la respuesta del webservice al modelo de dominio
     */
    fun toAfipResponse(): AfipResponse {
        return if (resultado == "A") {
            AfipResponse.createApprovedWithCAEA(
                caea = caea,
                fechaVencimiento = parseFechaVencimiento(fechaHasta),
                numeroComprobante = 0L,
                observaciones = observaciones.ifEmpty { listOf("CAEA obtenido exitosamente") },
                tipoOperacion = TipoOperacionAfip.CAEA_SOLICITUD
            )
        } else {
            AfipResponse.createRejected(
                observaciones = observaciones.ifEmpty { listOf("CAEA rechazado por AFIP") },
                TipoOperacionAfip.CAEA_SOLICITUD
            )
        }
    }
    
    private fun parseFechaVencimiento(fechaStr: String): LocalDate {
        return try {
            LocalDate.parse(fechaStr, DateTimeFormatter.ofPattern("yyyyMMdd"))
        } catch (e: Exception) {
            LocalDate.now().plusDays(15)
        }
    }
}

/**
 * Solicitud para informar movimientos sin CAEA
 */
data class AfipCAEASinMovimientoRequest(
    val caeaCode: String,
    val puntoVenta: Int,
    val credentials: AfipCredentials
) {
    init {
        require(caeaCode.length == 14) { "El código CAEA debe tener 14 dígitos" }
        require(puntoVenta > 0) { "El punto de venta debe ser positivo" }
        require(credentials.isValid()) { "Las credenciales deben ser válidas" }
    }
}

/**
 * Solicitud para informar movimientos con CAEA
 */
data class AfipCAEAMovimientosRequest(
    val caeaCode: String,
    val puntoVenta: Int,
    val comprobantes: List<ComprobanteCAEAMovimiento>,
    val credentials: AfipCredentials
) {
    init {
        require(caeaCode.length == 14) { "El código CAEA debe tener 14 dígitos" }
        require(puntoVenta > 0) { "El punto de venta debe ser positivo" }
        require(comprobantes.isNotEmpty()) { "Debe haber al menos un comprobante para informar" }
        require(credentials.isValid()) { "Las credenciales deben ser válidas" }
    }
}

/**
 * Información de comprobante para informar movimientos CAEA
 */
data class ComprobanteCAEAMovimiento(
    val tipoComprobante: TipoComprobanteAfip,
    val numeroComprobante: Long,
    val fechaEmision: LocalDate,
    val montoTotal: java.math.BigDecimal,
    val montoNeto: java.math.BigDecimal,
    val montoIva: java.math.BigDecimal,
    val montoTributos: java.math.BigDecimal,
    val montoTotalConceptos: java.math.BigDecimal,
    val montoExento: java.math.BigDecimal,
    val caeaUtilizado: String
) {
    init {
        require(numeroComprobante > 0) { "El número de comprobante debe ser positivo" }
        require(montoTotal >= java.math.BigDecimal.ZERO) { "El monto total no puede ser negativo" }
        require(caeaUtilizado.length == 14) { "El CAEA utilizado debe tener 14 dígitos" }
    }
    
    /**
     * Convierte al formato requerido por el webservice AFIP
     */
    fun toAfipMovimientoData(): AfipMovimientoData {
        return AfipMovimientoData(
            tipoComprobante = tipoComprobante.codigo,
            numeroComprobante = numeroComprobante,
            fechaEmision = fechaEmision.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
            montoTotal = montoTotal,
            caeaUtilizado = caeaUtilizado
        )
    }
}

/**
 * Datos de movimiento para el webservice
 */
data class AfipMovimientoData(
    val tipoComprobante: Int,
    val numeroComprobante: Long,
    val fechaEmision: String,
    val montoTotal: java.math.BigDecimal,
    val caeaUtilizado: String
)

/**
 * Respuesta del webservice de información de movimientos CAEA
 */
data class WsfeCAEAMovimientosResponse(
    val resultado: String,
    val observaciones: List<String> = emptyList(),
    val fechaProceso: String? = null
) {
    /**
     * Convierte la respuesta del webservice al modelo de dominio
     */
    fun toAfipResponse(): AfipResponse {
        return if (resultado == "A") {
            AfipResponse.createApproved(
                observaciones = observaciones.ifEmpty { listOf("Movimientos CAEA informados exitosamente") },
                TipoOperacionAfip.CAEA_INFORMATIVO
            )
        } else {
            AfipResponse.createRejected(
                observaciones = observaciones.ifEmpty { listOf("Error al informar movimientos CAEA") },
                TipoOperacionAfip.CAEA_INFORMATIVO
            )
        }
    }
}

/**
 * Solicitud de consulta CAEA
 */
data class AfipCAEAConsultaRequest(
    val puntoVenta: Int,
    val periodo: String,
    val orden: Int,
    val credentials: AfipCredentials
) {
    init {
        require(puntoVenta > 0) { "Punto de venta debe ser mayor a 0" }
        require(periodo.matches(Regex("\\d{6}"))) { "Período debe tener formato YYYYMM" }
        require(orden in 1..2) { "Orden debe ser 1 o 2" }
    }
}

/**
 * Respuesta de consulta CAEA del webservice
 */
data class WsfeCAEAConsultaResponse(
    val encontrado: Boolean,
    val caea: String?,
    val periodo: String,
    val orden: Int,
    val fechaDesde: String?,
    val fechaHasta: String?,
    val fechaTopeInforme: String?,
    val observaciones: List<String>
) {
    /**
     * Convierte a CaeaCode para guardar en BD
     */
    fun toCaeaCode(puntoVenta: Int): CaeaCode? {
        if (!encontrado || caea == null) return null

        return CaeaCode(
            id = null,
            caea = caea,
            puntoVenta = puntoVenta,
            periodo = periodo,
            fechaDesde = parseFecha(fechaDesde),
            fechaHasta = parseFecha(fechaHasta),
            orden = orden,
            estado = EstadoCaea.ACTIVO,
            ultimoNumeroFacturaB = 0L,
            ultimoNumeroNotaCreditoB = 0L,
            ultimoNumeroNotaDebitoB = 0L,
            creadoEn = java.time.LocalDateTime.now(),
            actualizadoEn = java.time.LocalDateTime.now()
        )
    }

    private fun parseFecha(fechaStr: String?): java.time.LocalDate {
        if (fechaStr.isNullOrBlank()) return java.time.LocalDate.now()

        return try {
            java.time.LocalDate.parse(fechaStr, java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"))
        } catch (e: Exception) {
            java.time.LocalDate.now()
        }
    }
}

/**
 * Tipos de operación AFIP extendidos para CAEA
 */
enum class TipoOperacionAfipCAEA(val descripcion: String) {
    CAEA_SOLICITUD("Solicitud de CAEA"),
    CAEA_SIN_MOVIMIENTO("Informe CAEA sin movimientos"),
    CAEA_CON_MOVIMIENTOS("Informe CAEA con movimientos")
}
