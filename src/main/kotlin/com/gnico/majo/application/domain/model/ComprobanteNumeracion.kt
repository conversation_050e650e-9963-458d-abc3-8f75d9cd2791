package com.gnico.majo.application.domain.model

import java.time.LocalDateTime

/**
 * Representa el tracking de numeración de comprobantes por punto de venta y tipo
 * Evita consultas a AFIP y conflictos de numeración
 */
data class ComprobanteNumeracion(
    val id: Id? = null,
    val puntoVenta: Int,
    val tipoComprobante: String,
    val ultimoNumero: Int,
    val creadoEn: LocalDateTime = LocalDateTime.now(),
    val actualizadoEn: LocalDateTime = LocalDateTime.now()
) {
    
    /**
     * Obtiene el siguiente número disponible para este punto de venta y tipo
     */
    fun siguienteNumero(): Int = ultimoNumero + 1
    
    /**
     * Actualiza con el nuevo último número emitido
     */
    fun conNuevoNumero(nuevoNumero: Int): ComprobanteNumeracion {
        require(nuevoNumero > ultimoNumero) { 
            "El nuevo número ($nuevoNumero) debe ser mayor al último número ($ultimoNumero)" 
        }
        
        return copy(
            ultimoNumero = nuevoNumero,
            actualizadoEn = LocalDateTime.now()
        )
    }
    
    companion object {
        /**
         * Crea una nueva numeración para un punto de venta y tipo específico
         */
        fun nueva(puntoVenta: Int, tipoComprobante: String, numeroInicial: Int = 0): ComprobanteNumeracion {
            return ComprobanteNumeracion(
                puntoVenta = puntoVenta,
                tipoComprobante = tipoComprobante,
                ultimoNumero = numeroInicial
            )
        }
        
        /**
         * Crea desde datos de persistencia
         */
        fun fromPersistence(
            id: Id,
            puntoVenta: Int,
            tipoComprobante: String,
            ultimoNumero: Int,
            creadoEn: LocalDateTime,
            actualizadoEn: LocalDateTime
        ): ComprobanteNumeracion {
            return ComprobanteNumeracion(
                id = id,
                puntoVenta = puntoVenta,
                tipoComprobante = tipoComprobante,
                ultimoNumero = ultimoNumero,
                creadoEn = creadoEn,
                actualizadoEn = actualizadoEn
            )
        }
    }
}
