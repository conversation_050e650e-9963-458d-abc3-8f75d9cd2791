package com.gnico.majo.application.domain.model

import java.math.BigDecimal
import java.time.LocalDate

/**
 * Modelos de dominio para los webservices de AFIP
 * Estos modelos encapsulan la lógica de negocio relacionada con AFIP
 */

/**
 * Credenciales de autenticación para AFIP
 */
data class AfipCredentials(
    val token: String,
    val sign: String,
    val cuit: Long
) {
    fun isValid(): Boolean = token.isNotBlank() && sign.isNotBlank() && cuit > 0
    
    fun isExpired(): Boolean {
        // TODO: Implementar lógica de verificación de expiración del token
        // Por ahora asumimos que es válido
        return false
    }
}

/**
 * Configuración para solicitud de CAE
 */
data class AfipCAERequest(
    val sale: Sale,
    val tipoComprobante: TipoComprobanteAfip,
    val puntoVenta: Int,
    val credentials: AfipCredentials,
    val condicionIvaReceptor: CondicionIvaReceptor? = null,
    val comprobanteAsociado: ComprobanteAsociado? = null
) {
    init {
        require(puntoVenta > 0) { "El punto de venta debe ser positivo" }
        require(credentials.isValid()) { "Las credenciales deben ser válidas" }

        // Validar que las notas de crédito y débito tengan comprobante asociado
        if (tipoComprobante in listOf(TipoComprobanteAfip.NOTA_CREDITO_B, TipoComprobanteAfip.NOTA_DEBITO_B)) {
            require(comprobanteAsociado != null) {
                "Las notas de crédito y débito requieren un comprobante asociado"
            }
            require(comprobanteAsociado.puntoVenta == puntoVenta) {
                "El punto de venta del comprobante asociado debe ser el mismo que el del comprobante actual"
            }
        }
    }

    /**
     * Obtiene la condición de IVA del receptor, usando la por defecto si no se especifica
     */
    fun obtenerCondicionIvaReceptor(): CondicionIvaReceptor =
        condicionIvaReceptor ?: CondicionIvaReceptor.getCondicionPorDefecto(tipoComprobante)

    /**
     * Convierte la venta a los datos requeridos por AFIP
     */
    fun toAfipInvoiceData(): AfipInvoiceData {
        val taxAmounts = sale.calculateTaxAmounts()

        // Solo manejamos comprobantes tipo B (consumidor final)
        // Siempre usar consumidor final sin identificar
        val docTipo = 99 // 99 = Sin identificar/Consumidor Final
        val docNro = 0L

        return AfipInvoiceData(
            concepto = 1, // Productos
            docTipo = docTipo,
            docNro = docNro,
            impTotal = taxAmounts.impTotal,
            impTotConc = taxAmounts.impTotConc,
            impNeto = taxAmounts.impNeto,
            impIva = taxAmounts.impIva,
            impTrib = taxAmounts.impTrib,
            monId = "PES",
            monCotiz = BigDecimal.ONE,
            ivaDetails = sale.getIvaDetails()
        )
    }
}

/**
 * Datos de factura para enviar a AFIP
 */
data class AfipInvoiceData(
    val concepto: Int,
    val docTipo: Int,
    val docNro: Long,
    val impTotal: BigDecimal,
    val impTotConc: BigDecimal,
    val impNeto: BigDecimal,
    val impIva: BigDecimal,
    val impTrib: BigDecimal,
    val monId: String,
    val monCotiz: BigDecimal,
    val ivaDetails: List<IvaDetail>
)

/**
 * Detalle de IVA para AFIP
 */
data class IvaDetail(
    val id: Int, // Código de alícuota IVA en AFIP
    val baseImponible: BigDecimal,
    val importe: BigDecimal
)

/**
 * Tipos de comprobante AFIP
 */
enum class TipoComprobanteAfip(val codigo: Int, val descripcion: String) {
    FACTURA_A(1, "Factura A"),
    FACTURA_B(6, "Factura B"),
    FACTURA_C(11, "Factura C"),
    NOTA_CREDITO_A(3, "Nota de Crédito A"),
    NOTA_CREDITO_B(8, "Nota de Crédito B"),
    NOTA_CREDITO_C(13, "Nota de Crédito C"),
    NOTA_DEBITO_A(2, "Nota de Débito A"),
    NOTA_DEBITO_B(7, "Nota de Débito B"),
    NOTA_DEBITO_C(12, "Nota de Débito C");

    companion object {
        fun fromString(tipo: String): TipoComprobanteAfip? = when (tipo.uppercase()) {
            "FACTURA_A" -> FACTURA_A
            "FACTURA_B" -> FACTURA_B
            "FACTURA_C" -> FACTURA_C
            "NOTA_CREDITO_A" -> NOTA_CREDITO_A
            "NOTA_CREDITO_B" -> NOTA_CREDITO_B
            "NOTA_CREDITO_C" -> NOTA_CREDITO_C
            "NOTA_DEBITO_A" -> NOTA_DEBITO_A
            "NOTA_DEBITO_B" -> NOTA_DEBITO_B
            "NOTA_DEBITO_C" -> NOTA_DEBITO_C
            else -> null
        }

        fun fromCodigo(codigo: Int): TipoComprobanteAfip? =
            entries.find { it.codigo == codigo }
    }
}

/**
 * Condiciones de IVA del receptor según AFIP
 * Obligatorio desde junio 2025 según Resolución General ARCA N° 5.616/2024
 */
enum class CondicionIvaReceptor(val codigo: Int, val descripcion: String, val tiposComprobante: String) {
    IVA_RESPONSABLE_INSCRIPTO(1, "IVA Responsable Inscripto", "A/M/C"),
    RESPONSABLE_MONOTRIBUTO(6, "Responsable Monotributo", "A/M/C"),
    MONOTRIBUTISTA_SOCIAL(13, "Monotributista Social", "A/M/C"),
    MONOTRIBUTO_TRABAJADOR_INDEPENDIENTE_PROMOVIDO(16, "Monotributo Trabajador Independiente Promovido", "A/M/C"),
    IVA_SUJETO_EXENTO(4, "IVA Sujeto Exento", "B/C"),
    CONSUMIDOR_FINAL(5, "Consumidor Final", "B/C"),
    SUJETO_NO_CATEGORIZADO(7, "Sujeto No Categorizado", "B/C"),
    PROVEEDOR_DEL_EXTERIOR(8, "Proveedor del Exterior", "B/C"),
    CLIENTE_DEL_EXTERIOR(9, "Cliente del Exterior", "B/C"),
    IVA_LIBERADO_LEY_19640(10, "IVA Liberado – Ley N° 19.640", "B/C"),
    IVA_NO_ALCANZADO(15, "IVA No Alcanzado", "B/C");

    companion object {
        /**
         * Obtiene la condición de IVA por código
         */
        fun fromCodigo(codigo: Int): CondicionIvaReceptor? =
            entries.find { it.codigo == codigo }

        /**
         * Determina la condición de IVA apropiada según el tipo de comprobante
         * Para facturas B y C se usa Consumidor Final por defecto
         * Para facturas A se debería consultar el padrón, pero por ahora usamos Responsable Monotributo
         */
        fun getCondicionPorDefecto(tipoComprobante: TipoComprobanteAfip): CondicionIvaReceptor = when (tipoComprobante) {
            TipoComprobanteAfip.FACTURA_A,
            TipoComprobanteAfip.NOTA_CREDITO_A,
            TipoComprobanteAfip.NOTA_DEBITO_A -> RESPONSABLE_MONOTRIBUTO

            TipoComprobanteAfip.FACTURA_B,
            TipoComprobanteAfip.FACTURA_C,
            TipoComprobanteAfip.NOTA_CREDITO_B,
            TipoComprobanteAfip.NOTA_CREDITO_C,
            TipoComprobanteAfip.NOTA_DEBITO_B,
            TipoComprobanteAfip.NOTA_DEBITO_C -> CONSUMIDOR_FINAL
        }
    }
}

/**
 * Respuesta cruda del webservice WSFE
 */
data class WsfeResponse(
    val cae: String,
    val caeFchVto: String,
    val resultado: String,
    val numeroComprobante: Long,
    val observaciones: List<String> = emptyList()
) {
    /**
     * Convierte la respuesta del webservice al modelo de dominio
     */
    fun toAfipResponse(): AfipResponse {
        return if (resultado == "A") {
            AfipResponse.createApprovedCAE(
                cae = cae,
                fechaVencimiento = parseFechaVencimiento(caeFchVto),
                numeroComprobante = numeroComprobante,
                observaciones = observaciones
            )
        } else {
            AfipResponse.createRejected(
                observaciones = observaciones.ifEmpty { listOf("Comprobante rechazado por AFIP") }
            )
        }
    }
    
    private fun parseFechaVencimiento(fechaStr: String): LocalDate {
        return try {
            LocalDate.parse(fechaStr, java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"))
        } catch (e: Exception) {
            LocalDate.now().plusDays(10)
        }
    }
}

/**
 * Configuración de AFIP
 */
data class AfipConfiguration(
    val cuit: Long,
    val certificatePath: String,
    val certificatePassword: String,
    val certificateAlias: String = "1",
    val isProduction: Boolean = false
) {
    fun getWsaaUrl(): String = if (isProduction) {
        "https://wsaa.afip.gov.ar/ws/services/LoginCms"
    } else {
        "https://wsaahomo.afip.gov.ar/ws/services/LoginCms"
    }
    
    fun getWsfeUrl(): String = if (isProduction) {
        "https://servicios1.afip.gov.ar/wsfev1/service.asmx"
    } else {
        "https://wswhomo.afip.gov.ar/wsfev1/service.asmx"
    }
}
