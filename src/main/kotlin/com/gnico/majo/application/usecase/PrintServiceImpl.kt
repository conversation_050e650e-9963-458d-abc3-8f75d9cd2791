package com.gnico.majo.application.usecase

import com.gnico.majo.application.port.`in`.PrintService
import com.gnico.majo.application.port.`in`.ReportService
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.ReportFilters
import com.gnico.majo.application.domain.model.SalesReportTicket
import com.gnico.majo.application.port.out.SaleRepositoryPort
import com.gnico.majo.application.port.out.PrinterPort

/**
 * Implementación simplificada del servicio de impresión
 *
 * Para búsquedas de ventas/comprobantes, usar SaleService:
 * - findSaleByNumero() - Buscar venta por número
 * - findSalesWithFilters() - Buscar ventas con filtros
 * - findSaleById() - Buscar venta por ID
 */
class PrintServiceImpl(
    private val saleRepository: SaleRepositoryPort,
    private val printer: PrinterPort,
    private val reportService: ReportService
) : PrintService {
    

    override fun imprimirTicket(ventaId: Id, comprobanteId: Id?) {
        val sale = saleRepository.findSaleById(ventaId)
            ?: throw IllegalArgumentException("Venta ${ventaId.value} no encontrada")

        val comprobante = if (comprobanteId != null) {
            saleRepository.findComprobanteById(comprobanteId)
                ?: throw IllegalArgumentException("Comprobante ${comprobanteId.value} no encontrado")
        } else {
            null
        }

        printer.printTicket(comprobante, sale)
    }

    override suspend fun imprimirReporteVentas(filters: ReportFilters) {
        val salesReport = reportService.getSalesReport(filters)
        val reportTicket = SalesReportTicket.fromSalesReport(salesReport)
        printer.printSalesReport(reportTicket)
    }
}
