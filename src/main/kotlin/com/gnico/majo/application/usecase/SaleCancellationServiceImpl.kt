package com.gnico.majo.application.usecase

import com.gnico.majo.application.port.`in`.SaleCancellationService
import com.gnico.majo.application.port.`in`.SaleCancellationResult
import com.gnico.majo.application.port.`in`.CancelledSaleInfo
import com.gnico.majo.application.port.`in`.CancellationStats
import com.gnico.majo.application.port.`in`.ComprobanteService
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.port.out.SaleRepositoryPort
import com.gnico.majo.application.port.out.UsuarioRepository
import java.text.DecimalFormat
import java.time.format.DateTimeFormatter

class SaleCancellationServiceImpl(
    private val saleRepository: SaleRepositoryPort,
    private val usuarioRepository: UsuarioRepository,
    private val comprobanteService: ComprobanteService
) : SaleCancellationService {

    private val dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")
    private val decimalFormat = DecimalFormat("#,##0.00")

    override suspend fun cancelSale(
        ventaId: Id,
        usuarioCancelacion: String,
        motivo: String,
        generarNotaCreditoOnline: Boolean
    ): SaleCancellationResult {
        try {
            // Validar que el usuario existe
            val usuario = usuarioRepository.findByUsername(usuarioCancelacion)
                ?: return SaleCancellationResult(
                    success = false,
                    message = "Usuario '$usuarioCancelacion' no encontrado",
                    ventaId = ventaId.value,
                    numeroVenta = "",
                    error = "Usuario no encontrado"
                )

            // Buscar la venta
            val sale = saleRepository.findSaleById(ventaId)
                ?: return SaleCancellationResult(
                    success = false,
                    message = "Venta ${ventaId.value} no encontrada",
                    ventaId = ventaId.value,
                    numeroVenta = "",
                    error = "Venta no encontrada"
                )

            // Validar que la venta puede ser cancelada
            if (!sale.canBeCancelled()) {
                return SaleCancellationResult(
                    success = false,
                    message = "La venta ${sale.numeroVenta} ya está cancelada",
                    ventaId = ventaId.value,
                    numeroVenta = sale.numeroVenta,
                    error = "Venta ya cancelada"
                )
            }

            // Cancelar la venta en la base de datos
            val cancelacionExitosa = saleRepository.cancelSale(ventaId, usuarioCancelacion, motivo)
            if (!cancelacionExitosa) {
                return SaleCancellationResult(
                    success = false,
                    message = "Error al cancelar la venta ${sale.numeroVenta}",
                    ventaId = ventaId.value,
                    numeroVenta = sale.numeroVenta,
                    error = "Error en base de datos"
                )
            }

            // Si la venta tenía comprobante emitido, generar nota de crédito B
            var notaCreditoId: Id? = null
            var notaCreditoNumero: String? = null
            var notaCreditoGenerada = false

            if (sale.comprobanteEmitido) {
                try {
                    // Determinar el modo de generación basándose en el parámetro del usuario
                    // Si es true -> ONLINE, si es false -> OFFLINE, pero el sistema validará
                    // que sea consistente con el comprobante original
                    val modoGeneracion = if (generarNotaCreditoOnline) "ONLINE" else "OFFLINE"

                    notaCreditoId = comprobanteService.generarComprobanteAuto(
                        ventaId = ventaId,
                        tipoComprobante = "NOTA_CREDITO_B",
                        modoGeneracion = modoGeneracion
                    )

                    // Obtener información del comprobante generado
                    val comprobante = saleRepository.findComprobanteById(notaCreditoId)
                    notaCreditoNumero = comprobante?.let { "${it.puntoVenta}-${it.numeroComprobante}" }
                    notaCreditoGenerada = true

                    // Marcar en la base de datos que se generó la nota de crédito
                    val marcadoExitoso = saleRepository.markNotaCreditoGenerada(ventaId)
                    if (!marcadoExitoso) {
                        println("⚠️ Advertencia: No se pudo marcar nota_credito_generada para venta ${ventaId.value}")
                    }

                    println("✅ Nota de crédito B generada: ID=${notaCreditoId.value}, Número=$notaCreditoNumero")

                } catch (e: Exception) {
                    println("❌ Error al generar nota de crédito para venta ${sale.numeroVenta}: ${e.message}")
                    // La cancelación ya se realizó, pero falló la nota de crédito
                    return SaleCancellationResult(
                        success = true,
                        message = "Venta cancelada pero falló la generación de nota de crédito: ${e.message}",
                        ventaId = ventaId.value,
                        numeroVenta = sale.numeroVenta,
                        notaCreditoGenerada = false,
                        error = "Error en nota de crédito: ${e.message}"
                    )
                }
            }

            println("✅ Venta ${sale.numeroVenta} cancelada exitosamente por $usuarioCancelacion")

            return SaleCancellationResult(
                success = true,
                message = if (notaCreditoGenerada) {
                    "Venta cancelada y nota de crédito B generada exitosamente"
                } else {
                    "Venta cancelada exitosamente"
                },
                ventaId = ventaId.value,
                numeroVenta = sale.numeroVenta,
                notaCreditoGenerada = notaCreditoGenerada,
                notaCreditoId = notaCreditoId?.value,
                notaCreditoNumero = notaCreditoNumero
            )

        } catch (e: Exception) {
            println("❌ Error inesperado al cancelar venta ${ventaId.value}: ${e.message}")
            e.printStackTrace()
            return SaleCancellationResult(
                success = false,
                message = "Error inesperado al cancelar la venta",
                ventaId = ventaId.value,
                numeroVenta = "",
                error = e.message
            )
        }
    }

    override fun getCancelledSalesHistory(limit: Int): List<CancelledSaleInfo> {
        return try {
            val cancelledSales = saleRepository.findCancelledSales(limit)
            
            cancelledSales.map { sale ->
                // Buscar si tiene nota de crédito asociada
                val comprobantes = saleRepository.findComprobantesByVentaId(sale.id!!)
                val notaCredito = comprobantes.find { it.tipoComprobante == "NOTA_CREDITO_B" }
                
                CancelledSaleInfo(
                    ventaId = sale.id.value,
                    numeroVenta = sale.numeroVenta,
                    fechaVenta = sale.fechaVenta.format(dateFormatter),
                    fechaCancelacion = sale.fechaCancelacion?.format(dateFormatter) ?: "",
                    usuarioVenta = sale.usuario.nombreDisplay,
                    usuarioCancelacion = sale.usuarioCancelacion ?: "",
                    motivoCancelacion = sale.motivoCancelacion ?: "",
                    montoTotal = decimalFormat.format(sale.montoTotal),
                    teniaComprobante = sale.comprobanteEmitido,
                    notaCreditoGenerada = notaCredito != null,
                    notaCreditoNumero = notaCredito?.let { "${it.puntoVenta}-${it.numeroComprobante}" }
                )
            }
        } catch (e: Exception) {
            println("❌ Error al obtener historial de cancelaciones: ${e.message}")
            emptyList()
        }
    }

    override fun getCancellationStats(): CancellationStats {
        return try {
            val totalCancelaciones = saleRepository.countCancelledSales()
            val cancelledSales = saleRepository.findCancelledSales(1000) // Obtener todas para estadísticas
            
            val conNotaCredito = cancelledSales.count { sale ->
                val comprobantes = saleRepository.findComprobantesByVentaId(sale.id!!)
                comprobantes.any { it.tipoComprobante == "NOTA_CREDITO_B" }
            }
            
            CancellationStats(
                totalCancelaciones = totalCancelaciones,
                cancelacionesConNotaCredito = conNotaCredito,
                cancelacionesSinNotaCredito = totalCancelaciones - conNotaCredito
            )
        } catch (e: Exception) {
            println("❌ Error al obtener estadísticas de cancelaciones: ${e.message}")
            CancellationStats(0, 0, 0)
        }
    }

    override fun getSalesPendingNotaCredito(limit: Int): List<CancelledSaleInfo> {
        return try {
            val pendingSales = saleRepository.findSalesPendingNotaCredito(limit)

            pendingSales.map { sale ->
                CancelledSaleInfo(
                    ventaId = sale.id!!.value,
                    numeroVenta = sale.numeroVenta,
                    fechaVenta = sale.fechaVenta.format(dateFormatter),
                    fechaCancelacion = sale.fechaCancelacion?.format(dateFormatter) ?: "",
                    usuarioVenta = sale.usuario.nombreDisplay,
                    usuarioCancelacion = sale.usuarioCancelacion ?: "",
                    motivoCancelacion = sale.motivoCancelacion ?: "",
                    montoTotal = decimalFormat.format(sale.montoTotal),
                    teniaComprobante = sale.comprobanteEmitido,
                    notaCreditoGenerada = sale.notaCreditoGenerada,
                    notaCreditoNumero = null // Estas ventas no tienen nota de crédito aún
                )
            }
        } catch (e: Exception) {
            println("❌ Error al obtener ventas pendientes de nota de crédito: ${e.message}")
            emptyList()
        }
    }

    override fun countSalesPendingNotaCredito(): Int {
        return try {
            saleRepository.countSalesPendingNotaCredito()
        } catch (e: Exception) {
            println("❌ Error al contar ventas pendientes de nota de crédito: ${e.message}")
            0
        }
    }
}
