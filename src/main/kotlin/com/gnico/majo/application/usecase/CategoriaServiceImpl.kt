package com.gnico.majo.application.usecase

import com.gnico.majo.application.port.`in`.CategoriaService
import com.gnico.majo.application.port.out.CategoriaRepositoryPort
import com.gnico.majo.application.domain.model.Categoria
import com.gnico.majo.application.domain.model.Id

/**
 * Implementación del servicio de categorías
 */
class CategoriaServiceImpl(
    private val categoriaRepository: CategoriaRepositoryPort
) : CategoriaService {

    override fun getAllCategorias(): List<Categoria> {
        return categoriaRepository.findAll()
    }

    override fun getCategoriaById(id: Id): Categoria? {
        return categoriaRepository.findById(id)
    }

    override fun createCategoria(categoria: Categoria): Id {
        // Verificar que no se intente crear otra categoría con el nombre especial
        if (categoria.esCategoriaEspecialSinCategoria()) {
            throw IllegalArgumentException("No se puede crear manualmente la categoría especial '${Categoria.SIN_CATEGORIA_NOMBRE}'")
        }

        // Verificar que el nombre no exista
        categoriaRepository.findByNombre(categoria.nombre)?.let {
            throw IllegalArgumentException("Ya existe una categoría con el nombre '${categoria.nombre}'")
        }

        // Crear la categoría con timestamp
        val categoriaToSave = categoria.withCreationTimestamp()
        return categoriaRepository.save(categoriaToSave)
    }

    override fun updateCategoria(categoria: Categoria): Boolean {
        // Verificar que la categoría exista
        val existingCategoria = categoria.id?.let { categoriaRepository.findById(it) }
            ?: throw IllegalArgumentException("No existe una categoría con el ID ${categoria.id}")

        // Verificar que el nombre no esté en uso por otra categoría
        categoriaRepository.findByNombre(categoria.nombre)?.let { existing ->
            if (existing.id != categoria.id) {
                throw IllegalArgumentException("Ya existe otra categoría con el nombre '${categoria.nombre}'")
            }
        }

        return categoriaRepository.update(categoria)
    }

    override fun deleteCategoria(id: Id): Boolean {
        // Verificar que la categoría exista
        val categoria = categoriaRepository.findById(id)
            ?: throw IllegalArgumentException("No existe una categoría con el ID $id")

        // Verificar que no sea la categoría especial "Sin Categoría"
        if (categoria.esCategoriaEspecialSinCategoria()) {
            throw IllegalArgumentException("No se puede eliminar la categoría especial '${Categoria.SIN_CATEGORIA_NOMBRE}'")
        }

        return categoriaRepository.delete(id)
    }

    override fun updateOrdenCategorias(ordenMap: Map<Int, Int>): Int {
        if (ordenMap.isEmpty()) return 0

        // Validar que todas las categorías existan y estén activas
        ordenMap.keys.forEach { categoriaId ->
            categoriaRepository.findById(Id(categoriaId))
                ?: throw IllegalArgumentException("No existe una categoría con el ID $categoriaId")
        }

        return categoriaRepository.updateOrdenMultiple(ordenMap)
    }

    override fun getSinCategoriaEspecial(): Categoria? {
        return categoriaRepository.findSinCategoriaEspecial()
    }
}
