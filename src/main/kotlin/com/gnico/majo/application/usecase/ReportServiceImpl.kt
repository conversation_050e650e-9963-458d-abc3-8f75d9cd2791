package com.gnico.majo.application.usecase

import com.gnico.majo.application.port.`in`.ReportService
import com.gnico.majo.application.port.out.SalesReportPort
import com.gnico.majo.application.domain.model.*
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

/**
 * Implementación del servicio de reportes
 */
class ReportServiceImpl(
    private val salesReportPort: SalesReportPort
) : ReportService {

    override suspend fun getSalesReport(filters: ReportFilters): SalesReport {
        validateReportFilters(filters)
        return salesReportPort.getSalesReport(filters)
    }





    override suspend fun getComparativeReport(
        currentPeriod: ReportPeriod,
        previousPeriod: ReportPeriod,
        filters: ReportFilters?
    ): ComparativeSummary {
        val baseFilters = filters ?: ReportFilters(
            periodo = currentPeriod
        )

        return salesReportPort.getComparativeSummary(
            currentPeriod = currentPeriod,
            previousPeriod = previousPeriod,
            filters = baseFilters
        )
    }





    override fun validateReportFilters(filters: ReportFilters) {
        // Validar que el período no sea muy extenso (máximo 1 año)
        val daysBetween = ChronoUnit.DAYS.between(
            filters.periodo.desde.toLocalDate(),
            filters.periodo.hasta.toLocalDate()
        )
        
        if (daysBetween > 365) {
            throw IllegalArgumentException("El período del reporte no puede exceder 1 año")
        }

        // Validar que la fecha desde no sea futura
        if (filters.periodo.desde.isAfter(LocalDateTime.now())) {
            throw IllegalArgumentException("La fecha desde no puede ser futura")
        }

        // Validar usuarios si se especifican
        filters.usuarios?.forEach { username ->
            if (username.isBlank()) {
                throw IllegalArgumentException("Los nombres de usuario no pueden estar vacíos")
            }
        }

        // Validar medios de pago si se especifican
        filters.mediosPago?.forEach { medio ->
            try {
                MedioPago.fromString(medio)
                    ?: throw IllegalArgumentException("Medio de pago '$medio' no válido")
            } catch (e: Exception) {
                throw IllegalArgumentException("Medio de pago '$medio' no válido", e)
            }
        }

        // Validar combinación de filtros
        if (filters.soloMediosPagoElectronicos && filters.mediosPago?.contains("EFECTIVO") == true) {
            throw IllegalArgumentException("No se puede filtrar por medios electrónicos y efectivo al mismo tiempo")
        }
    }

    /**
     * Métodos auxiliares para cálculos comunes
     */
    private fun calculatePreviousPeriod(period: ReportPeriod): ReportPeriod {
        val duration = ChronoUnit.DAYS.between(period.desde.toLocalDate(), period.hasta.toLocalDate())
        val previousStart = period.desde.minusDays(duration + 1)
        val previousEnd = period.desde.minusDays(1)
        
        return ReportPeriod.custom(previousStart, previousEnd)
    }

    /**
     * Obtiene el período anterior equivalente para comparativas
     */
    suspend fun getComparativeWithPreviousPeriod(
        currentPeriod: ReportPeriod,
        filters: ReportFilters? = null
    ): ComparativeSummary {
        val previousPeriod = calculatePreviousPeriod(currentPeriod)
        return getComparativeReport(currentPeriod, previousPeriod, filters)
    }

    /**
     * Obtiene reporte con comparativa automática del día anterior
     */
    suspend fun getDailyReportWithComparative(): SalesReport {
        val todayFilters = ReportFilters(
            periodo = ReportPeriod.today()
        )
        val todayReport = getSalesReport(todayFilters)

        // Agregar comparativa con ayer si es posible
        val yesterday = ReportPeriod.custom(
            LocalDateTime.now().minusDays(1).toLocalDate().atStartOfDay(),
            LocalDateTime.now().minusDays(1).toLocalDate().atTime(23, 59, 59)
        )

        return todayReport
    }

    /**
     * Obtiene alertas críticas del sistema
     */
    suspend fun getCriticalAlerts(): ReportAlerts {
        val todayFilters = ReportFilters(
            periodo = ReportPeriod.today()
        )
        
        return salesReportPort.getReportAlerts(todayFilters)
    }

    /**
     * Obtiene métricas de performance de vendedores
     */
    suspend fun getSellerPerformanceMetrics(period: ReportPeriod): List<SellerBreakdown> {
        val filters = ReportFilters(
            periodo = period
        )
        
        return salesReportPort.getSellerBreakdown(filters)
            .sortedWith(
                compareByDescending<SellerBreakdown> { it.montoTotal }
                    .thenByDescending { it.cantidadVentas }
            )
    }

    /**
     * Obtiene estadísticas de medios de pago electrónicos
     */
    suspend fun getElectronicPaymentStats(period: ReportPeriod): List<PaymentMethodBreakdown> {
        val filters = ReportFilters(
            periodo = period,
            soloMediosPagoElectronicos = true
        )
        
        return salesReportPort.getPaymentMethodBreakdown(filters)
            .filter { it.esElectronico }
            .sortedByDescending { it.montoTotal }
    }
}
