package com.gnico.majo.application.usecase

import com.gnico.majo.application.domain.model.Usuario
import com.gnico.majo.application.port.`in`.UsuarioService
import com.gnico.majo.application.port.out.UsuarioRepository
import com.gnico.majo.application.port.out.SaleRepositoryPort


class UsuarioServiceImpl(
    private val usuarioRepository: UsuarioRepository,
    private val saleRepository: SaleRepositoryPort
) : UsuarioService {

    override suspend fun getAllActiveUsers(): List<Usuario> {
        return usuarioRepository.findAllActive()
    }

    override suspend fun getAllUsers(): List<Usuario> {
        return usuarioRepository.findAll()
    }

    override suspend fun getUserByUsername(username: String): Usua<PERSON>? {
        require(username.isNotBlank()) { "Username no puede estar vacío" }
        return usuarioRepository.findByUsername(username)
    }

    override suspend fun createUser(
        username: String,
        nombre: String,
        nombreDisplay: String,
        activo: Boolean
    ): String {
        // Validar que el usuario no exista
        val existingUser = usuarioRepository.findByUsername(username)
        if (existingUser != null) {
            throw IllegalArgumentException("Ya existe un usuario con username '$username'")
        }

        val usuario = Usuario.create(
            username = username,
            nombre = nombre,
            nombreDisplay = nombreDisplay,
            activo = activo
        )

        return usuarioRepository.save(usuario)
    }

    override suspend fun updateUser(usuario: Usuario): Boolean {
        // Verificar que el usuario existe
        val existingUser = usuarioRepository.findByUsername(usuario.username)
            ?: throw IllegalArgumentException("Usuario '${usuario.username}' no encontrado")

        return usuarioRepository.update(usuario)
    }

    override suspend fun deleteUser(username: String): Boolean {
        require(username.isNotBlank()) { "Username no puede estar vacío" }

        // Verificar que el usuario existe
        val existingUser = usuarioRepository.findByUsername(username)
            ?: throw IllegalArgumentException("Usuario '$username' no encontrado")

        // Verificar que el usuario no tiene ventas asociadas
        if (saleRepository.hasUserAnySales(username)) {
            throw IllegalArgumentException("No se puede eliminar el usuario '$username' porque tiene ventas asociadas. Para mantener la integridad de los datos, los usuarios con ventas no pueden ser eliminados.")
        }

        return usuarioRepository.delete(username)
    }
    
}
