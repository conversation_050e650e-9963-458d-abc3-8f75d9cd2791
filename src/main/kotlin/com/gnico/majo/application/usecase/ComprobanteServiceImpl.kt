package com.gnico.majo.application.usecase

import com.gnico.majo.application.port.`in`.ComprobanteService
import com.gnico.majo.application.port.`in`.ComprobanteInfo
import com.gnico.majo.application.port.`in`.ComprobanteAttemptInfo
import com.gnico.majo.application.port.`in`.VentaSinComprobanteInfo
import com.gnico.majo.application.domain.model.Comprobante
import com.gnico.majo.application.domain.model.ComprobanteAsociado
import com.gnico.majo.application.domain.model.ComprobanteAttempt
import com.gnico.majo.application.domain.model.TipoOperacionAfip
import com.gnico.majo.application.domain.model.TipoAutorizacion
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Sale
import com.gnico.majo.application.port.out.SaleRepositoryPort
import com.gnico.majo.application.port.out.AfipService
import com.gnico.majo.application.port.out.ComprobanteNumeracionRepositoryPort
import com.gnico.majo.application.domain.model.AfipResponse
import com.gnico.majo.infrastructure.config.ComprobanteConfigurationService
import org.slf4j.LoggerFactory
import java.time.format.DateTimeFormatter
import java.text.DecimalFormat
import kotlin.system.measureTimeMillis

/**
 * Estrategia para generar comprobantes que encapsula las diferencias entre CAE y CAEA
 */
private enum class ComprobanteGenerationStrategy(
    val tipoAutorizacion: TipoAutorizacion,
    val tipoOperacion: TipoOperacionAfip
) {
    ONLINE(TipoAutorizacion.CAE, TipoOperacionAfip.CAE_ONLINE) {
        override suspend fun obtenerAutorizacion(
            afipService: AfipService,
            sale: Sale,
            tipoComprobante: String,
            puntoVenta: Int,
            comprobanteAsociado: ComprobanteAsociado?
        ): AfipResponse {
            return afipService.solicitarCAE(sale, tipoComprobante, puntoVenta, comprobanteAsociado)
        }
    },

    OFFLINE(TipoAutorizacion.CAEA, TipoOperacionAfip.CAEA_OFFLINE) {
        override suspend fun obtenerAutorizacion(
            afipService: AfipService,
            sale: Sale,
            tipoComprobante: String,
            puntoVenta: Int,
            comprobanteAsociado: ComprobanteAsociado?
        ): AfipResponse {
            return afipService.crearComprobanteConCAEA(sale, tipoComprobante, puntoVenta, comprobanteAsociado)
        }
    };

    abstract suspend fun obtenerAutorizacion(
        afipService: AfipService,
        sale: Sale,
        tipoComprobante: String,
        puntoVenta: Int,
        comprobanteAsociado: ComprobanteAsociado?
    ): AfipResponse
}

class ComprobanteServiceImpl(
    private val saleRepository: SaleRepositoryPort,
    private val afipService: AfipService,
    private val numeracionRepository: ComprobanteNumeracionRepositoryPort
) : ComprobanteService {

    private val logger = LoggerFactory.getLogger(ComprobanteServiceImpl::class.java)
    private val dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")
    private val decimalFormatter = DecimalFormat("#,##0.00")

    override suspend fun generarComprobanteOnline(
        ventaId: Id,
        tipoComprobante: String?,
        puntoVenta: Int?
    ): Id {
        return generarComprobante(
            ventaId = ventaId,
            tipoComprobante = tipoComprobante,
            puntoVenta = puntoVenta,
            strategy = ComprobanteGenerationStrategy.ONLINE
        )
    }

    override suspend fun generarComprobanteOffline(
        ventaId: Id,
        tipoComprobante: String?,
        puntoVenta: Int?
    ): Id {
        return generarComprobante(
            ventaId = ventaId,
            tipoComprobante = tipoComprobante,
            puntoVenta = puntoVenta,
            strategy = ComprobanteGenerationStrategy.OFFLINE
        )
    }

    override suspend fun generarComprobanteAuto(
        ventaId: Id,
        tipoComprobante: String?,
        modoGeneracion: String?
    ): Id {
        // Resolver configuración con valores por defecto
        val config = ComprobanteConfigurationService.resolveConfig(
            puntoVenta = null, // Siempre usar valor del .env
            tipoComprobante = tipoComprobante
        )

        // Determinar la estrategia basándose en el modo de generación
        val strategy = determineGenerationStrategy(ventaId, config.tipoComprobante, modoGeneracion)

        return generarComprobante(
            ventaId = ventaId,
            tipoComprobante = tipoComprobante,
            puntoVenta = null, // Siempre usar valor del .env
            strategy = strategy
        )
    }

    /**
     * Método privado que contiene la lógica común para generar comprobantes
     * tanto online (CAE) como offline (CAEA)
     */
    private suspend fun generarComprobante(
        ventaId: Id,
        tipoComprobante: String?,
        puntoVenta: Int?,
        strategy: ComprobanteGenerationStrategy
    ): Id {
        // Resolver configuración con valores por defecto
        val config = ComprobanteConfigurationService.resolveConfig(
            puntoVenta = puntoVenta,
            tipoComprobante = tipoComprobante
        )

        // Validar que la venta existe
        val sale = saleRepository.findSaleById(ventaId)
            ?: throw IllegalArgumentException("Venta ${ventaId.value} no encontrada")

        // Validar estado del comprobante según el tipo
        validateComprobanteState(sale, config.tipoComprobante)

        // Determinar el punto de venta correcto según la estrategia
        val puntoVentaEfectivo = when (strategy) {
            ComprobanteGenerationStrategy.ONLINE -> config.puntoVenta
            ComprobanteGenerationStrategy.OFFLINE -> {
                // Para comprobantes offline, usar punto de venta offline configurado
                if (config.puntoVenta == ComprobanteConfigurationService.getDefaultPuntoVenta()) {
                    ComprobanteConfigurationService.getDefaultPuntoVentaOffline()
                } else {
                    config.puntoVenta
                }
            }
        }

        // Buscar comprobante asociado si es requerido (notas de crédito/débito)
        // Para notas de crédito que anulan facturas, usamos exactamente los mismos importes
        // de la factura original para evitar rechazos de AFIP por diferencias de redondeo
        val (comprobanteAsociado, comprobanteOriginal) = findComprobanteAsociadoIfRequired(sale, config.tipoComprobante, puntoVentaEfectivo)

        // Obtener el siguiente número de comprobante según la estrategia
        val numeroComprobante = when (strategy) {
            ComprobanteGenerationStrategy.ONLINE -> {
                // Para comprobantes online (CAE), consultar a AFIP
                obtenerSiguienteNumeroDeAfip(puntoVentaEfectivo, config.tipoComprobante)
            }
            ComprobanteGenerationStrategy.OFFLINE -> {
                // Para comprobantes offline (CAEA), usar numeración interna
                // El número real será asignado por el AfipServiceAdapter.crearComprobanteConCAEA
                // Aquí usamos un placeholder que será reemplazado
                0 // Placeholder - será reemplazado por el número real del CAEA
            }
        }

        // Registrar intento y medir tiempo de respuesta
        var tiempoRespuesta: Long = 0
        val afipResponse: AfipResponse = try {
            var response: AfipResponse? = null
            tiempoRespuesta = measureTimeMillis {
                response = strategy.obtenerAutorizacion(
                    afipService = afipService,
                    sale = sale,
                    tipoComprobante = config.tipoComprobante,
                    puntoVenta = puntoVentaEfectivo,
                    comprobanteAsociado = comprobanteAsociado
                )
            }
            response!!
        } catch (e: Exception) {
            // Registrar intento fallido por error de sistema
            val attemptFallido = ComprobanteAttempt.createFallidoSistema(
                ventaId = ventaId,
                tipoComprobante = config.tipoComprobante,
                puntoVenta = config.puntoVenta,
                tipoOperacion = strategy.tipoOperacion,
                codigoError = "SYSTEM_ERROR",
                mensajeError = "Error de sistema: ${e.message}",
                tiempoRespuestaMs = tiempoRespuesta.toInt()
            )
            saleRepository.saveComprobanteAttempt(attemptFallido)
            throw e
        }

        // Verificar que la respuesta sea exitosa
        if (!afipResponse.isApproved()) {
            // Registrar intento fallido por rechazo de AFIP
            val attemptFallido = ComprobanteAttempt.createFallidoAfip(
                ventaId = ventaId,
                tipoComprobante = config.tipoComprobante,
                puntoVenta = puntoVentaEfectivo,
                afipResponse = afipResponse,
                tiempoRespuestaMs = tiempoRespuesta.toInt()
            )
            saleRepository.saveComprobanteAttempt(attemptFallido)
            val modoOperacion = if (strategy == ComprobanteGenerationStrategy.ONLINE) "online" else "offline"
            throw IllegalStateException("Error al generar comprobante $modoOperacion: ${afipResponse.getObservacionesResumen()}")
        }

        // Determinar el número de comprobante final
        val numeroComprobanteFinal = when (strategy) {
            ComprobanteGenerationStrategy.ONLINE -> numeroComprobante
            ComprobanteGenerationStrategy.OFFLINE -> {
                // Para CAEA, usar el número que viene de la respuesta AFIP
                afipResponse.numeroComprobante?.toInt() ?: throw IllegalStateException("Respuesta AFIP no contiene número de comprobante para CAEA")
            }
        }

        // Crear y guardar el comprobante
        val comprobante = Comprobante.createFromSale(
            sale = sale,
            tipoComprobante = config.tipoComprobante,
            puntoVenta = puntoVentaEfectivo,
            ventaId = ventaId,
            numeroComprobante = numeroComprobanteFinal,
            tipoAutorizacion = strategy.tipoAutorizacion,
            comprobanteAsociado = comprobanteAsociado,
            comprobanteOriginal = comprobanteOriginal
        ).let { baseComprobante ->
            // Para comprobantes offline (CAEA), usar withCAEA para asignar el código CAEA por separado
            if (strategy == ComprobanteGenerationStrategy.OFFLINE && afipResponse.isOfflineOperation()) {
                baseComprobante.withCAEA(
                    cae = afipResponse.cae,
                    estado = afipResponse.getEstadoDescriptivo(),
                    caeaUtilizado = afipResponse.cae // El CAEA está en el campo cae de la respuesta
                )
            } else {
                // Para comprobantes online (CAE), usar withCAE normal
                baseComprobante.withCAE(
                    cae = afipResponse.cae,
                    estado = afipResponse.getEstadoDescriptivo()
                )
            }
        }

        val comprobanteId = saleRepository.saveComprobante(comprobante)

        // Actualizar la numeración local para cache/monitoreo (opcional)
        val numeroComprobanteAfip = afipResponse.numeroComprobante ?: numeroComprobanteFinal.toLong()
        numeracionRepository.actualizarUltimoNumero(puntoVentaEfectivo, config.tipoComprobante, numeroComprobanteAfip.toInt())

        // Registrar intento exitoso
        val attemptExitoso = ComprobanteAttempt.createExitoso(
            ventaId = ventaId,
            tipoComprobante = config.tipoComprobante,
            puntoVenta = puntoVentaEfectivo,
            afipResponse = afipResponse,
            tiempoRespuestaMs = tiempoRespuesta.toInt(),
            comprobanteId = comprobanteId
        )
        saleRepository.saveComprobanteAttempt(attemptExitoso)

        // Marcar la venta como comprobante emitido
        saleRepository.updateComprobanteEmitido(ventaId, true)

        return comprobanteId
    }

    /**
     * Determina la estrategia de generación basándose en el modo solicitado y el comprobante original
     */
    private fun determineGenerationStrategy(
        ventaId: Id,
        tipoComprobante: String,
        modoGeneracion: String?
    ): ComprobanteGenerationStrategy {
        val modo = modoGeneracion?.uppercase() ?: "AUTO"

        return when (modo) {
            "ONLINE" -> {
                // Para notas de crédito/débito, validar que sea consistente con el comprobante original
                if (tipoComprobante in listOf("NOTA_CREDITO_B", "NOTA_DEBITO_B")) {
                    validateConsistencyWithOriginalComprobante(ventaId, ComprobanteGenerationStrategy.ONLINE)
                }
                ComprobanteGenerationStrategy.ONLINE
            }
            "OFFLINE" -> {
                // Para notas de crédito/débito, validar que sea consistente con el comprobante original
                if (tipoComprobante in listOf("NOTA_CREDITO_B", "NOTA_DEBITO_B")) {
                    validateConsistencyWithOriginalComprobante(ventaId, ComprobanteGenerationStrategy.OFFLINE)
                }
                ComprobanteGenerationStrategy.OFFLINE
            }
            "AUTO" -> {
                // Para facturas, usar online por defecto
                if (tipoComprobante == "FACTURA_B") {
                    ComprobanteGenerationStrategy.ONLINE
                } else if (tipoComprobante in listOf("NOTA_CREDITO_B", "NOTA_DEBITO_B")) {
                    // Para notas de crédito/débito, usar el mismo método que el comprobante original
                    determineStrategyFromOriginalComprobante(ventaId)
                } else {
                    ComprobanteGenerationStrategy.ONLINE
                }
            }
            else -> throw IllegalArgumentException("Modo de generación no válido: $modoGeneracion. Valores permitidos: AUTO, ONLINE, OFFLINE")
        }
    }

    /**
     * Determina la estrategia basándose en el tipo de autorización del comprobante original
     */
    private fun determineStrategyFromOriginalComprobante(ventaId: Id): ComprobanteGenerationStrategy {
        val facturaOriginal = findOriginalFactura(ventaId)

        // Retornar la estrategia basándose en el tipo de autorización del comprobante original
        return when (facturaOriginal.tipoAutorizacion) {
            TipoAutorizacion.CAE -> ComprobanteGenerationStrategy.ONLINE
            TipoAutorizacion.CAEA -> ComprobanteGenerationStrategy.OFFLINE
        }
    }

    /**
     * Valida que el modo de generación solicitado sea consistente con el comprobante original
     */
    private fun validateConsistencyWithOriginalComprobante(
        ventaId: Id,
        requestedStrategy: ComprobanteGenerationStrategy
    ) {
        val facturaOriginal = findOriginalFactura(ventaId)

        val originalStrategy = when (facturaOriginal.tipoAutorizacion) {
            TipoAutorizacion.CAE -> ComprobanteGenerationStrategy.ONLINE
            TipoAutorizacion.CAEA -> ComprobanteGenerationStrategy.OFFLINE
        }

        if (requestedStrategy != originalStrategy) {
            val requestedMode = if (requestedStrategy == ComprobanteGenerationStrategy.ONLINE) "ONLINE (CAE)" else "OFFLINE (CAEA)"
            val originalMode = if (originalStrategy == ComprobanteGenerationStrategy.ONLINE) "ONLINE (CAE)" else "OFFLINE (CAEA)"

            throw IllegalArgumentException(
                "Las notas de crédito/débito deben generarse con el mismo método que el comprobante original. " +
                "Comprobante original: $originalMode, Solicitado: $requestedMode"
            )
        }
    }

    /**
     * Busca la factura original de una venta
     */
    private fun findOriginalFactura(ventaId: Id): Comprobante {
        // Buscar la venta para obtener sus comprobantes
        val sale = saleRepository.findSaleById(ventaId)
            ?: throw IllegalArgumentException("Venta ${ventaId.value} no encontrada")

        // Buscar el comprobante original de la venta
        val comprobantesVenta = saleRepository.findComprobantesByVentaId(ventaId)

        // Buscar la factura B original
        return comprobantesVenta.find { it.tipoComprobante == "FACTURA_B" }
            ?: throw IllegalStateException("No se encontró la factura original para determinar el modo de generación de la nota")
    }

    /**
     * Valida el estado del comprobante según el tipo
     */
    private fun validateComprobanteState(sale: Sale, tipoComprobante: String) {
        when (tipoComprobante) {
            "FACTURA_B" -> {
                // Para facturas, la venta NO debe tener comprobante emitido
                if (sale.comprobanteEmitido) {
                    throw IllegalArgumentException("La venta ${sale.numeroVenta} ya tiene un comprobante emitido")
                }
            }
            "NOTA_CREDITO_B", "NOTA_DEBITO_B" -> {
                // Para notas de crédito/débito, la venta SÍ debe tener comprobante emitido
                if (!sale.comprobanteEmitido) {
                    throw IllegalArgumentException("La venta ${sale.numeroVenta} debe tener un comprobante emitido para generar una $tipoComprobante")
                }
            }
            else -> {
                throw IllegalArgumentException("Tipo de comprobante no soportado: $tipoComprobante")
            }
        }
    }

    /**
     * Busca el comprobante asociado si es requerido para notas de crédito/débito
     * @return Pair de (ComprobanteAsociado?, Comprobante?) donde el segundo es el comprobante original completo
     */
    private fun findComprobanteAsociadoIfRequired(
        sale: Sale,
        tipoComprobante: String,
        puntoVenta: Int
    ): Pair<ComprobanteAsociado?, Comprobante?> {
        // Solo las notas de crédito y débito requieren comprobante asociado
        if (tipoComprobante !in listOf("NOTA_CREDITO_B", "NOTA_DEBITO_B")) {
            return Pair(null, null)
        }

        // Buscar el comprobante original de la venta
        val comprobantesVenta = saleRepository.findComprobantesByVentaId(sale.id!!)

        // Buscar la factura B original (debe existir para poder generar nota)
        val facturaOriginal = comprobantesVenta.find { it.tipoComprobante == "FACTURA_B" }
            ?: throw IllegalStateException("No se encontró la factura original para generar la nota de crédito/débito")

        // Validar que el punto de venta sea el mismo
        if (facturaOriginal.puntoVenta != puntoVenta) {
            throw IllegalArgumentException(
                "El punto de venta debe ser el mismo que el de la factura original. " +
                "Factura: ${facturaOriginal.puntoVenta}, Solicitado: $puntoVenta"
            )
        }

        val comprobanteAsociado = ComprobanteAsociado.fromComprobante(facturaOriginal)
        return Pair(comprobanteAsociado, facturaOriginal)
    }

    override fun buscarComprobantesPorVenta(ventaId: Id): List<ComprobanteInfo> {
        val comprobantes = saleRepository.findComprobantesByVentaId(ventaId)
        return comprobantes.map { comprobante ->
            ComprobanteInfo(
                id = comprobante.id ?: throw IllegalStateException("Comprobante sin ID"),
                ventaId = comprobante.venta,
                tipoComprobante = comprobante.tipoComprobante,
                puntoVenta = comprobante.puntoVenta,
                numeroComprobante = comprobante.numeroComprobante,
                cae = comprobante.cae,
                estado = comprobante.estado,
                fechaEmision = comprobante.fechaEmision.format(dateFormatter),
                montoTotal = decimalFormatter.format(comprobante.impTotal),
                tipoAutorizacion = comprobante.tipoAutorizacion.name
            )
        }
    }
    
    override fun buscarComprobantePorNumero(puntoVenta: Int, numeroComprobante: Int): ComprobanteInfo? {
        val comprobante = saleRepository.findComprobanteByNumero(puntoVenta, numeroComprobante)
            ?: return null
        
        return ComprobanteInfo(
            id = comprobante.id ?: throw IllegalStateException("Comprobante sin ID"),
            ventaId = comprobante.venta,
            tipoComprobante = comprobante.tipoComprobante,
            puntoVenta = comprobante.puntoVenta,
            numeroComprobante = comprobante.numeroComprobante,
            cae = comprobante.cae,
            estado = comprobante.estado,
            fechaEmision = comprobante.fechaEmision.format(dateFormatter),
            montoTotal = decimalFormatter.format(comprobante.impTotal),
            tipoAutorizacion = comprobante.tipoAutorizacion.name
        )
    }
    
    override fun contarVentasSinComprobante(): Int {
        return saleRepository.countSalesWithoutComprobante()
    }
    
    override fun obtenerVentasSinComprobante(limit: Int): List<VentaSinComprobanteInfo> {
        val ventas = saleRepository.findSalesWithoutComprobante(limit)
        return ventas.map { venta ->
            VentaSinComprobanteInfo(
                ventaId = venta.id ?: throw IllegalStateException("Venta sin ID"),
                numeroVenta = venta.numeroVenta,
                fechaVenta = venta.fechaVenta.format(dateFormatter),
                clienteNombre = null, // Ya no manejamos clientes
                usuarioNombre = venta.usuario.nombreDisplay,
                montoTotal = decimalFormatter.format(venta.montoTotal),
                medioPago = venta.medioPago
            )
        }
    }

    override fun obtenerIntentosComprobante(ventaId: Id): List<ComprobanteAttemptInfo> {
        val attempts = saleRepository.findComprobanteAttemptsByVentaId(ventaId)
        return attempts.map { attempt -> mapToComprobanteAttemptInfo(attempt) }
    }

    override fun obtenerIntentosFallidos(ventaId: Id): List<ComprobanteAttemptInfo> {
        val failedAttempts = saleRepository.findFailedComprobanteAttemptsByVentaId(ventaId)
        return failedAttempts.map { attempt -> mapToComprobanteAttemptInfo(attempt) }
    }

    private fun mapToComprobanteAttemptInfo(attempt: ComprobanteAttempt): ComprobanteAttemptInfo {
        return ComprobanteAttemptInfo(
            id = attempt.id ?: throw IllegalStateException("ComprobanteAttempt sin ID"),
            ventaId = attempt.ventaId,
            tipoComprobante = attempt.tipoComprobante,
            puntoVenta = attempt.puntoVenta,
            tipoOperacion = attempt.tipoOperacion.name,
            estado = attempt.estado.name,
            fechaIntento = attempt.fechaIntento.format(dateFormatter),
            tiempoRespuestaMs = attempt.tiempoRespuestaMs,
            cae = attempt.cae,
            numeroComprobante = attempt.numeroComprobante,
            fechaVencimientoCae = attempt.fechaVencimientoCae?.format(java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy")),
            comprobanteId = attempt.comprobanteId,
            codigoError = attempt.codigoError,
            mensajeError = attempt.mensajeError,
            observacionesAfip = attempt.observacionesAfip
        )
    }

    /**
     * Obtiene el siguiente número de comprobante consultando a AFIP
     * Implementa retry y fallback para mayor robustez
     */
    private suspend fun obtenerSiguienteNumeroDeAfip(puntoVenta: Int, tipoComprobante: String): Int {
        return try {
            // Consultar último número autorizado en AFIP
            val ultimoNumeroAfip = afipService.getLastInvoiceNumber(tipoComprobante, puntoVenta)
            val siguienteNumero = (ultimoNumeroAfip + 1).toInt()

            logger.info("Último número AFIP para PV=$puntoVenta, Tipo=$tipoComprobante: $ultimoNumeroAfip, Siguiente: $siguienteNumero")

            siguienteNumero

        } catch (e: Exception) {
            logger.error("Error al consultar último número en AFIP para PV=$puntoVenta, Tipo=$tipoComprobante: ${e.message}", e)

            // Fallback: usar numeración local como respaldo
            val numeracionLocal = numeracionRepository.obtenerNumeracion(puntoVenta, tipoComprobante)
            val siguienteNumeroLocal = numeracionLocal.siguienteNumero()

            logger.warn("Usando numeración local como fallback para PV=$puntoVenta, Tipo=$tipoComprobante: $siguienteNumeroLocal")

            siguienteNumeroLocal
        }
    }
}
