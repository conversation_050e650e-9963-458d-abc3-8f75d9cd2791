package com.gnico.majo.application.usecase

import com.gnico.majo.application.port.`in`.ProductoService
import com.gnico.majo.application.port.out.ProductoRepositoryPort
import com.gnico.majo.application.domain.model.Producto

class ProductoServiceImpl(private val productoRepository: ProductoRepositoryPort) : ProductoService {

    override fun getAllProductos(): List<Producto> {
        return productoRepository.findAll()
    }

    override fun getProductoByCodigo(codigo: Int): Producto? {
        return productoRepository.findByCodigo(codigo)
    }

    override fun createProducto(producto: Producto): Int {
        // Verificar que el código no exista
        productoRepository.findByCodigo(producto.codigo)?.let {
            throw IllegalArgumentException("Ya existe un producto con el código ${producto.codigo}")
        }
        // Crear el producto con la fecha actual
        val productoToSave = producto.withCurrentTimestamps()
        return productoRepository.save(productoToSave)
    }

    override fun updateProducto(producto: Producto): Boolean {
        // Verificar que el producto exista
        val existingProducto = productoRepository.findByCodigo(producto.codigo)
            ?: throw IllegalArgumentException("No existe un producto con el código ${producto.codigo}")

        // Actualizar el producto con la fecha actual
        val productoToUpdate = producto.withUpdatedTimestamp()
        return productoRepository.update(productoToUpdate)
    }


    override fun deleteProducto(codigo: Int): Boolean {
        return productoRepository.delete(codigo)
    }

    override fun deleteMultipleProductos(codigos: List<Int>): Int {
        if (codigos.isEmpty()) return 0
        return productoRepository.deleteMultiple(codigos)
    }

    override fun updateMultipleProductos(
        codigos: List<Int>,
        categoriaId: com.gnico.majo.application.domain.model.Id?,
        precioUnitario: java.math.BigDecimal?,
        unidadMedidaId: com.gnico.majo.application.domain.model.Id?,
        stockActual: Int?
    ): Int {
        if (codigos.isEmpty()) return 0
        return productoRepository.updateMultiple(
            codigos = codigos,
            categoriaId = categoriaId,
            precioUnitario = precioUnitario,
            unidadMedidaId = unidadMedidaId,
            stockActual = stockActual
        )
    }
}