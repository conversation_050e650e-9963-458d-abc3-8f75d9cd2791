package com.gnico.majo.utils

import io.github.cdimascio.dotenv.dotenv

object Env {
    private val dotenv = dotenv {
        ignoreIfMissing = true
    }

    fun get(key: String): String =
        dotenv[key] ?: System.getenv(key) ?: error("Missing required env var: $key")

    fun getOrDefault(key: String, fallback: String): String =
        dotenv[key] ?: System.getenv(key) ?: fallback

    fun getBoolean(key: String, fallback: Boolean = false): Boolean =
        (dotenv[key] ?: System.getenv(key))?.toBoolean() ?: fallback
}