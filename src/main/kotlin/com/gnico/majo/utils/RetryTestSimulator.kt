package com.gnico.majo.utils

import org.slf4j.LoggerFactory
import java.net.SocketException
import java.net.ConnectException
import java.net.SocketTimeoutException

/**
 * Simulador para probar la lógica de retry
 * Simula diferentes tipos de errores de red para verificar el comportamiento del retry
 */
object RetryTestSimulator {
    
    private val logger = LoggerFactory.getLogger(RetryTestSimulator::class.java)
    
    /**
     * Simula la lógica de retry similar a la implementada en WsaaClientImpl
     */
    fun testRetryLogic(): RetryTestResult {
        logger.info("=== Iniciando simulación de lógica de retry ===")
        
        val results = mutableMapOf<String, Boolean>()
        
        // Test 1: SocketException (debería reintentar)
        results["SocketException"] = testSocketException()
        
        // Test 2: ConnectException (debería reintentar)
        results["ConnectException"] = testConnectException()
        
        // Test 3: SocketTimeoutException (debería reintentar)
        results["SocketTimeoutException"] = testSocketTimeoutException()
        
        // Test 4: Error de autenticación (NO debería reintentar)
        results["AuthenticationError"] = testAuthenticationError()
        
        // Test 5: Operación exitosa después de fallos
        results["SuccessAfterRetries"] = testSuccessAfterRetries()
        
        return RetryTestResult(results)
    }
    
    /**
     * Simula SocketException
     */
    private fun testSocketException(): Boolean {
        logger.info("Test 1: Simulando SocketException")
        return simulateRetryOperation { attempt ->
            if (attempt < 2) {
                throw SocketException("Connection reset")
            }
            "Success after SocketException"
        }
    }
    
    /**
     * Simula ConnectException
     */
    private fun testConnectException(): Boolean {
        logger.info("Test 2: Simulando ConnectException")
        return simulateRetryOperation { attempt ->
            if (attempt < 2) {
                throw ConnectException("Connection refused")
            }
            "Success after ConnectException"
        }
    }
    
    /**
     * Simula SocketTimeoutException
     */
    private fun testSocketTimeoutException(): Boolean {
        logger.info("Test 3: Simulando SocketTimeoutException")
        return simulateRetryOperation { attempt ->
            if (attempt < 2) {
                throw SocketTimeoutException("Read timed out")
            }
            "Success after SocketTimeoutException"
        }
    }
    
    /**
     * Simula error de autenticación (no debería reintentar)
     */
    private fun testAuthenticationError(): Boolean {
        logger.info("Test 4: Simulando error de autenticación")
        return try {
            simulateRetryOperation { _ ->
                throw RuntimeException("Authentication failed - should not retry")
            }
            false // No debería llegar aquí
        } catch (e: Exception) {
            // Debería fallar inmediatamente sin reintentos
            logger.info("✅ Error de autenticación manejado correctamente (no se reintentó)")
            true
        }
    }
    
    /**
     * Simula éxito después de varios fallos
     */
    private fun testSuccessAfterRetries(): Boolean {
        logger.info("Test 5: Simulando éxito después de reintentos")
        return simulateRetryOperation { attempt ->
            when (attempt) {
                0 -> throw SocketException("First attempt failed")
                1 -> throw ConnectException("Second attempt failed")
                else -> "Success on third attempt"
            }
        }
    }
    
    /**
     * Simula la lógica de retry
     */
    private fun simulateRetryOperation(operation: (Int) -> String): Boolean {
        val maxRetries = 3
        val retryDelay = 100L // Reducido para tests
        var lastException: Exception? = null
        
        for (attempt in 0 until maxRetries) {
            try {
                logger.debug("Intento ${attempt + 1}/$maxRetries")
                val result = operation(attempt)
                logger.info("✅ Operación exitosa: $result")
                return true
                
            } catch (e: SocketException) {
                lastException = e
                logger.warn("Intento ${attempt + 1}/$maxRetries falló - SocketException: ${e.message}")
                if (shouldRetry(attempt, maxRetries)) {
                    waitBeforeRetry(retryDelay * (attempt + 1))
                    continue
                }
                
            } catch (e: ConnectException) {
                lastException = e
                logger.warn("Intento ${attempt + 1}/$maxRetries falló - ConnectException: ${e.message}")
                if (shouldRetry(attempt, maxRetries)) {
                    waitBeforeRetry(retryDelay * (attempt + 1))
                    continue
                }
                
            } catch (e: SocketTimeoutException) {
                lastException = e
                logger.warn("Intento ${attempt + 1}/$maxRetries falló - SocketTimeoutException: ${e.message}")
                if (shouldRetry(attempt, maxRetries)) {
                    waitBeforeRetry(retryDelay * (attempt + 1))
                    continue
                }
                
            } catch (e: Exception) {
                // Para otros errores, no reintentar
                logger.error("Error no recuperable (no se reintenta): ${e.message}")
                throw e
            }
        }
        
        logger.error("❌ Todos los intentos fallaron después de $maxRetries intentos")
        throw RuntimeException("Falló después de $maxRetries intentos: ${lastException?.message}", lastException)
    }
    
    private fun shouldRetry(attempt: Int, maxRetries: Int): Boolean {
        return attempt < maxRetries - 1
    }
    
    private fun waitBeforeRetry(delay: Long) {
        logger.info("Esperando ${delay}ms antes del siguiente intento...")
        Thread.sleep(delay)
    }
}

/**
 * Resultado de las pruebas de retry
 */
data class RetryTestResult(
    val results: Map<String, Boolean>
) {
    fun isAllSuccessful(): Boolean = results.values.all { it }
    
    fun getSuccessfulCount(): Int = results.values.count { it }
    
    fun getTotalCount(): Int = results.size
    
    fun getFailedTests(): List<String> = results.filterValues { !it }.keys.toList()
    
    fun printSummary() {
        println("=== Resumen de Pruebas de Retry ===")
        println("Tests exitosos: ${getSuccessfulCount()}/${getTotalCount()}")
        println()
        
        results.forEach { (test, success) ->
            val status = if (success) "✅ PASS" else "❌ FAIL"
            println("  $test: $status")
        }
        
        if (!isAllSuccessful()) {
            println()
            println("Tests fallidos: ${getFailedTests()}")
        }
        
        println()
        println("Estado general: ${if (isAllSuccessful()) "✅ TODOS LOS TESTS PASARON" else "❌ ALGUNOS TESTS FALLARON"}")
    }
}

/**
 * Función main para ejecutar las pruebas
 */
fun main() {
    val result = RetryTestSimulator.testRetryLogic()
    result.printSummary()
    
    if (!result.isAllSuccessful()) {
        System.exit(1)
    }
}
