package com.gnico.majo.utils

import com.gnico.majo.infrastructure.config.NetworkConfiguration
import org.slf4j.LoggerFactory

/**
 * Utilidad para probar la conectividad con los servicios de AFIP
 * Útil para diagnosticar problemas de conexión
 */
object AfipConnectivityTest {
    
    private val logger = LoggerFactory.getLogger(AfipConnectivityTest::class.java)
    
    /**
     * Ejecuta una prueba completa de conectividad
     */
    fun runFullTest(): TestResult {
        logger.info("=== Iniciando prueba de conectividad AFIP ===")
        
        // Configurar red
        NetworkConfiguration.initialize()
        
        // Probar conectividad
        val connectivityResult = NetworkConfiguration.testAfipConnectivity()
        
        // Mostrar resultados
        logger.info("Resultados de conectividad:")
        connectivityResult.results.forEach { (service, success) ->
            val status = if (success) "✅ OK" else "❌ FALLO"
            logger.info("  $service: $status")
        }
        
        logger.info(connectivityResult.getSummary())
        
        if (!connectivityResult.isAllSuccessful()) {
            logger.warn("Servicios con problemas: ${connectivityResult.getFailedServices()}")
            logger.info("Posibles soluciones:")
            logger.info("  1. Verificar conexión a internet")
            logger.info("  2. Verificar configuración de proxy/firewall")
            logger.info("  3. Verificar configuración DNS")
            logger.info("  4. Verificar que los servicios de AFIP estén disponibles")
        }
        
        return TestResult(
            success = connectivityResult.isAllSuccessful(),
            connectivityResult = connectivityResult,
            recommendations = generateRecommendations(connectivityResult)
        )
    }
    
    /**
     * Genera recomendaciones basadas en los resultados
     */
    private fun generateRecommendations(result: com.gnico.majo.infrastructure.config.ConnectivityResult): List<String> {
        val recommendations = mutableListOf<String>()
        
        if (!result.isAllSuccessful()) {
            recommendations.add("Verificar conexión a internet")
            
            val failedServices = result.getFailedServices()
            
            if (failedServices.any { it.contains("Homologación") }) {
                recommendations.add("Verificar acceso a servicios de homologación de AFIP")
            }
            
            if (failedServices.any { it.contains("Producción") }) {
                recommendations.add("Verificar acceso a servicios de producción de AFIP")
            }
            
            if (failedServices.contains("WSAA Homologación") || failedServices.contains("WSAA Producción")) {
                recommendations.add("Problema específico con WSAA - verificar certificados")
            }
            
            if (failedServices.contains("WSFE Homologación") || failedServices.contains("WSFE Producción")) {
                recommendations.add("Problema específico con WSFE - verificar credenciales")
            }
            
            recommendations.add("Verificar configuración de proxy/firewall")
            recommendations.add("Verificar configuración DNS")
            recommendations.add("Contactar al administrador de red si persisten los problemas")
        } else {
            recommendations.add("Conectividad OK - todos los servicios son accesibles")
        }
        
        return recommendations
    }
}

/**
 * Resultado de la prueba de conectividad
 */
data class TestResult(
    val success: Boolean,
    val connectivityResult: com.gnico.majo.infrastructure.config.ConnectivityResult,
    val recommendations: List<String>
) {
    fun printSummary() {
        println("=== Resumen de Prueba de Conectividad AFIP ===")
        println(connectivityResult.getSummary())
        println()
        
        println("Detalles:")
        connectivityResult.results.forEach { (service, success) ->
            val status = if (success) "✅ OK" else "❌ FALLO"
            println("  $service: $status")
        }
        println()
        
        println("Recomendaciones:")
        recommendations.forEach { recommendation ->
            println("  • $recommendation")
        }
        println()
    }
}

/**
 * Función main para ejecutar la prueba desde línea de comandos
 */
fun main() {
    val result = AfipConnectivityTest.runFullTest()
    result.printSummary()
    
    if (!result.success) {
        System.exit(1)
    }
}
