package com.gnico.majo.adapter.printer

import com.gnico.majo.application.domain.model.Comprobante
import com.gnico.majo.application.domain.model.Sale
import com.gnico.majo.application.domain.model.SalesReportTicket
import com.gnico.majo.application.port.out.PrinterPort
import com.gnico.majo.infrastructure.config.PrinterConfiguration
import com.gnico.majo.infrastructure.printer.StyledTicketFormatter
import com.github.anastaciocintra.escpos.EscPos
import com.github.anastaciocintra.output.TcpIpOutputStream
import org.slf4j.LoggerFactory
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.ConnectException
import java.net.NoRouteToHostException
import java.net.UnknownHostException

/**
 * Adaptador para impresión térmica usando escpos-coffee con estilos ESC/POS
 * Maneja tanto comprobantes fiscales como no fiscales aplicando estilos como negrita, subrayado y tamaños de fuente
 */
class EscPosPrinterAdapter : PrinterPort {

    private val logger = LoggerFactory.getLogger(EscPosPrinterAdapter::class.java)
    private val config = PrinterConfiguration.fromEnvironment()
    private val formatter = StyledTicketFormatter(config)

    companion object {
        private const val CONNECTION_TIMEOUT_MS = 5000 // 5 segundos timeout
        private const val READ_TIMEOUT_MS = 10000 // 10 segundos timeout para lectura
    }

    init {
        if (!config.validate()) {
            logger.warn("Configuración de impresora incompleta. Algunas funciones pueden no estar disponibles.")
        }
    }

    override fun printTicket(comprobante: Comprobante?, sale: Sale) {
        // Verificar si la impresión está habilitada
        if (!config.enabled) {
            logger.info("Impresión deshabilitada por configuración - omitiendo ticket para venta ${sale.numeroVenta}")
            return
        }

        try {
            if (comprobante != null) {
                logger.info("Imprimiendo ticket fiscal con estilos para venta ${sale.numeroVenta} - comprobante ${comprobante.numeroComprobante}")
                printStyledTicket { escpos -> formatter.formatFiscalTicket(escpos, comprobante, sale) }
            } else {
                logger.info("Imprimiendo ticket no fiscal con estilos para venta ${sale.numeroVenta}")
                printStyledTicket { escpos -> formatter.formatNonFiscalTicket(escpos, sale) }
            }

            logger.info("Ticket con estilos impreso exitosamente para venta ${sale.numeroVenta}")

        } catch (e: Exception) {
            logger.error("Error al imprimir ticket para venta ${sale.numeroVenta}: ${e.message}", e)
            throw PrinterException("Error al imprimir ticket: ${e.message}", e)
        }
    }

    /**
     * Imprime un ticket con estilos ESC/POS usando el formatter
     */
    private fun printStyledTicket(formatAction: (EscPos) -> Unit) {
        var outputStream: TcpIpOutputStream? = null
        var escpos: EscPos? = null

        try {
            logger.debug("Intentando conectar a impresora ${config.printerIp}:${config.printerPort}")

            // Conectar a la impresora via TCP/IP
            // Nota: TcpIpOutputStream no soporta timeout en constructor, pero el socket subyacente
            // debería usar timeouts del sistema operativo
            outputStream = TcpIpOutputStream(config.printerIp, config.printerPort)
            escpos = EscPos(outputStream)

            logger.debug("Conexión establecida, enviando datos a impresora")

            // Ejecutar el formateo con estilos
            formatAction(escpos)

            // Asegurar que todo el contenido se envíe antes del corte
            escpos.flush()

            // Pausa configurable para que la impresora procese todo el contenido
            Thread.sleep(config.cutDelayMs)

            // Cortar papel
            escpos.cut(EscPos.CutMode.FULL)

            // Flush final para asegurar que el comando de corte se envíe
            escpos.flush()

            logger.debug("Ticket con estilos enviado exitosamente a impresora ${config.printerIp}:${config.printerPort}")

        } catch (e: SocketTimeoutException) {
            logger.error("Timeout al conectar con impresora ${config.printerIp}:${config.printerPort}")
            throw PrinterConnectionException("Timeout de conexión con la impresora", e)
        } catch (e: ConnectException) {
            logger.error("Conexión rechazada por impresora ${config.printerIp}:${config.printerPort}: ${e.message}")
            throw PrinterConnectionException("Conexión rechazada por la impresora", e)
        } catch (e: NoRouteToHostException) {
            logger.error("No hay ruta hacia la impresora ${config.printerIp}:${config.printerPort}: ${e.message}")
            throw PrinterConnectionException("No se puede alcanzar la impresora", e)
        } catch (e: UnknownHostException) {
            logger.error("Host desconocido para impresora ${config.printerIp}: ${e.message}")
            throw PrinterConnectionException("Dirección IP de impresora inválida", e)
        } catch (e: IOException) {
            logger.error("Error de E/O con impresora ${config.printerIp}:${config.printerPort}: ${e.message}")
            throw PrinterConnectionException("Error de comunicación con la impresora", e)
        } catch (e: Exception) {
            logger.error("Error inesperado al imprimir: ${e.message}")
            throw PrinterException("Error durante la impresión", e)
        } finally {
            try {
                escpos?.close()
                outputStream?.close()
                logger.debug("Conexión con impresora cerrada correctamente")
            } catch (e: Exception) {
                logger.warn("Error al cerrar conexión con impresora: ${e.message}")
            }
        }
    }

    override fun printSalesReport(reportTicket: SalesReportTicket) {
        // Verificar si la impresión está habilitada
        if (!config.enabled) {
            logger.info("Impresión deshabilitada por configuración - omitiendo reporte de ventas")
            return
        }

        try {
            logger.info("Imprimiendo reporte de ventas para período ${reportTicket.periodo}")
            printStyledTicket { escpos -> formatter.formatSalesReportTicket(escpos, reportTicket) }
            logger.info("Reporte de ventas impreso exitosamente")

        } catch (e: PrinterConnectionException) {
            logger.error("Error de conexión al imprimir reporte de ventas: ${e.message}")
            throw e
        } catch (e: PrinterException) {
            logger.error("Error de impresión al imprimir reporte de ventas: ${e.message}")
            throw e
        } catch (e: Exception) {
            logger.error("Error inesperado al imprimir reporte de ventas: ${e.message}")
            throw PrinterException("Error durante la impresión del reporte", e)
        }
    }
}

/**
 * Excepción base para errores de impresión
 */
open class PrinterException(message: String, cause: Throwable? = null) : Exception(message, cause)

/**
 * Excepción específica para errores de conexión con la impresora
 */
class PrinterConnectionException(message: String, cause: Throwable? = null) : PrinterException(message, cause)