package com.gnico.majo.adapter.afip

import com.gnico.majo.application.domain.model.*
import com.gnico.majo.application.port.out.AfipService
import com.gnico.majo.application.port.out.CaeaRepositoryPort
import com.gnico.majo.application.port.out.ComprobanteCAEAInfo
import com.gnico.majo.infrastructure.afip.webservices.WsfeClientImpl
import com.gnico.majo.infrastructure.afip.webservices.AfipWebserviceException
import com.gnico.majo.infrastructure.afip.webservices.AfipAuthenticationException
import com.gnico.majo.infrastructure.config.ComprobanteConfigurationService
import com.gnico.majo.infrastructure.afip.WsaaCredentialsService
import org.slf4j.LoggerFactory

/**
 * Adaptador unificado para los servicios de AFIP
 * Implementa arquitectura hexagonal con modelo de dominio rico
 * Maneja tanto facturación online (CAE) como offline (CAEA)
 * Usa persistencia de credenciales WSAA en base de datos
 */
class AfipServiceAdapter(
    private val wsaaCredentialsService: WsaaCredentialsService,
    private val caeaRepository: CaeaRepositoryPort
) : AfipService {

    private val logger = LoggerFactory.getLogger(AfipServiceAdapter::class.java)
    private val wsfeClient = WsfeClientImpl()

    override suspend fun solicitarCAE(
        sale: Sale,
        tipoComprobante: String,
        puntoVenta: Int,
        comprobanteAsociado: ComprobanteAsociado?
    ): AfipResponse {
        logger.info("Solicitando CAE online para venta ${sale.numeroVenta}, tipo: $tipoComprobante, PV: $puntoVenta")

        return try {
            // Obtener credenciales válidas
            val credentials = obtenerCredencialesValidas()
                ?: return AfipResponse.createRejected(
                    listOf("No se pudieron obtener credenciales válidas de AFIP"),
                    TipoOperacionAfip.CAE_ONLINE
                )

            // Solo permitir comprobantes tipo B (consumidor final)
            val tipoComprobanteAfip = when (tipoComprobante) {
                "FACTURA_B" -> TipoComprobanteAfip.FACTURA_B
                "NOTA_CREDITO_B" -> TipoComprobanteAfip.NOTA_CREDITO_B
                "NOTA_DEBITO_B" -> TipoComprobanteAfip.NOTA_DEBITO_B
                else -> return AfipResponse.createRejected(
                    listOf("Solo se permiten comprobantes tipo B (FACTURA_B, NOTA_CREDITO_B, NOTA_DEBITO_B). Tipo recibido: $tipoComprobante"),
                    TipoOperacionAfip.CAE_ONLINE
                )
            }

            // Crear solicitud de CAE
            val caeRequest = AfipCAERequest(
                sale = sale,
                tipoComprobante = tipoComprobanteAfip,
                puntoVenta = puntoVenta,
                credentials = credentials,
                comprobanteAsociado = comprobanteAsociado
            )

            // Solicitar CAE usando el cliente WSFE
            val wsfeResponse = wsfeClient.requestCAE(caeRequest)

            // Convertir respuesta al modelo de dominio
            val afipResponse = wsfeResponse.toAfipResponse()

            logger.info("CAE procesado para venta ${sale.numeroVenta}: ${afipResponse.resultado}, CAE: ${afipResponse.cae}")
            return afipResponse

        } catch (e: AfipWebserviceException) {
            logger.error("Error de webservice AFIP para venta ${sale.numeroVenta}: ${e.message}", e)
            AfipResponse.createRejected(
                listOf("Error de comunicación con AFIP: ${e.message}"),
                TipoOperacionAfip.CAE_ONLINE
            )
        } catch (e: Exception) {
            logger.error("Error inesperado al solicitar CAE para venta ${sale.numeroVenta}: ${e.message}", e)
            AfipResponse.createRejected(
                listOf("Error interno al procesar CAE: ${e.message}"),
                TipoOperacionAfip.CAE_ONLINE
            )
        }
    }

    override suspend fun crearComprobanteConCAEA(
        sale: Sale,
        tipoComprobante: String,
        puntoVenta: Int,
        comprobanteAsociado: ComprobanteAsociado?
    ): AfipResponse {
        logger.info("Creando comprobante offline con CAEA para venta ${sale.numeroVenta}, tipo: $tipoComprobante, PV: $puntoVenta")

        return try {
            // Solo permitir comprobantes tipo B (consumidor final)
            if (tipoComprobante !in listOf("FACTURA_B", "NOTA_CREDITO_B", "NOTA_DEBITO_B")) {
                return AfipResponse.createRejected(
                    listOf("Solo se permiten comprobantes tipo B (FACTURA_B, NOTA_CREDITO_B, NOTA_DEBITO_B). Tipo recibido: $tipoComprobante"),
                    TipoOperacionAfip.CAEA_OFFLINE
                )
            }

            // Usar punto de venta offline configurado si no se especifica uno diferente
            val puntoVentaOffline = if (puntoVenta == ComprobanteConfigurationService.getDefaultPuntoVenta()) {
                ComprobanteConfigurationService.getDefaultPuntoVentaOffline()
            } else {
                puntoVenta
            }

            // Marcar CAEAs vencidos antes de buscar uno activo
            caeaRepository.markExpiredCaeas()

            // Buscar CAEA activo para el punto de venta offline
            val caeaCode = caeaRepository.findActiveCaea(puntoVentaOffline)
                ?: return AfipResponse.createRejected(
                    listOf("No hay CAEA activo disponible para el punto de venta $puntoVentaOffline"),
                    TipoOperacionAfip.CAEA_OFFLINE
                )

            // Obtener el siguiente número de comprobante
            val numeroComprobante = caeaRepository.getNextComprobanteNumber(caeaCode.id!!, tipoComprobante)

            // Validar que no se exceda el límite de numeración (99,999,999)
            if (numeroComprobante > 99_999_999) {
                // Marcar CAEA como agotado
                caeaRepository.markAsExhausted(caeaCode.id)
                return AfipResponse.createRejected(
                    listOf("CAEA ${caeaCode.caea} agotado: se alcanzó el límite de numeración para $tipoComprobante"),
                    TipoOperacionAfip.CAEA_OFFLINE
                )
            }

            // Actualizar el último número usado
            caeaRepository.updateLastComprobanteNumber(caeaCode.id, tipoComprobante, numeroComprobante)

            logger.info("Comprobante offline creado - CAEA: ${caeaCode.caea}, Número: $numeroComprobante, PV: $puntoVentaOffline")

            AfipResponse.createApprovedCAEA(
                caea = caeaCode.caea,
                fechaVencimiento = caeaCode.fechaHasta,
                numeroComprobante = numeroComprobante,
                observaciones = listOf("Comprobante creado en modo offline con CAEA ${caeaCode.caea}")
            )

        } catch (e: Exception) {
            logger.error("Error al crear comprobante offline para venta ${sale.numeroVenta}: ${e.message}", e)
            AfipResponse.createRejected(
                listOf("Error interno al procesar CAEA: ${e.message}"),
                TipoOperacionAfip.CAEA_OFFLINE
            )
        }
    }

    override suspend fun obtenerCredenciales(service: String): AfipCredentials? {
        return try {
            wsaaCredentialsService.getValidCredentials(service)
        } catch (e: AfipAuthenticationException) {
            logger.error("Error de autenticación AFIP para servicio $service: ${e.message}", e)
            null
        } catch (e: Exception) {
            logger.error("Error inesperado al obtener credenciales AFIP: ${e.message}", e)
            null
        }
    }

    override suspend fun isServiceAvailable(): Boolean {
        return try {
            wsaaCredentialsService.hasValidCredentials()
        } catch (e: Exception) {
            logger.warn("Servicio AFIP no disponible: ${e.message}")
            false
        }
    }

    override suspend fun getLastInvoiceNumber(
        tipoComprobante: String,
        puntoVenta: Int
    ): Long {
        return try {
            val credentials = obtenerCredencialesValidas() ?: return 0L
            val tipoComprobanteAfip = TipoComprobanteAfip.fromString(tipoComprobante) ?: return 0L

            wsfeClient.getLastInvoiceNumber(credentials, puntoVenta, tipoComprobanteAfip)
        } catch (e: Exception) {
            logger.error("Error al obtener último número de comprobante: ${e.message}", e)
            0L
        }
    }

    /**
     * Obtiene credenciales válidas desde el servicio de credenciales
     */
    private suspend fun obtenerCredencialesValidas(): AfipCredentials? {
        return obtenerCredenciales()
    }


    override suspend fun solicitarCAEA(
        puntoVenta: Int,
        periodo: String,
        orden: Int
    ): AfipResponse {
        logger.info("Solicitando CAEA para punto de venta $puntoVenta, período $periodo, orden $orden")

        return try {
            // Obtener credenciales válidas
            val credentials = obtenerCredencialesValidas()
                ?: return AfipResponse.createRejected(
                    listOf("No se pudieron obtener credenciales válidas de AFIP"),
                    TipoOperacionAfip.CAEA_SOLICITUD
                )

            // Crear solicitud de CAEA
            val caeaRequest = AfipCAEARequest(
                puntoVenta = puntoVenta,
                periodo = periodo,
                orden = orden,
                credentials = credentials
            )

            // Solicitar CAEA usando el cliente WSFE
            val wsfeResponse = wsfeClient.requestCAEA(caeaRequest)

            // Convertir respuesta al modelo de dominio
            val afipResponse = wsfeResponse.toAfipResponse()

            // Si fue exitoso, guardar el CAEA en la base de datos
            if (afipResponse.isApproved()) {
                try {
                    val caeaCode = CaeaCode.create(
                        caea = wsfeResponse.caea,
                        puntoVenta = puntoVenta,
                        periodo = periodo,
                        fechaDesde = java.time.LocalDate.parse(wsfeResponse.fechaDesde, java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd")),
                        fechaHasta = java.time.LocalDate.parse(wsfeResponse.fechaHasta, java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd")),
                        orden = orden
                    )

                    caeaRepository.save(caeaCode)
                    logger.info("CAEA ${wsfeResponse.caea} guardado exitosamente en la base de datos")
                } catch (e: Exception) {
                    logger.error("Error al guardar CAEA en la base de datos: ${e.message}", e)
                    // No fallar la operación por error de persistencia
                }
            }

            logger.info("CAEA procesado para período $periodo: ${afipResponse.resultado}, CAEA: ${afipResponse.cae}")
            return afipResponse

        } catch (e: AfipWebserviceException) {
            logger.error("Error de webservice AFIP para solicitud CAEA período $periodo: ${e.message}", e)
            AfipResponse.createRejected(
                listOf("Error de comunicación con AFIP: ${e.message}"),
                TipoOperacionAfip.CAEA_SOLICITUD
            )
        } catch (e: Exception) {
            logger.error("Error inesperado al solicitar CAEA para período $periodo: ${e.message}", e)
            AfipResponse.createRejected(
                listOf("Error interno al procesar solicitud CAEA: ${e.message}"),
                TipoOperacionAfip.CAEA_SOLICITUD
            )
        }
    }

    /**
     * Informa movimientos CAEA a AFIP de forma automática
     * Determina automáticamente si usar FECAEASinMovimientoInformar o FECAEARegInformativo
     * basándose en si existen comprobantes emitidos con el CAEA
     */
    override suspend fun informarMovimientosCAEAAutomatico(
        caeaCode: String,
        puntoVenta: Int
    ): AfipResponse {
        logger.info("Informando movimientos CAEA automático: $caeaCode, PV: $puntoVenta")

        return try {
            // Validaciones comunes
            val validationResult = validarCAEAParaInforme(caeaCode)
            if (validationResult != null) {
                return validationResult
            }

            // Obtener credenciales válidas
            val credentials = obtenerCredencialesValidas()
                ?: return AfipResponse.createRejected(
                    listOf("No se pudieron obtener credenciales válidas de AFIP"),
                    TipoOperacionAfip.CAEA_INFORMATIVO
                )

            // Verificar si hay comprobantes emitidos con este CAEA
            val comprobantesInfo = caeaRepository.findComprobantesEmitidosConCAEA(caeaCode)

            val afipResponse = if (comprobantesInfo.isEmpty()) {
                // No hay comprobantes - usar FECAEASinMovimientoInformar
                logger.info("No se encontraron comprobantes para CAEA $caeaCode - informando sin movimientos")
                informarSinMovimientos(caeaCode, puntoVenta, credentials)
            } else {
                // Hay comprobantes - usar FECAEARegInformativo
                logger.info("Se encontraron ${comprobantesInfo.size} comprobantes para CAEA $caeaCode - informando con movimientos")
                informarConMovimientos(caeaCode, puntoVenta, comprobantesInfo, credentials)
            }

            // Si fue exitoso, marcar como informado en la base de datos
            if (afipResponse.isApproved()) {
                try {
                    val tipoInforme = if (comprobantesInfo.isEmpty()) "SIN_MOVIMIENTO" else "CON_MOVIMIENTOS"
                    caeaRepository.markAsInformado(caeaCode, tipoInforme)
                    logger.info("CAEA $caeaCode marcado como informado ($tipoInforme)")
                } catch (e: Exception) {
                    logger.error("Error al marcar CAEA como informado: ${e.message}", e)
                    // No fallar la operación por error de persistencia
                }
            }

            return afipResponse

        } catch (e: AfipWebserviceException) {
            logger.error("Error de webservice AFIP para informe CAEA $caeaCode: ${e.message}", e)
            AfipResponse.createRejected(
                listOf("Error de comunicación con AFIP: ${e.message}"),
                TipoOperacionAfip.CAEA_INFORMATIVO
            )
        } catch (e: Exception) {
            logger.error("Error inesperado al informar CAEA $caeaCode: ${e.message}", e)
            AfipResponse.createRejected(
                listOf("Error interno al procesar informe: ${e.message}"),
                TipoOperacionAfip.CAEA_INFORMATIVO
            )
        }
    }



    /**
     * Validaciones comunes para informes CAEA
     */
    private fun validarCAEAParaInforme(caeaCode: String): AfipResponse? {
        // Verificar que el CAEA existe en la base de datos
        val caea = caeaRepository.findByCaeaCode(caeaCode)
            ?: return AfipResponse.createRejected(
                listOf("CAEA $caeaCode no encontrado en la base de datos"),
                TipoOperacionAfip.CAEA_INFORMATIVO
            )

        // Verificar que el CAEA ha vencido (requisito de AFIP)
        if (!caea.isExpired()) {
            return AfipResponse.createRejected(
                listOf("CAEA $caeaCode aún no ha vencido. Solo se pueden informar CAEAs vencidos. Vence el: ${caea.fechaHasta}"),
                TipoOperacionAfip.CAEA_INFORMATIVO
            )
        }

        // Verificar que no fue informado previamente
        if (caeaRepository.isInformado(caeaCode)) {
            return AfipResponse.createRejected(
                listOf("CAEA $caeaCode ya fue informado previamente"),
                TipoOperacionAfip.CAEA_INFORMATIVO
            )
        }

        // Verificar que aún está dentro del plazo de informe (8 días después del vencimiento)
        val fechaLimiteInforme = caea.fechaHasta.plusDays(8)
        val hoy = java.time.LocalDate.now()

        if (hoy > fechaLimiteInforme) {
            return AfipResponse.createRejected(
                listOf("CAEA $caeaCode ya no puede ser informado. El plazo límite era: $fechaLimiteInforme"),
                TipoOperacionAfip.CAEA_INFORMATIVO
            )
        }

        return null // Sin errores de validación
    }

    /**
     * Informa a AFIP que no hubo movimientos para un CAEA específico
     */
    private fun informarSinMovimientos(
        caeaCode: String,
        puntoVenta: Int,
        credentials: AfipCredentials
    ): AfipResponse {
        logger.info("Ejecutando FECAEASinMovimientoInformar para CAEA $caeaCode")

        return try {
            // Crear solicitud de informe sin movimientos
            val request = AfipCAEASinMovimientoRequest(
                caeaCode = caeaCode,
                puntoVenta = puntoVenta,
                credentials = credentials
            )

            // Informar a AFIP
            val wsfeResponse = wsfeClient.informarCAEASinMovimiento(request)

            // Convertir respuesta al modelo de dominio
            val afipResponse = wsfeResponse.toAfipResponse()

            logger.info("FECAEASinMovimientoInformar procesado para CAEA $caeaCode: ${afipResponse.resultado}")
            afipResponse

        } catch (e: Exception) {
            logger.error("Error en FECAEASinMovimientoInformar para CAEA $caeaCode: ${e.message}", e)
            throw e
        }
    }

    /**
     * Informa a AFIP todos los comprobantes emitidos con un CAEA específico
     */
    private fun informarConMovimientos(
        caeaCode: String,
        puntoVenta: Int,
        comprobantesInfo: List<ComprobanteCAEAInfo>,
        credentials: AfipCredentials
    ): AfipResponse {
        logger.info("Ejecutando FECAEARegInformativo para CAEA $caeaCode con ${comprobantesInfo.size} comprobantes")

        return try {
            // Convertir a formato requerido por el webservice
            val comprobantes = comprobantesInfo.map { info ->
                ComprobanteCAEAMovimiento(
                    tipoComprobante = TipoComprobanteAfip.fromString(info.tipoComprobante)
                        ?: throw IllegalArgumentException("Tipo de comprobante no válido: ${info.tipoComprobante}"),
                    numeroComprobante = info.numeroComprobante,
                    fechaEmision = info.fechaEmision,
                    montoTotal = info.montoTotal,
                    montoNeto = info.montoNeto,
                    montoIva = info.montoIva,
                    montoTributos = info.montoTributos,
                    montoTotalConceptos = info.montoTotalConceptos,
                    montoExento = info.montoExento,
                    caeaUtilizado = info.caeaUtilizado
                )
            }

            // Crear solicitud de informe con movimientos
            val request = AfipCAEAMovimientosRequest(
                caeaCode = caeaCode,
                puntoVenta = puntoVenta,
                comprobantes = comprobantes,
                credentials = credentials
            )

            // Informar a AFIP
            val wsfeResponse = wsfeClient.informarCAEAMovimientos(request)

            // Convertir respuesta al modelo de dominio
            val afipResponse = wsfeResponse.toAfipResponse()

            logger.info("FECAEARegInformativo procesado para CAEA $caeaCode: ${afipResponse.resultado}, ${comprobantes.size} comprobantes")
            afipResponse

        } catch (e: Exception) {
            logger.error("Error en FECAEARegInformativo para CAEA $caeaCode: ${e.message}", e)
            throw e
        }
    }

    override suspend fun consultarCAEA(
        puntoVenta: Int,
        periodo: String,
        orden: Int
    ): CaeaCode? {
        logger.info("Consultando CAEA existente: PV=$puntoVenta, período=$periodo, orden=$orden")

        return try {
            // Obtener credenciales válidas
            val credentials = obtenerCredencialesValidas()
                ?: return null

            // Crear solicitud de consulta
            val request = AfipCAEAConsultaRequest(
                puntoVenta = puntoVenta,
                periodo = periodo,
                orden = orden,
                credentials = credentials
            )

            // Consultar en AFIP
            val wsfeResponse = wsfeClient.consultarCAEA(request)

            // Convertir respuesta al modelo de dominio
            val caeaCode = wsfeResponse.toCaeaCode(puntoVenta)

            if (caeaCode != null) {
                logger.info("✅ CAEA encontrado en AFIP: ${caeaCode.caea}")
            } else {
                logger.info("❌ No se encontró CAEA en AFIP para período $periodo-$orden")
            }

            return caeaCode

        } catch (e: AfipWebserviceException) {
            logger.error("Error de webservice AFIP al consultar CAEA: ${e.message}", e)
            null
        } catch (e: Exception) {
            logger.error("Error inesperado al consultar CAEA: ${e.message}", e)
            null
        }
    }
}