package com.gnico.majo.adapter.controller.rest

import com.gnico.majo.application.port.out.AfipService
import com.gnico.majo.application.port.out.CaeaRepositoryPort
import com.gnico.majo.adapter.controller.dto.ErrorResponse
import com.gnico.majo.infrastructure.config.ComprobanteConfigurationService
import io.ktor.http.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.serialization.Serializable
import org.slf4j.LoggerFactory

/**
 * Controlador REST para gestión de CAEA (Códigos de Autorización Electrónico Anticipado)
 */
class CaeaController(
    private val afipService: AfipService,
    private val caeaRepository: CaeaRepositoryPort
) {
    
    private val logger = LoggerFactory.getLogger(CaeaController::class.java)

    /**
     * Solicita un CAEA a AFIP
     */
    suspend fun solicitarCAEA(call: RoutingCall) {
        try {
            val request = call.receive<SolicitarCAEARequest>()
            val puntoVenta = ComprobanteConfigurationService.getDefaultPuntoVentaOffline()

            logger.info("Solicitando CAEA para PV: $puntoVenta, período: ${request.periodo}, orden: ${request.orden}")

            val response = afipService.solicitarCAEA(
                puntoVenta = puntoVenta,
                periodo = request.periodo,
                orden = request.orden
            )
            
            val responseDto = CAEAResponse(
                success = response.isApproved(),
                caea = if (response.isApproved()) response.cae else null,
                fechaVencimiento = if (response.isApproved()) response.fechaVencimientoCae.toString() else null,
                observaciones = response.observaciones,
                error = if (response.isRejected()) response.observaciones.joinToString("; ") else null
            )
            
            val statusCode = if (response.isApproved()) HttpStatusCode.OK else HttpStatusCode.BadRequest
            call.respond(statusCode, responseDto)
            
        } catch (e: Exception) {
            logger.error("Error al solicitar CAEA: ${e.message}", e)
            call.respond(
                HttpStatusCode.InternalServerError,
                ErrorResponse("Error al solicitar CAEA: ${e.message}")
            )
        }
    }

    /**
     * Informa movimientos CAEA a AFIP de forma automática
     * Determina automáticamente si usar sin movimientos o con movimientos
     */
    suspend fun informarMovimientosAutomatico(call: RoutingCall) {
        try {
            val request = call.receive<InformarCAEARequest>()
            val puntoVenta = ComprobanteConfigurationService.getDefaultPuntoVentaOffline()

            logger.info("Informando CAEA automático: ${request.caeaCode}, PV: $puntoVenta")

            // Usar el método automático del servicio
            val response = afipService.informarMovimientosCAEAAutomatico(
                caeaCode = request.caeaCode,
                puntoVenta = puntoVenta
            )

            val responseDto = InformarCAEAResponse(
                success = response.isApproved(),
                mensaje = if (response.isApproved()) {
                    "CAEA informado exitosamente (${if (response.observaciones.any { it.contains("sin movimientos") }) "sin movimientos" else "con movimientos"})"
                } else {
                    "Error al informar CAEA"
                },
                observaciones = response.observaciones,
                error = if (response.isRejected()) response.observaciones.joinToString("; ") else null
            )

            val statusCode = if (response.isApproved()) HttpStatusCode.OK else HttpStatusCode.BadRequest
            call.respond(statusCode, responseDto)

        } catch (e: Exception) {
            logger.error("Error al informar CAEA automático: ${e.message}", e)
            call.respond(
                HttpStatusCode.InternalServerError,
                ErrorResponse("Error al informar CAEA automático: ${e.message}")
            )
        }
    }



    /**
     * Obtiene todos los CAEAs para el punto de venta offline configurado
     */
    suspend fun getCaeas(call: RoutingCall) {
        try {
            val puntoVenta = ComprobanteConfigurationService.getDefaultPuntoVentaOffline()

            val caeas = caeaRepository.findByPuntoVenta(puntoVenta)
            
            val responseDto = CAEAListResponse(
                caeas = caeas.map { caea ->
                    CAEAInfoResponse(
                        id = caea.id ?: 0,
                        caea = caea.caea,
                        puntoVenta = caea.puntoVenta,
                        periodo = caea.periodo,
                        fechaDesde = caea.fechaDesde.toString(),
                        fechaHasta = caea.fechaHasta.toString(),
                        orden = caea.orden,
                        estado = caea.estado.name,
                        ultimoNumeroFacturaB = caea.ultimoNumeroFacturaB,
                        ultimoNumeroNotaCreditoB = caea.ultimoNumeroNotaCreditoB,
                        ultimoNumeroNotaDebitoB = caea.ultimoNumeroNotaDebitoB,
                        informado = caeaRepository.isInformado(caea.caea),
                        creadoEn = caea.creadoEn.toString(),
                        actualizadoEn = caea.actualizadoEn.toString()
                    )
                },
                total = caeas.size
            )
            
            call.respond(HttpStatusCode.OK, responseDto)
            
        } catch (e: Exception) {
            logger.error("Error al obtener CAEAs: ${e.message}", e)
            call.respond(
                HttpStatusCode.InternalServerError,
                ErrorResponse("Error al obtener CAEAs: ${e.message}")
            )
        }
    }

    /**
     * Obtiene información de comprobantes emitidos con un CAEA específico
     */
    suspend fun getComprobantesCAEA(call: RoutingCall) {
        try {
            val caeaCode = call.parameters["caeaCode"]
                ?: return call.respond(HttpStatusCode.BadRequest, ErrorResponse("Código CAEA requerido"))
            
            val comprobantes = caeaRepository.findComprobantesEmitidosConCAEA(caeaCode)
            
            val responseDto = ComprobantesCAEAResponse(
                caeaCode = caeaCode,
                comprobantes = comprobantes.map { comp ->
                    ComprobanteCAEAInfoResponse(
                        numeroComprobante = comp.numeroComprobante,
                        tipoComprobante = comp.tipoComprobante,
                        fechaEmision = comp.fechaEmision.toString(),
                        montoTotal = comp.montoTotal.toString(),
                        montoNeto = comp.montoNeto.toString(),
                        montoIva = comp.montoIva.toString(),
                        montoTributos = comp.montoTributos.toString(),
                        montoTotalConceptos = comp.montoTotalConceptos.toString(),
                        montoExento = comp.montoExento.toString(),
                        caeaUtilizado = comp.caeaUtilizado
                    )
                },
                total = comprobantes.size
            )
            
            call.respond(HttpStatusCode.OK, responseDto)
            
        } catch (e: Exception) {
            logger.error("Error al obtener comprobantes CAEA: ${e.message}", e)
            call.respond(
                HttpStatusCode.InternalServerError,
                ErrorResponse("Error al obtener comprobantes CAEA: ${e.message}")
            )
        }
    }
}

// DTOs para requests y responses

@Serializable
data class SolicitarCAEARequest(
    val periodo: String, // YYYYMM
    val orden: Int // 1 o 2
)

@Serializable
data class InformarCAEARequest(
    val caeaCode: String
)

@Serializable
data class CAEAResponse(
    val success: Boolean,
    val caea: String? = null,
    val fechaVencimiento: String? = null,
    val observaciones: List<String> = emptyList(),
    val error: String? = null
)

@Serializable
data class InformarCAEAResponse(
    val success: Boolean,
    val mensaje: String,
    val observaciones: List<String> = emptyList(),
    val error: String? = null
)

@Serializable
data class CAEAInfoResponse(
    val id: Int,
    val caea: String,
    val puntoVenta: Int,
    val periodo: String,
    val fechaDesde: String,
    val fechaHasta: String,
    val orden: Int,
    val estado: String,
    val ultimoNumeroFacturaB: Long,
    val ultimoNumeroNotaCreditoB: Long,
    val ultimoNumeroNotaDebitoB: Long,
    val informado: Boolean,
    val creadoEn: String,
    val actualizadoEn: String
)

@Serializable
data class CAEAListResponse(
    val caeas: List<CAEAInfoResponse>,
    val total: Int
)

@Serializable
data class ComprobanteCAEAInfoResponse(
    val numeroComprobante: Long,
    val tipoComprobante: String,
    val fechaEmision: String,
    val montoTotal: String,
    val montoNeto: String,
    val montoIva: String,
    val montoTributos: String,
    val montoTotalConceptos: String,
    val montoExento: String,
    val caeaUtilizado: String
)

@Serializable
data class ComprobantesCAEAResponse(
    val caeaCode: String,
    val comprobantes: List<ComprobanteCAEAInfoResponse>,
    val total: Int
)
