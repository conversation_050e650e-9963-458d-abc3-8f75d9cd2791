package com.gnico.majo.adapter.controller.rest

import com.gnico.majo.adapter.controller.dto.SaleCancellationRequest
import com.gnico.majo.adapter.controller.dto.SaleCancellationResponse
import com.gnico.majo.adapter.controller.dto.CancelledSalesHistoryResponse
import com.gnico.majo.adapter.controller.dto.CancelledSaleResponse
import com.gnico.majo.adapter.controller.dto.CancellationStatsResponse
import com.gnico.majo.application.port.`in`.SaleService
import com.gnico.majo.application.port.`in`.SaleCancellationService
import com.gnico.majo.application.domain.model.Id

class SaleCancellationController(
    private val saleService: SaleService,
    private val saleCancellationService: SaleCancellationService
) {

    /**
     * Cancela una venta
     */
    suspend fun cancelSale(ventaId: Int, request: SaleCancellationRequest): SaleCancellationResponse {
        val result = saleService.cancelSale(
            ventaId = Id(ventaId),
            usuarioCancelacion = request.usuarioCancelacion,
            motivo = request.motivo,
            generarNotaCreditoOnline = request.generarNotaCreditoOnline
        )

        return SaleCancellationResponse(
            success = result.success,
            message = result.message,
            ventaId = result.ventaId,
            numeroVenta = result.numeroVenta,
            notaCreditoGenerada = result.notaCreditoGenerada,
            notaCreditoId = result.notaCreditoId,
            notaCreditoNumero = result.notaCreditoNumero,
            error = result.error
        )
    }

    /**
     * Obtiene ventas canceladas pendientes de nota de crédito
     */
    fun getSalesPendingNotaCredito(limit: Int): CancelledSalesHistoryResponse {
        val pendingSales = saleCancellationService.getSalesPendingNotaCredito(limit)
        val totalPending = saleCancellationService.countSalesPendingNotaCredito()

        return CancelledSalesHistoryResponse(
            cancelledSales = pendingSales.map { sale ->
                CancelledSaleResponse(
                    ventaId = sale.ventaId,
                    numeroVenta = sale.numeroVenta,
                    fechaVenta = sale.fechaVenta,
                    fechaCancelacion = sale.fechaCancelacion,
                    usuarioVenta = sale.usuarioVenta,
                    usuarioCancelacion = sale.usuarioCancelacion,
                    motivoCancelacion = sale.motivoCancelacion,
                    montoTotal = sale.montoTotal,
                    teniaComprobante = sale.teniaComprobante,
                    notaCreditoGenerada = sale.notaCreditoGenerada,
                    notaCreditoNumero = sale.notaCreditoNumero
                )
            },
            totalCancelaciones = totalPending
        )
    }

    /**
     * Obtiene el historial de ventas canceladas
     */
    fun getCancelledSalesHistory(limit: Int = 100): CancelledSalesHistoryResponse {
        val history = saleCancellationService.getCancelledSalesHistory(limit)
        val stats = saleCancellationService.getCancellationStats()

        val cancelledSales = history.map { info ->
            CancelledSaleResponse(
                ventaId = info.ventaId,
                numeroVenta = info.numeroVenta,
                fechaVenta = info.fechaVenta,
                fechaCancelacion = info.fechaCancelacion,
                usuarioVenta = info.usuarioVenta,
                usuarioCancelacion = info.usuarioCancelacion,
                motivoCancelacion = info.motivoCancelacion,
                montoTotal = info.montoTotal,
                teniaComprobante = info.teniaComprobante,
                notaCreditoGenerada = info.notaCreditoGenerada,
                notaCreditoNumero = info.notaCreditoNumero
            )
        }

        return CancelledSalesHistoryResponse(
            cancelledSales = cancelledSales,
            totalCancelaciones = stats.totalCancelaciones
        )
    }

    /**
     * Obtiene estadísticas de cancelaciones
     */
    fun getCancellationStats(): CancellationStatsResponse {
        val stats = saleCancellationService.getCancellationStats()
        
        return CancellationStatsResponse(
            totalCancelaciones = stats.totalCancelaciones,
            cancelacionesConNotaCredito = stats.cancelacionesConNotaCredito,
            cancelacionesSinNotaCredito = stats.cancelacionesSinNotaCredito
        )
    }
}
