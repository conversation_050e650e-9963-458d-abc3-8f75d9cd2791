package com.gnico.majo.adapter.controller.dto

import kotlinx.serialization.Serializable

/**
 * Request para cancelar una venta
 */
@Serializable
data class SaleCancellationRequest(
    val usuarioCancelacion: String,
    val motivo: String,
    val generarNotaCreditoOnline: Boolean = true
)

/**
 * Response de cancelación de venta
 */
@Serializable
data class SaleCancellationResponse(
    val success: Boolean,
    val message: String,
    val ventaId: Int,
    val numeroVenta: String,
    val notaCreditoGenerada: Boolean = false,
    val notaCreditoId: Int? = null,
    val notaCreditoNumero: String? = null,
    val error: String? = null
)

/**
 * Response para historial de ventas canceladas
 */
@Serializable
data class CancelledSalesHistoryResponse(
    val cancelledSales: List<CancelledSaleResponse>,
    val totalCancelaciones: Int
)

/**
 * Información de una venta cancelada
 */
@Serializable
data class CancelledSaleResponse(
    val ventaId: Int,
    val numeroVenta: String,
    val fechaVenta: String,
    val fechaCancelacion: String,
    val usuarioVenta: String,
    val usuarioCancelacion: String,
    val motivoCancelacion: String,
    val montoTotal: String,
    val teniaComprobante: Boolean,
    val notaCreditoGenerada: Boolean,
    val notaCreditoNumero: String?
)

/**
 * Response para estadísticas de cancelaciones
 */
@Serializable
data class CancellationStatsResponse(
    val totalCancelaciones: Int,
    val cancelacionesConNotaCredito: Int,
    val cancelacionesSinNotaCredito: Int
)
