package com.gnico.majo.adapter.controller.rest

import com.gnico.majo.adapter.controller.dto.CategoriaDto
import com.gnico.majo.adapter.controller.dto.CategoriaResponse
import com.gnico.majo.adapter.controller.dto.CategoriaCreatedResponse
import com.gnico.majo.adapter.controller.dto.CategoriaListResponse
import com.gnico.majo.adapter.controller.dto.BulkUpdateCategoriasOrdenRequest
import com.gnico.majo.adapter.controller.dto.BulkUpdateCategoriasOrdenResponse
import com.gnico.majo.application.port.`in`.CategoriaService
import com.gnico.majo.application.domain.model.Categoria
import com.gnico.majo.application.domain.model.Id

/**
 * Controlador REST para categorías
 */
class CategoriaController(private val categoriaService: CategoriaService) {

    fun getAllCategorias(): CategoriaListResponse {
        val categorias = categoriaService.getAllCategorias()
        val categoriasResponse = categorias.map { mapToCategoriaResponse(it) }
        return CategoriaListResponse(
            categorias = categoriasResponse,
            total = categoriasResponse.size
        )
    }

    fun getCategoriaById(id: Int): CategoriaResponse? {
        val categoria = categoriaService.getCategoriaById(Id(id))
        return categoria?.let { mapToCategoriaResponse(it) }
    }

    fun createCategoria(request: CategoriaDto): CategoriaCreatedResponse {
        val categoria = mapToCategoria(request)
        val id = categoriaService.createCategoria(categoria)
        return CategoriaCreatedResponse(id = id.value)
    }

    fun updateCategoria(id: Int, request: CategoriaDto): Boolean {
        val categoria = mapToCategoria(request).copy(id = Id(id))
        return categoriaService.updateCategoria(categoria)
    }

    fun deleteCategoria(id: Int): Boolean {
        return categoriaService.deleteCategoria(Id(id))
    }

    private fun mapToCategoria(request: CategoriaDto): Categoria {
        return Categoria.create(
            nombre = request.nombre,
            descripcion = request.descripcion,
            color = Categoria.sanitizeColor(request.color),
            orden = request.orden,
            activo = request.activo
        )
    }

    fun updateOrdenCategorias(request: BulkUpdateCategoriasOrdenRequest): BulkUpdateCategoriasOrdenResponse {
        if (request.categorias.isEmpty()) {
            return BulkUpdateCategoriasOrdenResponse(
                actualizadas = 0,
                mensaje = "No se proporcionaron categorías para actualizar"
            )
        }

        // Validar que todos los órdenes sean positivos
        val invalidOrden = request.categorias.find { it.orden <= 0 }
        if (invalidOrden != null) {
            return BulkUpdateCategoriasOrdenResponse(
                actualizadas = 0,
                mensaje = "El orden debe ser un número positivo. Categoría ID ${invalidOrden.id} tiene orden ${invalidOrden.orden}"
            )
        }

        val actualizadas = categoriaService.updateOrdenCategorias(request.categorias.associate { it.id to it.orden })

        return BulkUpdateCategoriasOrdenResponse(
            actualizadas = actualizadas,
            mensaje = "Se actualizó el orden de $actualizadas categoría(s) correctamente"
        )
    }

    fun getSinCategoriaEspecial(): CategoriaResponse? {
        val categoria = categoriaService.getSinCategoriaEspecial()
        return categoria?.let { mapToCategoriaResponse(it) }
    }

    private fun mapToCategoriaResponse(categoria: Categoria): CategoriaResponse {
        return CategoriaResponse(
            id = categoria.id?.value ?: 0,
            nombre = categoria.nombre,
            descripcion = categoria.descripcion,
            color = categoria.color,
            orden = categoria.orden,
            activo = categoria.activo
        )
    }
}
