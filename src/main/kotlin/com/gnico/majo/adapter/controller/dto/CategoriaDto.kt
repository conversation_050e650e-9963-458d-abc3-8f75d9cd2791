package com.gnico.majo.adapter.controller.dto

import kotlinx.serialization.Serializable

/**
 * DTO para requests de categoría
 */
@Serializable
data class CategoriaDto(
    val nombre: String,
    val descripcion: String? = null,
    val color: String? = null,
    val orden: Int? = null,
    val activo: Boolean = true
)

/**
 * DTO para responses de categoría
 */
@Serializable
data class CategoriaResponse(
    val id: Int,
    val nombre: String,
    val descripcion: String? = null,
    val color: String? = null,
    val orden: Int? = null,
    val activo: Boolean
)

/**
 * DTO para response de creación de categoría
 */
@Serializable
data class CategoriaCreatedResponse(
    val id: Int,
    val mensaje: String = "Categoría creada exitosamente"
)

/**
 * DTO para request de actualización de orden de categorías
 */
@Serializable
data class BulkUpdateCategoriasOrdenRequest(
    val categorias: List<CategoriaOrdenItem>
)

/**
 * DTO para item individual de orden de categoría
 */
@Serializable
data class CategoriaOrdenItem(
    val id: Int,
    val orden: Int
)

/**
 * DTO para response de actualización de orden de categorías
 */
@Serializable
data class BulkUpdateCategoriasOrdenResponse(
    val actualizadas: Int,
    val mensaje: String
)

/**
 * DTO para response de lista de categorías
 */
@Serializable
data class CategoriaListResponse(
    val categorias: List<CategoriaResponse>,
    val total: Int
)
