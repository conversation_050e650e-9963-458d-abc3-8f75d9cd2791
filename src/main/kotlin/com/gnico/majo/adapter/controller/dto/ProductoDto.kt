package com.gnico.majo.adapter.controller.dto

import kotlinx.serialization.Serializable

@Serializable
data class ProductoDto(
    val codigo: Int, // Código de 4 dígitos usado como identificador
    val nombre: String,
    val descripcion: String? = null,
    val unidadMedidaId: Int,
    val tipoIvaId: Int  = 5,
    val categoriaId: Int? = null,
    val precioUnitario: Double? = null,
    val stockActual: Int? = null,
    val activo: Boolean = true
)

@Serializable
data class ProductoResponse(
    val codigo: Int,
    val nombre: String,
    val descripcion: String? = null,
    val unidadMedidaId: Int,
    val tipoIvaId: Int,
    val categoriaId: Int? = null,
    val precioUnitario: Double? = null,
    val stockActual: Int? = null,
    val activo: Boolean
)

@Serializable
data class ProductoListResponse(
    val productos: List<ProductoResponse>
)

@Serializable
data class ProductoCodigoResponse(
    val codigo: Int
)

@Serializable
data class DeleteMultipleProductosRequest(
    val codigos: List<Int>
)

@Serializable
data class DeleteMultipleProductosResponse(
    val eliminados: Int,
    val mensaje: String
)

@Serializable
data class BulkUpdateProductosRequest(
    val codigos: List<Int>,
    val categoriaId: Int? = null,
    val precioUnitario: Double? = null,
    val unidadMedidaId: Int? = null,
    val stockActual: Int? = null
)

@Serializable
data class BulkUpdateProductosResponse(
    val actualizados: Int,
    val mensaje: String
)
