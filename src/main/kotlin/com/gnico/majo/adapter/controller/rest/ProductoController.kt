package com.gnico.majo.adapter.controller.rest

import com.gnico.majo.adapter.controller.dto.ProductoCodigoResponse
import com.gnico.majo.adapter.controller.dto.ProductoDto
import com.gnico.majo.adapter.controller.dto.ProductoListResponse
import com.gnico.majo.adapter.controller.dto.ProductoResponse
import com.gnico.majo.adapter.controller.dto.DeleteMultipleProductosRequest
import com.gnico.majo.adapter.controller.dto.DeleteMultipleProductosResponse
import com.gnico.majo.adapter.controller.dto.BulkUpdateProductosRequest
import com.gnico.majo.adapter.controller.dto.BulkUpdateProductosResponse
import com.gnico.majo.application.port.`in`.ProductoService
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Producto
import com.gnico.majo.application.domain.model.TipoIva
import java.math.BigDecimal

class ProductoController(private val productoService: ProductoService) {

    fun getAllProductos(): ProductoListResponse {
        val productos = productoService.getAllProductos()
        return ProductoListResponse(
            productos = productos.map { mapToProductoResponse(it) }
        )
    }

    fun getProductoByCodigo(codigo: Int): ProductoResponse? {
        val producto = productoService.getProductoByCodigo(codigo)
        return producto?.let { mapToProductoResponse(it) }
    }

    fun createProducto(request: ProductoDto): ProductoCodigoResponse {
        val producto = mapToProducto(request)
        val codigo = productoService.createProducto(producto)
        return ProductoCodigoResponse(codigo)
    }

    fun updateProducto(request: ProductoDto): Boolean {
        val producto = mapToProducto(request)
        return productoService.updateProducto(producto)
    }

    fun deleteProducto(codigo: Int): Boolean {
        return productoService.deleteProducto(codigo)
    }

    fun deleteMultipleProductos(request: DeleteMultipleProductosRequest): DeleteMultipleProductosResponse {
        if (request.codigos.isEmpty()) {
            return DeleteMultipleProductosResponse(
                eliminados = 0,
                mensaje = "No se proporcionaron códigos para eliminar"
            )
        }

        val eliminados = productoService.deleteMultipleProductos(request.codigos)
        return DeleteMultipleProductosResponse(
            eliminados = eliminados,
            mensaje = if (eliminados > 0) {
                "Se eliminaron $eliminados producto(s) correctamente"
            } else {
                "No se encontraron productos con los códigos proporcionados"
            }
        )
    }

    fun updateMultipleProductos(request: BulkUpdateProductosRequest): BulkUpdateProductosResponse {
        if (request.codigos.isEmpty()) {
            return BulkUpdateProductosResponse(
                actualizados = 0,
                mensaje = "No se proporcionaron códigos para actualizar"
            )
        }

        // Validar que al menos un campo esté presente para actualizar
        if (request.categoriaId == null && request.precioUnitario == null &&
            request.unidadMedidaId == null && request.stockActual == null) {
            return BulkUpdateProductosResponse(
                actualizados = 0,
                mensaje = "No se proporcionaron campos para actualizar"
            )
        }

        // Procesar valores especiales para limpiar campos opcionales
        val categoriaIdToUpdate = when {
            request.categoriaId == null -> null
            request.categoriaId <= 0 -> Id(-1) // Valor especial para indicar que se debe limpiar
            else -> Id(request.categoriaId)
        }

        val stockActualToUpdate = when {
            request.stockActual == null -> null
            request.stockActual < 0 -> -1 // Valor especial para indicar que se debe limpiar
            else -> request.stockActual
        }

        val actualizados = productoService.updateMultipleProductos(
            codigos = request.codigos,
            categoriaId = categoriaIdToUpdate,
            precioUnitario = request.precioUnitario?.let { BigDecimal(it.toString()) },
            unidadMedidaId = request.unidadMedidaId?.let { Id(it) },
            stockActual = stockActualToUpdate
        )

        return BulkUpdateProductosResponse(
            actualizados = actualizados,
            mensaje = if (actualizados > 0) {
                "Se actualizaron $actualizados producto(s) correctamente"
            } else {
                "No se encontraron productos con los códigos proporcionados"
            }
        )
    }

    private fun mapToProducto(request: ProductoDto): Producto {
        return Producto.create(
            codigo = request.codigo,
            nombre = request.nombre,
            descripcion = request.descripcion,
            unidadMedida = Id(request.unidadMedidaId),
            tipoIva = TipoIva.fromIdOrThrow(request.tipoIvaId),
            categoria = request.categoriaId?.let { Id(it) },
            precioUnitario = request.precioUnitario?.let { BigDecimal(it.toString()) },
            stockActual = request.stockActual,
            activo = request.activo
        )
    }

    private fun mapToProductoResponse(producto: Producto): ProductoResponse {
        return ProductoResponse(
            codigo = producto.codigo,
            nombre = producto.nombre,
            descripcion = producto.descripcion,
            unidadMedidaId = producto.unidadMedida.value,
            tipoIvaId = producto.tipoIva.id,
            categoriaId = producto.categoria?.value,
            precioUnitario = producto.precioUnitario?.toDouble(),
            stockActual = producto.stockActual,
            activo = producto.activo
        )
    }
}