package com.gnico.majo.adapter.controller.rest

import com.gnico.majo.application.port.`in`.PrintService
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.ReportFilters
import com.gnico.majo.application.domain.model.ReportPeriod
import kotlinx.serialization.Serializable
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException

/**
 * Controlador simplificado para impresión de tickets
 *
 * Para búsquedas de ventas/comprobantes, usar SaleController:
 * - getSaleByNumero() - Buscar venta por número
 * - getSalesWithFilters() - Buscar ventas con filtros
 * - getSaleById() - Buscar venta por ID
 */
class PrintController(
    private val printService: PrintService
) {

    /**
     * Imprime un ticket térmico unificado
     * @param ventaId ID de la venta
     * @param comprobanteId ID del comprobante (opcional, null para ticket genérico)
     */
    fun imprimirTicket(ventaId: Int, comprobanteId: Int? = null): PrintResponse {
        return try {
            val comprobanteIdObj = comprobanteId?.let { Id(it) }
            printService.imprimirTicket(Id(ventaId), comprobanteIdObj)

            val message = if (comprobanteId != null) {
                "Ticket fiscal impreso exitosamente"
            } else {
                "Ticket de venta impreso exitosamente"
            }

            PrintResponse(
                success = true,
                message = message
            )
        } catch (e: Exception) {
            PrintResponse(
                success = false,
                message = "Error al imprimir ticket",
                error = e.message
            )
        }
    }

    /**
     * Imprime un reporte de ventas en ticket térmico
     * @param periodo Período del reporte (opcional, por defecto "hoy")
     * @param fechaDesde Fecha de inicio (opcional)
     * @param fechaHasta Fecha de fin (opcional)
     * @param usuarios Lista de usuarios a filtrar (opcional)
     * @param mediosPago Lista de medios de pago a filtrar (opcional)

     * @param soloConComprobante Si mostrar solo ventas con comprobante (opcional)
     * @param soloMediosPagoElectronicos Si mostrar solo medios electrónicos (por defecto false)
     */
    suspend fun imprimirReporteVentas(
        periodo: String?,
        fechaDesde: String?,
        fechaHasta: String?,
        usuarios: List<String>?,
        mediosPago: List<String>?,

        soloConComprobante: Boolean?,
        soloMediosPagoElectronicos: Boolean
    ): PrintResponse {
        return try {
            val filters = createReportFilters(
                periodo = periodo,
                fechaDesde = fechaDesde,
                fechaHasta = fechaHasta,
                usuarios = usuarios,
                mediosPago = mediosPago,

                soloConComprobante = soloConComprobante,
                soloMediosPagoElectronicos = soloMediosPagoElectronicos
            )

            printService.imprimirReporteVentas(filters)

            PrintResponse(
                success = true,
                message = "Reporte de ventas impreso exitosamente"
            )
        } catch (e: IllegalArgumentException) {
            PrintResponse(
                success = false,
                message = "Error en parámetros del reporte",
                error = e.message
            )
        } catch (e: Exception) {
            PrintResponse(
                success = false,
                message = "Error al imprimir reporte de ventas",
                error = e.message
            )
        }
    }

    private fun createReportFilters(
        periodo: String?,
        fechaDesde: String?,
        fechaHasta: String?,
        usuarios: List<String>?,
        mediosPago: List<String>?,
        soloConComprobante: Boolean?,
        soloMediosPagoElectronicos: Boolean
    ): ReportFilters {
        val reportPeriod = when {
            periodo != null -> ReportPeriod.fromString(periodo)
            fechaDesde != null && fechaHasta != null -> {
                try {
                    val formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME
                    val desde = LocalDateTime.parse(fechaDesde, formatter)
                    val hasta = LocalDateTime.parse(fechaHasta, formatter)
                    ReportPeriod.custom(desde, hasta)
                } catch (e: DateTimeParseException) {
                    throw IllegalArgumentException("Formato de fecha inválido. Use ISO format: yyyy-MM-ddTHH:mm:ss")
                }
            }
            else -> ReportPeriod.today()
        }

        return ReportFilters(
            periodo = reportPeriod,
            usuarios = usuarios?.filter { it.isNotBlank() },
            mediosPago = mediosPago?.filter { it.isNotBlank() },
            soloConComprobante = soloConComprobante,
            soloMediosPagoElectronicos = soloMediosPagoElectronicos
        )
    }
}

@Serializable
data class PrintResponse(
    val success: Boolean,
    val message: String,
    val error: String? = null
)
