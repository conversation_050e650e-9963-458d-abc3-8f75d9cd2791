package com.gnico.majo.adapter.controller.dto

import com.gnico.majo.application.port.`in`.ComprobanteInfo
import com.gnico.majo.application.port.`in`.ComprobanteAttemptInfo
import com.gnico.majo.application.port.`in`.VentaSinComprobanteInfo
import kotlinx.serialization.Serializable

/**
 * DTOs para serialización de respuestas de comprobantes
 */

@Serializable
data class ComprobanteInfoResponse(
    val id: Int,
    val ventaId: Int,
    val tipoComprobante: String,
    val puntoVenta: Int,
    val numeroComprobante: Int,
    val cae: String,
    val estado: String,
    val fechaEmision: String,
    val montoTotal: String,
    val tipoAutorizacion: String // CAE o CAEA
)

@Serializable
data class VentaSinComprobanteInfoResponse(
    val ventaId: Int,
    val numeroVenta: String,
    val fechaVenta: String,
    val clienteNombre: String?,
    val usuarioNombre: String,
    val montoTotal: String,
    val medioPago: String
)

@Serializable
data class ComprobanteAttemptInfoResponse(
    val id: Int,
    val ventaId: Int,
    val tipoComprobante: String,
    val puntoVenta: Int,
    val tipoOperacion: String, // CAE_ONLINE, CAEA_OFFLINE
    val estado: String, // EXITOSO, FALLIDO, PENDIENTE
    val fechaIntento: String,
    val tiempoRespuestaMs: Int?,

    // Datos de éxito
    val cae: String?,
    val numeroComprobante: Long?,
    val fechaVencimientoCae: String?,
    val comprobanteId: Int?,

    // Datos de error
    val codigoError: String?,
    val mensajeError: String?,
    val observacionesAfip: List<String>
)

// Funciones de extensión para convertir entre DTOs
fun ComprobanteInfo.toResponse(): ComprobanteInfoResponse {
    return ComprobanteInfoResponse(
        id = this.id.value,
        ventaId = this.ventaId.value,
        tipoComprobante = this.tipoComprobante,
        puntoVenta = this.puntoVenta,
        numeroComprobante = this.numeroComprobante,
        cae = this.cae,
        estado = this.estado,
        fechaEmision = this.fechaEmision,
        montoTotal = this.montoTotal,
        tipoAutorizacion = this.tipoAutorizacion
    )
}

fun VentaSinComprobanteInfo.toResponse(): VentaSinComprobanteInfoResponse {
    return VentaSinComprobanteInfoResponse(
        ventaId = this.ventaId.value,
        numeroVenta = this.numeroVenta,
        fechaVenta = this.fechaVenta,
        clienteNombre = this.clienteNombre,
        usuarioNombre = this.usuarioNombre,
        montoTotal = this.montoTotal,
        medioPago = this.medioPago
    )
}

fun ComprobanteAttemptInfo.toResponse(): ComprobanteAttemptInfoResponse {
    return ComprobanteAttemptInfoResponse(
        id = this.id.value,
        ventaId = this.ventaId.value,
        tipoComprobante = this.tipoComprobante,
        puntoVenta = this.puntoVenta,
        tipoOperacion = this.tipoOperacion,
        estado = this.estado,
        fechaIntento = this.fechaIntento,
        tiempoRespuestaMs = this.tiempoRespuestaMs,
        cae = this.cae,
        numeroComprobante = this.numeroComprobante,
        fechaVencimientoCae = this.fechaVencimientoCae,
        comprobanteId = this.comprobanteId?.value,
        codigoError = this.codigoError,
        mensajeError = this.mensajeError,
        observacionesAfip = this.observacionesAfip
    )
}
