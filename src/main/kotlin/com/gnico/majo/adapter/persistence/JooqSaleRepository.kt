package com.gnico.majo.adapter.persistence

import com.gnico.majo.infrastructure.config.Database
import com.gnico.majo.application.domain.model.Comprobante
import com.gnico.majo.application.domain.model.ComprobanteAsociado
import com.gnico.majo.application.domain.model.ComprobanteAttempt
import com.gnico.majo.application.domain.model.EstadoIntento
import com.gnico.majo.application.domain.model.TipoOperacionAfip
import com.gnico.majo.application.domain.model.TipoAutorizacion
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Sale
import com.gnico.majo.application.domain.model.SaleItem
import com.gnico.majo.application.domain.model.Usuario
import com.gnico.majo.application.port.out.SaleRepositoryPort
import com.gnico.majo.application.port.out.SaleFilterCriteria
import com.gnico.majo.application.port.out.PageRequest
import com.gnico.majo.application.port.out.PageResult
import com.gnico.majo.jooq.generated.tables.Comprobantes.Companion.COMPROBANTES
import com.gnico.majo.jooq.generated.tables.ComprobanteAttempts.Companion.COMPROBANTE_ATTEMPTS
import com.gnico.majo.jooq.generated.tables.DetallesVenta.Companion.DETALLES_VENTA
import com.gnico.majo.jooq.generated.tables.Usuarios.Companion.USUARIOS
import com.gnico.majo.jooq.generated.tables.Ventas.Companion.VENTAS
import org.jooq.impl.DSL
import org.jooq.Condition
import java.time.LocalDateTime
import java.time.LocalDate
import kotlin.math.ceil
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString

class JooqSaleRepository : SaleRepositoryPort {
    override fun saveSale(sale: Sale): Id {
        return Database.dsl.transactionResult { config ->
            val ctx = DSL.using(config)

            // Guardar venta
            val ventaRecord = ctx.newRecord(VENTAS).apply {
                numeroVenta = sale.numeroVenta
                usuarioUsername = sale.usuario.username
                fechaVenta = sale.fechaVenta
                montoTotal = sale.montoTotal
                comprobanteEmitido = sale.comprobanteEmitido
                medioPago = sale.medioPago
                porcentajeDescuento = sale.porcentajeDescuento
                codigoTicketBalanza = sale.codigoTicketBalanza
                idTicketBalanza = sale.idTicketBalanza
                cancelada = sale.cancelada
                fechaCancelacion = sale.fechaCancelacion
                usuarioCancelacion = sale.usuarioCancelacion
                motivoCancelacion = sale.motivoCancelacion
                notaCreditoGenerada = sale.notaCreditoGenerada
                creadoEn = LocalDateTime.now()
            }
            ventaRecord.store()

            // Guardar detalles
            sale.items.forEach { item ->
                ctx.newRecord(DETALLES_VENTA).apply {
                    ventaId = ventaRecord.id
                    productoNombre = item.productoNombre
                    cantidad = item.cantidad
                    precioUnitario = item.precioUnitario
                    tipoIvaId = item.tipoIva.id
                    subtotal = item.subtotal
                    baseImp = item.baseImp
                    importeIva = item.importeIva
                    creadoEn = LocalDateTime.now()
                }.store()
            }

            Id(ventaRecord.id ?: throw IllegalStateException("Error al guardar la venta"))
        }
    }

    override fun findSaleById(id: Id): Sale? {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            val saleData = dsl.select(
                VENTAS.ID,
                VENTAS.NUMERO_VENTA,
                VENTAS.USUARIO_USERNAME,
                VENTAS.FECHA_VENTA,
                VENTAS.MONTO_TOTAL,
                VENTAS.COMPROBANTE_EMITIDO,
                VENTAS.MEDIO_PAGO,
                VENTAS.PORCENTAJE_DESCUENTO,
                VENTAS.CODIGO_TICKET_BALANZA,
                VENTAS.ID_TICKET_BALANZA,
                VENTAS.CANCELADA,
                VENTAS.FECHA_CANCELACION,
                VENTAS.USUARIO_CANCELACION,
                VENTAS.MOTIVO_CANCELACION,
                VENTAS.NOTA_CREDITO_GENERADA,
                USUARIOS.NOMBRE,
                USUARIOS.NOMBRE_DISPLAY,
                USUARIOS.ACTIVO
            )
                .from(VENTAS)
                .join(USUARIOS).on(VENTAS.USUARIO_USERNAME.eq(USUARIOS.USERNAME))
                .where(VENTAS.ID.eq(id.value))
                .fetchOne() ?: return@transactionResult null

            val usuario = Usuario(
                username = saleData[VENTAS.USUARIO_USERNAME]!!,
                nombre = saleData[USUARIOS.NOMBRE]!!,
                nombreDisplay = saleData[USUARIOS.NOMBRE_DISPLAY]!!,
                activo = saleData[USUARIOS.ACTIVO] ?: true
            )

            val items = dsl.selectFrom(DETALLES_VENTA)
                .where(DETALLES_VENTA.VENTA_ID.eq(id.value))
                .fetch()
                .map { record ->
                    SaleItem.fromPersistence(
                        productoNombre = record.productoNombre!!,
                        cantidad = record.cantidad!!,
                        precioUnitario = record.precioUnitario!!,
                        tipoIvaId = record.tipoIvaId!!,
                        subtotal = record.subtotal!!,
                        baseImp = record.baseImp!!,
                        importeIva = record.importeIva!!
                    )
                }

            Sale.fromPersistence(
                id = Id(saleData[VENTAS.ID]!!),
                numeroVenta = saleData[VENTAS.NUMERO_VENTA]!!,
                usuario = usuario,
                fechaVenta = saleData[VENTAS.FECHA_VENTA]!!,
                montoTotal = saleData[VENTAS.MONTO_TOTAL]!!,
                comprobanteEmitido = saleData[VENTAS.COMPROBANTE_EMITIDO] ?: false,
                medioPago = saleData[VENTAS.MEDIO_PAGO] ?: "EFECTIVO",
                porcentajeDescuento = saleData[VENTAS.PORCENTAJE_DESCUENTO],
                codigoTicketBalanza = saleData[VENTAS.CODIGO_TICKET_BALANZA],
                idTicketBalanza = saleData[VENTAS.ID_TICKET_BALANZA],
                cancelada = saleData[VENTAS.CANCELADA] ?: false,
                fechaCancelacion = saleData[VENTAS.FECHA_CANCELACION],
                usuarioCancelacion = saleData[VENTAS.USUARIO_CANCELACION],
                motivoCancelacion = saleData[VENTAS.MOTIVO_CANCELACION],
                notaCreditoGenerada = saleData[VENTAS.NOTA_CREDITO_GENERADA] ?: false,
                items = items
            )
        }
    }

    override fun saveComprobante(comprobante: Comprobante): Id {
        return Database.dsl.transactionResult { config ->
            val ctx = DSL.using(config)

            val record = ctx.newRecord(COMPROBANTES).apply {
                ventaId = comprobante.venta.value
                tipoComprobante = comprobante.tipoComprobante
                puntoVenta = comprobante.puntoVenta
                // Usar el número real del comprobante (obtenido del repositorio de numeración)
                this.numeroComprobante = comprobante.numeroComprobante
                cae = comprobante.cae
                fechaEmision = comprobante.fechaEmision
                fechaVencimientoCae = comprobante.fechaVencimientoCae
                impTotal = comprobante.impTotal
                impTotConc = comprobante.impTotConc
                impNeto = comprobante.impNeto
                impIva = comprobante.impIva
                impTrib = comprobante.impTrib
                monId = comprobante.monId
                monCotiz = comprobante.monCotiz
                estado = comprobante.estado
                tipoAutorizacion = comprobante.tipoAutorizacion.name

                // Asignar el campo caea_utilizado para comprobantes offline
                caeaUtilizado = comprobante.caeaUtilizado

                // Buscar el ID del comprobante asociado si existe
                if (comprobante.comprobanteAsociado != null) {
                    val comprobanteAsociadoId = ctx.select(COMPROBANTES.ID)
                        .from(COMPROBANTES)
                        .where(COMPROBANTES.PUNTO_VENTA.eq(comprobante.comprobanteAsociado.puntoVenta))
                        .and(COMPROBANTES.NUMERO_COMPROBANTE.eq(comprobante.comprobanteAsociado.numeroComprobante))
                        .and(COMPROBANTES.TIPO_COMPROBANTE.eq(comprobante.comprobanteAsociado.tipo))
                        .fetchOne(COMPROBANTES.ID)

                    // Convertir el ID String a Int para el campo comprobante_relacionado_id
                    comprobanteRelacionadoId = comprobanteAsociadoId?.let { it.toString().toIntOrNull() }
                }

                creadoEn = LocalDateTime.now()
            }
            record.store()

            Id(record.id ?: throw IllegalStateException("Error al guardar el comprobante"))
        }
    }

    override fun updateComprobanteWithAfipData(comprobanteId: Id, numeroComprobante: Long, cae: String, estado: String): Boolean {
        return Database.dsl.transactionResult { config ->
            val ctx = DSL.using(config)
            val updated = ctx.update(COMPROBANTES)
                .set(COMPROBANTES.NUMERO_COMPROBANTE, numeroComprobante.toInt())
                .set(COMPROBANTES.CAE, cae)
                .set(COMPROBANTES.ESTADO, estado)
                .where(COMPROBANTES.ID.eq(comprobanteId.value))
                .execute()

            updated > 0
        }
    }

    override fun findComprobanteById(id: Id): Comprobante? {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectFrom(COMPROBANTES)
                .where(COMPROBANTES.ID.eq(id.value))
                .fetchOne()
                ?.let { record ->
                    val comprobanteAsociado = loadComprobanteAsociado(record.comprobanteRelacionadoId)

                    Comprobante.fromPersistence(
                        id = Id(record.id!!),
                        venta = Id(record.ventaId!!),
                        tipoComprobante = record.tipoComprobante!!,
                        puntoVenta = record.puntoVenta!!,
                        numeroComprobante = record.numeroComprobante!!,
                        cae = record.cae!!,
                        fechaEmision = record.fechaEmision!!,
                        fechaVencimientoCae = record.fechaVencimientoCae!!,
                        impTotal = record.impTotal!!,
                        impTotConc = record.impTotConc!!,
                        impNeto = record.impNeto!!,
                        impIva = record.impIva!!,
                        impTrib = record.impTrib!!,
                        monId = record.monId!!,
                        monCotiz = record.monCotiz!!,
                        estado = record.estado!!,
                        tipoAutorizacion = TipoAutorizacion.fromString(record.tipoAutorizacion ?: "CAE") ?: TipoAutorizacion.CAE,
                        comprobanteAsociado = comprobanteAsociado,
                        caeaUtilizado = record.caeaUtilizado
                    )
                }
        }
    }

    override fun updateComprobanteEmitido(ventaId: Id, emitido: Boolean): Boolean {
        return Database.dsl.transactionResult { config ->
            val ctx = DSL.using(config)
            val rowsUpdated = ctx.update(VENTAS)
                .set(VENTAS.COMPROBANTE_EMITIDO, emitido)
                .where(VENTAS.ID.eq(ventaId.value))
                .execute()
            rowsUpdated > 0
        }
    }

    override fun findSaleByNumeroVenta(numeroVenta: String): Sale? {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            val saleData = dsl.select(
                VENTAS.ID,
                VENTAS.NUMERO_VENTA,
                VENTAS.USUARIO_USERNAME,
                VENTAS.FECHA_VENTA,
                VENTAS.MONTO_TOTAL,
                VENTAS.COMPROBANTE_EMITIDO,
                VENTAS.MEDIO_PAGO,
                VENTAS.PORCENTAJE_DESCUENTO,
                VENTAS.CODIGO_TICKET_BALANZA,
                VENTAS.ID_TICKET_BALANZA,
                VENTAS.CANCELADA,
                VENTAS.FECHA_CANCELACION,
                VENTAS.USUARIO_CANCELACION,
                VENTAS.MOTIVO_CANCELACION,
                VENTAS.NOTA_CREDITO_GENERADA,
                USUARIOS.NOMBRE,
                USUARIOS.NOMBRE_DISPLAY,
                USUARIOS.ACTIVO
            )
                .from(VENTAS)
                .join(USUARIOS).on(VENTAS.USUARIO_USERNAME.eq(USUARIOS.USERNAME))
                .where(VENTAS.NUMERO_VENTA.eq(numeroVenta))
                .fetchOne() ?: return@transactionResult null

            val ventaId = Id(saleData[VENTAS.ID]!!)

            val usuario = Usuario(
                username = saleData[VENTAS.USUARIO_USERNAME]!!,
                nombre = saleData[USUARIOS.NOMBRE]!!,
                nombreDisplay = saleData[USUARIOS.NOMBRE_DISPLAY]!!,
                activo = saleData[USUARIOS.ACTIVO] ?: true
            )

            val items = dsl.selectFrom(DETALLES_VENTA)
                .where(DETALLES_VENTA.VENTA_ID.eq(ventaId.value))
                .fetch()
                .map { record ->
                    SaleItem.fromPersistence(
                        productoNombre = record.productoNombre!!,
                        cantidad = record.cantidad!!,
                        precioUnitario = record.precioUnitario!!,
                        tipoIvaId = record.tipoIvaId!!,
                        subtotal = record.subtotal!!,
                        baseImp = record.baseImp!!,
                        importeIva = record.importeIva!!
                    )
                }

            Sale.fromPersistence(
                id = ventaId,
                numeroVenta = saleData[VENTAS.NUMERO_VENTA]!!,
                usuario = usuario,
                fechaVenta = saleData[VENTAS.FECHA_VENTA]!!,
                montoTotal = saleData[VENTAS.MONTO_TOTAL]!!,
                comprobanteEmitido = saleData[VENTAS.COMPROBANTE_EMITIDO] ?: false,
                medioPago = saleData[VENTAS.MEDIO_PAGO] ?: "EFECTIVO",
                porcentajeDescuento = saleData[VENTAS.PORCENTAJE_DESCUENTO],
                codigoTicketBalanza = saleData[VENTAS.CODIGO_TICKET_BALANZA],
                idTicketBalanza = saleData[VENTAS.ID_TICKET_BALANZA],
                cancelada = saleData[VENTAS.CANCELADA] ?: false,
                fechaCancelacion = saleData[VENTAS.FECHA_CANCELACION],
                usuarioCancelacion = saleData[VENTAS.USUARIO_CANCELACION],
                motivoCancelacion = saleData[VENTAS.MOTIVO_CANCELACION],
                notaCreditoGenerada = saleData[VENTAS.NOTA_CREDITO_GENERADA] ?: false,
                items = items
            )
        }
    }

    override fun findSalesByDateRange(startDate: LocalDateTime, endDate: LocalDateTime): List<Sale> {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            val salesData = dsl.select(
                VENTAS.ID,
                VENTAS.NUMERO_VENTA,
                VENTAS.USUARIO_USERNAME,
                VENTAS.FECHA_VENTA,
                VENTAS.MONTO_TOTAL,
                VENTAS.COMPROBANTE_EMITIDO,
                VENTAS.MEDIO_PAGO,
                VENTAS.PORCENTAJE_DESCUENTO,
                VENTAS.CODIGO_TICKET_BALANZA,
                VENTAS.ID_TICKET_BALANZA,
                VENTAS.CANCELADA,
                VENTAS.FECHA_CANCELACION,
                VENTAS.USUARIO_CANCELACION,
                VENTAS.MOTIVO_CANCELACION,
                VENTAS.NOTA_CREDITO_GENERADA,
                USUARIOS.NOMBRE,
                USUARIOS.NOMBRE_DISPLAY,
                USUARIOS.ACTIVO
            )
                .from(VENTAS)
                .join(USUARIOS).on(VENTAS.USUARIO_USERNAME.eq(USUARIOS.USERNAME))
                .where(VENTAS.FECHA_VENTA.between(startDate, endDate))
                .orderBy(VENTAS.FECHA_VENTA.desc())
                .fetch()

            salesData.map { saleData ->
                val ventaId = Id(saleData[VENTAS.ID]!!)

                val usuario = Usuario(
                    username = saleData[VENTAS.USUARIO_USERNAME]!!,
                    nombre = saleData[USUARIOS.NOMBRE]!!,
                    nombreDisplay = saleData[USUARIOS.NOMBRE_DISPLAY]!!,
                    activo = saleData[USUARIOS.ACTIVO] ?: true
                )

                val items = dsl.selectFrom(DETALLES_VENTA)
                    .where(DETALLES_VENTA.VENTA_ID.eq(ventaId.value))
                    .fetch()
                    .map { record ->
                        SaleItem.fromPersistence(
                            productoNombre = record.productoNombre!!,
                            cantidad = record.cantidad!!,
                            precioUnitario = record.precioUnitario!!,
                            tipoIvaId = record.tipoIvaId!!,
                            subtotal = record.subtotal!!,
                            baseImp = record.baseImp!!,
                            importeIva = record.importeIva!!
                        )
                    }

                Sale.fromPersistence(
                    id = ventaId,
                    numeroVenta = saleData[VENTAS.NUMERO_VENTA]!!,
                    usuario = usuario,
                    fechaVenta = saleData[VENTAS.FECHA_VENTA]!!,
                    montoTotal = saleData[VENTAS.MONTO_TOTAL]!!,
                    comprobanteEmitido = saleData[VENTAS.COMPROBANTE_EMITIDO] ?: false,
                    medioPago = saleData[VENTAS.MEDIO_PAGO] ?: "EFECTIVO",
                    porcentajeDescuento = saleData[VENTAS.PORCENTAJE_DESCUENTO],
                    codigoTicketBalanza = saleData[VENTAS.CODIGO_TICKET_BALANZA],
                    idTicketBalanza = saleData[VENTAS.ID_TICKET_BALANZA],
                    cancelada = saleData[VENTAS.CANCELADA] ?: false,
                    fechaCancelacion = saleData[VENTAS.FECHA_CANCELACION],
                    usuarioCancelacion = saleData[VENTAS.USUARIO_CANCELACION],
                    motivoCancelacion = saleData[VENTAS.MOTIVO_CANCELACION],
                    notaCreditoGenerada = saleData[VENTAS.NOTA_CREDITO_GENERADA] ?: false,
                    items = items
                )
            }
        }
    }

    override fun findSalesByUsuario(username: String): List<Sale> {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            val salesData = dsl.select(
                VENTAS.ID,
                VENTAS.NUMERO_VENTA,
                VENTAS.USUARIO_USERNAME,
                VENTAS.FECHA_VENTA,
                VENTAS.MONTO_TOTAL,
                VENTAS.COMPROBANTE_EMITIDO,
                VENTAS.MEDIO_PAGO,
                VENTAS.PORCENTAJE_DESCUENTO,
                VENTAS.CODIGO_TICKET_BALANZA,
                VENTAS.ID_TICKET_BALANZA,
                VENTAS.CANCELADA,
                VENTAS.FECHA_CANCELACION,
                VENTAS.USUARIO_CANCELACION,
                VENTAS.MOTIVO_CANCELACION,
                VENTAS.NOTA_CREDITO_GENERADA,
                USUARIOS.NOMBRE,
                USUARIOS.NOMBRE_DISPLAY,
                USUARIOS.ACTIVO
            )
                .from(VENTAS)
                .join(USUARIOS).on(VENTAS.USUARIO_USERNAME.eq(USUARIOS.USERNAME))
                .where(VENTAS.USUARIO_USERNAME.eq(username))
                .orderBy(VENTAS.FECHA_VENTA.desc())
                .fetch()

            salesData.map { saleData ->
                val ventaId = Id(saleData[VENTAS.ID]!!)

                val usuario = Usuario(
                    username = saleData[VENTAS.USUARIO_USERNAME]!!,
                    nombre = saleData[USUARIOS.NOMBRE]!!,
                    nombreDisplay = saleData[USUARIOS.NOMBRE_DISPLAY]!!,
                    activo = saleData[USUARIOS.ACTIVO] ?: true
                )

                val items = dsl.selectFrom(DETALLES_VENTA)
                    .where(DETALLES_VENTA.VENTA_ID.eq(ventaId.value))
                    .fetch()
                    .map { record ->
                        SaleItem.fromPersistence(
                            productoNombre = record.productoNombre!!,
                            cantidad = record.cantidad!!,
                            precioUnitario = record.precioUnitario!!,
                            tipoIvaId = record.tipoIvaId!!,
                            subtotal = record.subtotal!!,
                            baseImp = record.baseImp!!,
                            importeIva = record.importeIva!!
                        )
                    }

                Sale.fromPersistence(
                    id = ventaId,
                    numeroVenta = saleData[VENTAS.NUMERO_VENTA]!!,
                    usuario = usuario,
                    fechaVenta = saleData[VENTAS.FECHA_VENTA]!!,
                    montoTotal = saleData[VENTAS.MONTO_TOTAL]!!,
                    comprobanteEmitido = saleData[VENTAS.COMPROBANTE_EMITIDO] ?: false,
                    medioPago = saleData[VENTAS.MEDIO_PAGO] ?: "EFECTIVO",
                    porcentajeDescuento = saleData[VENTAS.PORCENTAJE_DESCUENTO],
                    codigoTicketBalanza = saleData[VENTAS.CODIGO_TICKET_BALANZA],
                    idTicketBalanza = saleData[VENTAS.ID_TICKET_BALANZA],
                    cancelada = saleData[VENTAS.CANCELADA] ?: false,
                    fechaCancelacion = saleData[VENTAS.FECHA_CANCELACION],
                    usuarioCancelacion = saleData[VENTAS.USUARIO_CANCELACION],
                    motivoCancelacion = saleData[VENTAS.MOTIVO_CANCELACION],
                    notaCreditoGenerada = saleData[VENTAS.NOTA_CREDITO_GENERADA] ?: false,
                    items = items
                )
            }
        }
    }



    override fun findComprobantesByVentaId(ventaId: Id): List<Comprobante> {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectFrom(COMPROBANTES)
                .where(COMPROBANTES.VENTA_ID.eq(ventaId.value))
                .orderBy(COMPROBANTES.FECHA_EMISION.desc())
                .fetch()
                .map { record ->
                    val comprobanteAsociado = loadComprobanteAsociado(record.comprobanteRelacionadoId)

                    Comprobante.fromPersistence(
                        id = Id(record.id!!),
                        venta = Id(record.ventaId!!),
                        tipoComprobante = record.tipoComprobante!!,
                        puntoVenta = record.puntoVenta!!,
                        numeroComprobante = record.numeroComprobante!!,
                        cae = record.cae!!,
                        fechaEmision = record.fechaEmision!!,
                        fechaVencimientoCae = record.fechaVencimientoCae!!,
                        impTotal = record.impTotal!!,
                        impTotConc = record.impTotConc!!,
                        impNeto = record.impNeto!!,
                        impIva = record.impIva!!,
                        impTrib = record.impTrib!!,
                        monId = record.monId!!,
                        monCotiz = record.monCotiz!!,
                        estado = record.estado!!,
                        tipoAutorizacion = TipoAutorizacion.fromString(record.tipoAutorizacion ?: "CAE") ?: TipoAutorizacion.CAE,
                        comprobanteAsociado = comprobanteAsociado,
                        caeaUtilizado = record.caeaUtilizado
                    )
                }
        }
    }

    override fun findComprobanteByNumero(puntoVenta: Int, numeroComprobante: Int): Comprobante? {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectFrom(COMPROBANTES)
                .where(COMPROBANTES.PUNTO_VENTA.eq(puntoVenta))
                .and(COMPROBANTES.NUMERO_COMPROBANTE.eq(numeroComprobante))
                .fetchOne()
                ?.let { record ->
                    val comprobanteAsociado = loadComprobanteAsociado(record.comprobanteRelacionadoId)

                    Comprobante.fromPersistence(
                        id = Id(record.id!!),
                        venta = Id(record.ventaId!!),
                        tipoComprobante = record.tipoComprobante!!,
                        puntoVenta = record.puntoVenta!!,
                        numeroComprobante = record.numeroComprobante!!,
                        cae = record.cae!!,
                        fechaEmision = record.fechaEmision!!,
                        fechaVencimientoCae = record.fechaVencimientoCae!!,
                        impTotal = record.impTotal!!,
                        impTotConc = record.impTotConc!!,
                        impNeto = record.impNeto!!,
                        impIva = record.impIva!!,
                        impTrib = record.impTrib!!,
                        monId = record.monId!!,
                        monCotiz = record.monCotiz!!,
                        estado = record.estado!!,
                        tipoAutorizacion = TipoAutorizacion.fromString(record.tipoAutorizacion ?: "CAE") ?: TipoAutorizacion.CAE,
                        comprobanteAsociado = comprobanteAsociado,
                        caeaUtilizado = record.caeaUtilizado
                    )
                }
        }
    }

    override fun countSalesWithoutComprobante(): Int {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectCount()
                .from(VENTAS)
                .where(VENTAS.COMPROBANTE_EMITIDO.eq(false))
                .fetchOne(0, Int::class.java) ?: 0
        }
    }

    override fun findSalesWithoutComprobante(limit: Int): List<Sale> {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            val salesData = dsl.select(
                VENTAS.ID,
                VENTAS.NUMERO_VENTA,
                VENTAS.USUARIO_USERNAME,
                VENTAS.FECHA_VENTA,
                VENTAS.MONTO_TOTAL,
                VENTAS.COMPROBANTE_EMITIDO,
                VENTAS.MEDIO_PAGO,
                VENTAS.PORCENTAJE_DESCUENTO,
                VENTAS.CODIGO_TICKET_BALANZA,
                VENTAS.ID_TICKET_BALANZA,
                VENTAS.CANCELADA,
                VENTAS.FECHA_CANCELACION,
                VENTAS.USUARIO_CANCELACION,
                VENTAS.MOTIVO_CANCELACION,
                VENTAS.NOTA_CREDITO_GENERADA,
                USUARIOS.NOMBRE,
                USUARIOS.NOMBRE_DISPLAY,
                USUARIOS.ACTIVO
            )
                .from(VENTAS)
                .join(USUARIOS).on(VENTAS.USUARIO_USERNAME.eq(USUARIOS.USERNAME))
                .where(VENTAS.COMPROBANTE_EMITIDO.eq(false))
                .orderBy(VENTAS.FECHA_VENTA.desc())
                .limit(limit)
                .fetch()

            salesData.map { saleData ->
                val ventaId = Id(saleData[VENTAS.ID]!!)

                val usuario = Usuario(
                    username = saleData[VENTAS.USUARIO_USERNAME]!!,
                    nombre = saleData[USUARIOS.NOMBRE]!!,
                    nombreDisplay = saleData[USUARIOS.NOMBRE_DISPLAY]!!,
                    activo = saleData[USUARIOS.ACTIVO] ?: true
                )

                val items = dsl.selectFrom(DETALLES_VENTA)
                    .where(DETALLES_VENTA.VENTA_ID.eq(ventaId.value))
                    .fetch()
                    .map { record ->
                        SaleItem.fromPersistence(
                            productoNombre = record.productoNombre!!,
                            cantidad = record.cantidad!!,
                            precioUnitario = record.precioUnitario!!,
                            tipoIvaId = record.tipoIvaId!!,
                            subtotal = record.subtotal!!,
                            baseImp = record.baseImp!!,
                            importeIva = record.importeIva!!
                        )
                    }

                Sale.fromPersistence(
                    id = ventaId,
                    numeroVenta = saleData[VENTAS.NUMERO_VENTA]!!,
                    usuario = usuario,
                    fechaVenta = saleData[VENTAS.FECHA_VENTA]!!,
                    montoTotal = saleData[VENTAS.MONTO_TOTAL]!!,
                    comprobanteEmitido = saleData[VENTAS.COMPROBANTE_EMITIDO] ?: false,
                    medioPago = saleData[VENTAS.MEDIO_PAGO] ?: "EFECTIVO",
                    porcentajeDescuento = saleData[VENTAS.PORCENTAJE_DESCUENTO],
                    codigoTicketBalanza = saleData[VENTAS.CODIGO_TICKET_BALANZA],
                    idTicketBalanza = saleData[VENTAS.ID_TICKET_BALANZA],
                    cancelada = saleData[VENTAS.CANCELADA] ?: false,
                    fechaCancelacion = saleData[VENTAS.FECHA_CANCELACION],
                    usuarioCancelacion = saleData[VENTAS.USUARIO_CANCELACION],
                    motivoCancelacion = saleData[VENTAS.MOTIVO_CANCELACION],
                    notaCreditoGenerada = saleData[VENTAS.NOTA_CREDITO_GENERADA] ?: false,
                    items = items
                )
            }
        }
    }

    override fun findSalesWithFilters(criteria: SaleFilterCriteria, pageRequest: PageRequest): PageResult<Sale> {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            // Construir condiciones de filtrado
            val conditions = mutableListOf<Condition>()

            // Filtro por fechas
            if (criteria.fechaDesde != null && criteria.fechaHasta != null) {
                conditions.add(VENTAS.FECHA_VENTA.between(criteria.fechaDesde, criteria.fechaHasta))
            } else if (criteria.fechaDesde != null) {
                conditions.add(VENTAS.FECHA_VENTA.greaterOrEqual(criteria.fechaDesde))
            } else if (criteria.fechaHasta != null) {
                conditions.add(VENTAS.FECHA_VENTA.lessOrEqual(criteria.fechaHasta))
            }

            // Filtro por usuarios
            if (!criteria.usuarios.isNullOrEmpty()) {
                conditions.add(VENTAS.USUARIO_USERNAME.`in`(criteria.usuarios))
            }

            // Filtro por comprobante emitido
            if (criteria.comprobanteEmitido != null) {
                conditions.add(VENTAS.COMPROBANTE_EMITIDO.eq(criteria.comprobanteEmitido))
            }

            // Filtro por medios de pago
            if (!criteria.mediosPago.isNullOrEmpty()) {
                conditions.add(VENTAS.MEDIO_PAGO.`in`(criteria.mediosPago))
            }

            // Filtro por ventas canceladas (por defecto excluir canceladas)
            if (!criteria.incluirCanceladas) {
                conditions.add(VENTAS.CANCELADA.eq(false))
            }

            // Combinar todas las condiciones
            val whereCondition = if (conditions.isNotEmpty()) {
                conditions.reduce { acc, condition -> acc.and(condition) }
            } else {
                DSL.trueCondition()
            }

            // Contar total de elementos
            val totalElements = dsl.selectCount()
                .from(VENTAS)
                .join(USUARIOS).on(VENTAS.USUARIO_USERNAME.eq(USUARIOS.USERNAME))
                .where(whereCondition)
                .fetchOne(0, Long::class.java) ?: 0L

            // Calcular offset y total de páginas
            val offset = (pageRequest.page - 1) * pageRequest.size
            val totalPages = ceil(totalElements.toDouble() / pageRequest.size).toInt()

            // Obtener datos paginados
            val salesData = dsl.select(
                VENTAS.ID,
                VENTAS.NUMERO_VENTA,
                VENTAS.USUARIO_USERNAME,
                VENTAS.FECHA_VENTA,
                VENTAS.MONTO_TOTAL,
                VENTAS.COMPROBANTE_EMITIDO,
                VENTAS.MEDIO_PAGO,
                VENTAS.PORCENTAJE_DESCUENTO,
                VENTAS.CODIGO_TICKET_BALANZA,
                VENTAS.ID_TICKET_BALANZA,
                VENTAS.CANCELADA,
                VENTAS.FECHA_CANCELACION,
                VENTAS.USUARIO_CANCELACION,
                VENTAS.MOTIVO_CANCELACION,
                VENTAS.NOTA_CREDITO_GENERADA,
                USUARIOS.NOMBRE,
                USUARIOS.NOMBRE_DISPLAY,
                USUARIOS.ACTIVO
            )
                .from(VENTAS)
                .join(USUARIOS).on(VENTAS.USUARIO_USERNAME.eq(USUARIOS.USERNAME))
                .where(whereCondition)
                .orderBy(VENTAS.FECHA_VENTA.desc())
                .limit(pageRequest.size)
                .offset(offset)
                .fetch()

            val sales = salesData.map { saleData ->
                // Obtener items de la venta
                val items = dsl.selectFrom(DETALLES_VENTA)
                    .where(DETALLES_VENTA.VENTA_ID.eq(saleData[VENTAS.ID]))
                    .fetch()
                    .map { record ->
                        SaleItem.fromPersistence(
                            productoNombre = record.productoNombre!!,
                            cantidad = record.cantidad!!,
                            precioUnitario = record.precioUnitario!!,
                            tipoIvaId = record.tipoIvaId!!,
                            subtotal = record.subtotal!!,
                            baseImp = record.baseImp!!,
                            importeIva = record.importeIva!!
                        )
                    }

                // Ya no manejamos clientes - solo consumidor final

                // Crear usuario
                val usuario = Usuario(
                    username = saleData[VENTAS.USUARIO_USERNAME]!!,
                    nombre = saleData[USUARIOS.NOMBRE] ?: "",
                    nombreDisplay = saleData[USUARIOS.NOMBRE_DISPLAY] ?: "",
                    activo = saleData[USUARIOS.ACTIVO] ?: true
                )

                Sale.fromPersistence(
                    id = Id(saleData[VENTAS.ID]!!),
                    numeroVenta = saleData[VENTAS.NUMERO_VENTA]!!,
                    usuario = usuario,
                    fechaVenta = saleData[VENTAS.FECHA_VENTA]!!,
                    montoTotal = saleData[VENTAS.MONTO_TOTAL]!!,
                    comprobanteEmitido = saleData[VENTAS.COMPROBANTE_EMITIDO] ?: false,
                    medioPago = saleData[VENTAS.MEDIO_PAGO] ?: "EFECTIVO",
                    porcentajeDescuento = saleData[VENTAS.PORCENTAJE_DESCUENTO],
                    codigoTicketBalanza = saleData[VENTAS.CODIGO_TICKET_BALANZA],
                    idTicketBalanza = saleData[VENTAS.ID_TICKET_BALANZA],
                    cancelada = saleData[VENTAS.CANCELADA] ?: false,
                    fechaCancelacion = saleData[VENTAS.FECHA_CANCELACION],
                    usuarioCancelacion = saleData[VENTAS.USUARIO_CANCELACION],
                    motivoCancelacion = saleData[VENTAS.MOTIVO_CANCELACION],
                    notaCreditoGenerada = saleData[VENTAS.NOTA_CREDITO_GENERADA] ?: false,
                    items = items
                )
            }

            PageResult(
                content = sales,
                page = pageRequest.page,
                size = pageRequest.size,
                totalElements = totalElements,
                totalPages = totalPages
            )
        }
    }

    // ========== Métodos para ComprobanteAttempt ==========

    override fun saveComprobanteAttempt(attempt: ComprobanteAttempt): Id {
        return Database.dsl.transactionResult { config ->
            val ctx = DSL.using(config)

            val record = ctx.newRecord(COMPROBANTE_ATTEMPTS).apply {
                ventaId = attempt.ventaId.value
                tipoComprobante = attempt.tipoComprobante
                puntoVenta = attempt.puntoVenta
                tipoOperacion = attempt.tipoOperacion.name
                estado = attempt.estado.name
                cae = attempt.cae
                numeroComprobante = attempt.numeroComprobante
                fechaVencimientoCae = attempt.fechaVencimientoCae
                codigoError = attempt.codigoError
                mensajeError = attempt.mensajeError
                observacionesAfip = if (attempt.observacionesAfip.isNotEmpty()) {
                    Json.encodeToString(attempt.observacionesAfip)
                } else null
                fechaIntento = attempt.fechaIntento
                tiempoRespuestaMs = attempt.tiempoRespuestaMs
                comprobanteId = attempt.comprobanteId?.value
                creadoEn = LocalDateTime.now()
            }
            record.store()

            Id(record.id ?: throw IllegalStateException("Error al guardar el intento de comprobante"))
        }
    }

    override fun findComprobanteAttemptsByVentaId(ventaId: Id): List<ComprobanteAttempt> {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectFrom(COMPROBANTE_ATTEMPTS)
                .where(COMPROBANTE_ATTEMPTS.VENTA_ID.eq(ventaId.value))
                .orderBy(COMPROBANTE_ATTEMPTS.FECHA_INTENTO.desc())
                .fetch()
                .map { record -> mapToComprobanteAttempt(record) }
        }
    }

    override fun findFailedComprobanteAttemptsByVentaId(ventaId: Id): List<ComprobanteAttempt> {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectFrom(COMPROBANTE_ATTEMPTS)
                .where(COMPROBANTE_ATTEMPTS.VENTA_ID.eq(ventaId.value))
                .and(COMPROBANTE_ATTEMPTS.ESTADO.eq(EstadoIntento.FALLIDO.name))
                .orderBy(COMPROBANTE_ATTEMPTS.FECHA_INTENTO.desc())
                .fetch()
                .map { record -> mapToComprobanteAttempt(record) }
        }
    }

    override fun updateComprobanteAttemptWithResult(attemptId: Id, comprobanteId: Id?): Boolean {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            val updated = dsl.update(COMPROBANTE_ATTEMPTS)
                .set(COMPROBANTE_ATTEMPTS.COMPROBANTE_ID, comprobanteId?.value)
                .set(COMPROBANTE_ATTEMPTS.ESTADO, EstadoIntento.EXITOSO.name)
                .where(COMPROBANTE_ATTEMPTS.ID.eq(attemptId.value))
                .execute()
            updated > 0
        }
    }

    private fun mapToComprobanteAttempt(record: com.gnico.majo.jooq.generated.tables.records.ComprobanteAttemptsRecord): ComprobanteAttempt {
        val observaciones = record.observacionesAfip?.let { json ->
            try {
                Json.decodeFromString<List<String>>(json)
            } catch (e: Exception) {
                emptyList()
            }
        } ?: emptyList()

        return ComprobanteAttempt.fromPersistence(
            id = Id(record.id!!),
            ventaId = Id(record.ventaId!!),
            tipoComprobante = record.tipoComprobante!!,
            puntoVenta = record.puntoVenta!!,
            tipoOperacion = TipoOperacionAfip.valueOf(record.tipoOperacion!!),
            estado = EstadoIntento.valueOf(record.estado!!),
            cae = record.cae,
            numeroComprobante = record.numeroComprobante,
            fechaVencimientoCae = record.fechaVencimientoCae,
            codigoError = record.codigoError,
            mensajeError = record.mensajeError,
            observacionesAfip = observaciones,
            fechaIntento = record.fechaIntento!!,
            tiempoRespuestaMs = record.tiempoRespuestaMs,
            comprobanteId = record.comprobanteId?.let { Id(it) }
        )
    }

    // ========== Métodos para cancelación de ventas ==========

    override fun cancelSale(ventaId: Id, usuarioCancelacion: String, motivo: String): Boolean {
        return Database.dsl.transactionResult { config ->
            val ctx = DSL.using(config)
            val rowsUpdated = ctx.update(VENTAS)
                .set(VENTAS.CANCELADA, true)
                .set(VENTAS.FECHA_CANCELACION, LocalDateTime.now())
                .set(VENTAS.USUARIO_CANCELACION, usuarioCancelacion)
                .set(VENTAS.MOTIVO_CANCELACION, motivo)
                .where(VENTAS.ID.eq(ventaId.value))
                .and(VENTAS.CANCELADA.eq(false)) // Solo cancelar si no está ya cancelada
                .execute()
            rowsUpdated > 0
        }
    }

    override fun findCancelledSales(limit: Int): List<Sale> {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            val salesData = dsl.select(
                VENTAS.ID,
                VENTAS.NUMERO_VENTA,
                VENTAS.USUARIO_USERNAME,
                VENTAS.FECHA_VENTA,
                VENTAS.MONTO_TOTAL,
                VENTAS.COMPROBANTE_EMITIDO,
                VENTAS.MEDIO_PAGO,
                VENTAS.PORCENTAJE_DESCUENTO,
                VENTAS.CODIGO_TICKET_BALANZA,
                VENTAS.ID_TICKET_BALANZA,
                VENTAS.CANCELADA,
                VENTAS.FECHA_CANCELACION,
                VENTAS.USUARIO_CANCELACION,
                VENTAS.MOTIVO_CANCELACION,
                VENTAS.NOTA_CREDITO_GENERADA,
                USUARIOS.NOMBRE,
                USUARIOS.NOMBRE_DISPLAY,
                USUARIOS.ACTIVO
            )
                .from(VENTAS)
                .join(USUARIOS).on(VENTAS.USUARIO_USERNAME.eq(USUARIOS.USERNAME))
                .where(VENTAS.CANCELADA.eq(true))
                .orderBy(VENTAS.FECHA_CANCELACION.desc())
                .limit(limit)
                .fetch()

            salesData.map { saleData ->
                val ventaId = Id(saleData[VENTAS.ID]!!)

                val usuario = Usuario(
                    username = saleData[VENTAS.USUARIO_USERNAME]!!,
                    nombre = saleData[USUARIOS.NOMBRE]!!,
                    nombreDisplay = saleData[USUARIOS.NOMBRE_DISPLAY]!!,
                    activo = saleData[USUARIOS.ACTIVO] ?: true
                )

                val items = dsl.selectFrom(DETALLES_VENTA)
                    .where(DETALLES_VENTA.VENTA_ID.eq(ventaId.value))
                    .fetch()
                    .map { record ->
                        SaleItem.fromPersistence(
                            productoNombre = record.productoNombre!!,
                            cantidad = record.cantidad!!,
                            precioUnitario = record.precioUnitario!!,
                            tipoIvaId = record.tipoIvaId!!,
                            subtotal = record.subtotal!!,
                            baseImp = record.baseImp!!,
                            importeIva = record.importeIva!!
                        )
                    }

                Sale.fromPersistence(
                    id = ventaId,
                    numeroVenta = saleData[VENTAS.NUMERO_VENTA]!!,
                    usuario = usuario,
                    fechaVenta = saleData[VENTAS.FECHA_VENTA]!!,
                    montoTotal = saleData[VENTAS.MONTO_TOTAL]!!,
                    comprobanteEmitido = saleData[VENTAS.COMPROBANTE_EMITIDO] ?: false,
                    medioPago = saleData[VENTAS.MEDIO_PAGO] ?: "EFECTIVO",
                    porcentajeDescuento = saleData[VENTAS.PORCENTAJE_DESCUENTO],
                    codigoTicketBalanza = saleData[VENTAS.CODIGO_TICKET_BALANZA],
                    idTicketBalanza = saleData[VENTAS.ID_TICKET_BALANZA],
                    cancelada = saleData[VENTAS.CANCELADA] ?: false,
                    fechaCancelacion = saleData[VENTAS.FECHA_CANCELACION],
                    usuarioCancelacion = saleData[VENTAS.USUARIO_CANCELACION],
                    motivoCancelacion = saleData[VENTAS.MOTIVO_CANCELACION],
                    notaCreditoGenerada = saleData[VENTAS.NOTA_CREDITO_GENERADA] ?: false,
                    items = items
                )
            }
        }
    }

    override fun countCancelledSales(): Int {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectCount()
                .from(VENTAS)
                .where(VENTAS.CANCELADA.eq(true))
                .fetchOne(0, Int::class.java) ?: 0
        }
    }

    /**
     * Marca que se ha generado exitosamente la nota de crédito para una venta cancelada
     */
    override fun markNotaCreditoGenerada(ventaId: Id): Boolean {
        return Database.dsl.transactionResult { config ->
            val ctx = DSL.using(config)
            val rowsUpdated = ctx.update(VENTAS)
                .set(VENTAS.NOTA_CREDITO_GENERADA, true)
                .where(VENTAS.ID.eq(ventaId.value))
                .and(VENTAS.CANCELADA.eq(true)) // Solo para ventas canceladas
                .and(VENTAS.COMPROBANTE_EMITIDO.eq(true)) // Solo para ventas con comprobante
                .execute()
            rowsUpdated > 0
        }
    }

    /**
     * Encuentra ventas canceladas que necesitan nota de crédito (tienen comprobante pero no se generó la nota)
     */
    override fun findSalesPendingNotaCredito(limit: Int): List<Sale> {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            val salesData = dsl.select(
                VENTAS.ID,
                VENTAS.NUMERO_VENTA,
                VENTAS.USUARIO_USERNAME,
                VENTAS.FECHA_VENTA,
                VENTAS.MONTO_TOTAL,
                VENTAS.COMPROBANTE_EMITIDO,
                VENTAS.MEDIO_PAGO,
                VENTAS.PORCENTAJE_DESCUENTO,
                VENTAS.CODIGO_TICKET_BALANZA,
                VENTAS.ID_TICKET_BALANZA,
                VENTAS.CANCELADA,
                VENTAS.FECHA_CANCELACION,
                VENTAS.USUARIO_CANCELACION,
                VENTAS.MOTIVO_CANCELACION,
                VENTAS.NOTA_CREDITO_GENERADA,
                USUARIOS.NOMBRE,
                USUARIOS.NOMBRE_DISPLAY,
                USUARIOS.ACTIVO
            )
                .from(VENTAS)
                .join(USUARIOS).on(VENTAS.USUARIO_USERNAME.eq(USUARIOS.USERNAME))
                .where(VENTAS.CANCELADA.eq(true))
                .and(VENTAS.COMPROBANTE_EMITIDO.eq(true))
                .and(VENTAS.NOTA_CREDITO_GENERADA.eq(false))
                .orderBy(VENTAS.FECHA_CANCELACION.desc())
                .limit(limit)
                .fetch()

            salesData.map { saleData ->
                val ventaId = Id(saleData[VENTAS.ID]!!)

                val usuario = Usuario(
                    username = saleData[VENTAS.USUARIO_USERNAME]!!,
                    nombre = saleData[USUARIOS.NOMBRE]!!,
                    nombreDisplay = saleData[USUARIOS.NOMBRE_DISPLAY]!!,
                    activo = saleData[USUARIOS.ACTIVO] ?: true
                )

                val items = dsl.selectFrom(DETALLES_VENTA)
                    .where(DETALLES_VENTA.VENTA_ID.eq(ventaId.value))
                    .fetch()
                    .map { record ->
                        SaleItem.fromPersistence(
                            productoNombre = record.productoNombre!!,
                            cantidad = record.cantidad!!,
                            precioUnitario = record.precioUnitario!!,
                            tipoIvaId = record.tipoIvaId!!,
                            subtotal = record.subtotal!!,
                            baseImp = record.baseImp!!,
                            importeIva = record.importeIva!!
                        )
                    }

                Sale.fromPersistence(
                    id = ventaId,
                    numeroVenta = saleData[VENTAS.NUMERO_VENTA]!!,
                    usuario = usuario,
                    fechaVenta = saleData[VENTAS.FECHA_VENTA]!!,
                    montoTotal = saleData[VENTAS.MONTO_TOTAL]!!,
                    comprobanteEmitido = saleData[VENTAS.COMPROBANTE_EMITIDO] ?: false,
                    medioPago = saleData[VENTAS.MEDIO_PAGO] ?: "EFECTIVO",
                    porcentajeDescuento = saleData[VENTAS.PORCENTAJE_DESCUENTO],
                    codigoTicketBalanza = saleData[VENTAS.CODIGO_TICKET_BALANZA],
                    idTicketBalanza = saleData[VENTAS.ID_TICKET_BALANZA],
                    cancelada = saleData[VENTAS.CANCELADA] ?: false,
                    fechaCancelacion = saleData[VENTAS.FECHA_CANCELACION],
                    usuarioCancelacion = saleData[VENTAS.USUARIO_CANCELACION],
                    motivoCancelacion = saleData[VENTAS.MOTIVO_CANCELACION],
                    notaCreditoGenerada = saleData[VENTAS.NOTA_CREDITO_GENERADA] ?: false,
                    items = items
                )
            }
        }
    }

    /**
     * Cuenta las ventas canceladas que necesitan nota de crédito
     */
    override fun countSalesPendingNotaCredito(): Int {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectCount()
                .from(VENTAS)
                .where(VENTAS.CANCELADA.eq(true))
                .and(VENTAS.COMPROBANTE_EMITIDO.eq(true))
                .and(VENTAS.NOTA_CREDITO_GENERADA.eq(false))
                .fetchOne(0, Int::class.java) ?: 0
        }
    }

    /**
     * Verifica si un usuario tiene alguna venta asociada
     * Método eficiente que solo cuenta sin cargar datos
     */
    override fun hasUserAnySales(username: String): Boolean {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            val count = dsl.selectCount()
                .from(VENTAS)
                .where(VENTAS.USUARIO_USERNAME.eq(username))
                .fetchOne(0, Int::class.java) ?: 0
            count > 0
        }
    }

    /**
     * Carga la información del comprobante asociado desde la base de datos
     */
    private fun loadComprobanteAsociado(comprobanteRelacionadoId: Int?): ComprobanteAsociado? {
        if (comprobanteRelacionadoId == null) return null

        // Buscar el comprobante por su ID numérico
        val comprobante = findComprobanteById(Id(comprobanteRelacionadoId))

        return comprobante?.let {
            ComprobanteAsociado.fromComprobante(it)
        }
    }
}