package com.gnico.majo.adapter.persistence

import com.gnico.majo.infrastructure.config.Database
import com.gnico.majo.application.domain.model.Categoria
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.port.out.CategoriaRepositoryPort
import com.gnico.majo.jooq.generated.tables.Categorias.Companion.CATEGORIAS
import com.gnico.majo.jooq.generated.tables.records.CategoriasRecord
import org.jooq.impl.DSL
import java.time.LocalDateTime

/**
 * Implementación JOOQ del repositorio de categorías
 */
class JooqCategoriaRepository : CategoriaRepositoryPort {

    override fun findAll(): List<Categoria> {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectFrom(CATEGORIAS)
                .where(CATEGORIAS.ACTIVO.eq(true))
                .orderBy(CATEGORIAS.ORDEN.asc(), CATEGORIAS.NOMBRE.asc())
                .fetch()
                .map { mapToCategoria(it) }
        }
    }

    override fun findById(id: Id): Categoria? {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectFrom(CATEGORIAS)
                .where(CATEGORIAS.ID.eq(id.value))
                .fetchOne()
                ?.let { mapToCategoria(it) }
        }
    }

    override fun findByNombre(nombre: String): Categoria? {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectFrom(CATEGORIAS)
                .where(CATEGORIAS.NOMBRE.eq(nombre))
                .and(CATEGORIAS.ACTIVO.eq(true))
                .fetchOne()
                ?.let { mapToCategoria(it) }
        }
    }

    override fun save(categoria: Categoria): Id {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            val record = dsl.newRecord(CATEGORIAS).apply {
                nombre = categoria.nombre
                descripcion = categoria.descripcion
                color = categoria.color
                orden = categoria.orden
                activo = categoria.activo
                creadoEn = categoria.creadoEn ?: LocalDateTime.now()
            }
            record.store()

            Id(record.id!!)
        }
    }

    override fun update(categoria: Categoria): Boolean {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            val count = dsl.update(CATEGORIAS)
                .set(CATEGORIAS.NOMBRE, categoria.nombre)
                .set(CATEGORIAS.DESCRIPCION, categoria.descripcion)
                .set(CATEGORIAS.COLOR, categoria.color)
                .set(CATEGORIAS.ORDEN, categoria.orden)
                .set(CATEGORIAS.ACTIVO, categoria.activo)
                .where(CATEGORIAS.ID.eq(categoria.id?.value))
                .execute()

            count > 0
        }
    }

    override fun delete(id: Id): Boolean {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            // Verificar si es la categoría especial "Sin Categoría"
            val categoria = dsl.selectFrom(CATEGORIAS)
                .where(CATEGORIAS.ID.eq(id.value))
                .fetchOne()

            if (categoria?.nombre == Categoria.SIN_CATEGORIA_NOMBRE) {
                throw IllegalArgumentException("No se puede eliminar la categoría especial '${Categoria.SIN_CATEGORIA_NOMBRE}'")
            }

            // Eliminación física (hard delete) de la categoría
            val count = dsl.deleteFrom(CATEGORIAS)
                .where(CATEGORIAS.ID.eq(id.value))
                .execute()

            count > 0
        }
    }

    override fun updateOrdenMultiple(ordenMap: Map<Int, Int>): Int {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            var totalUpdated = 0

            ordenMap.forEach { (categoriaId, orden) ->
                val count = dsl.update(CATEGORIAS)
                    .set(CATEGORIAS.ORDEN, orden)
                    .where(CATEGORIAS.ID.eq(categoriaId))
                    .and(CATEGORIAS.ACTIVO.eq(true))
                    .execute()
                totalUpdated += count
            }

            totalUpdated
        }
    }

    override fun findSinCategoriaEspecial(): Categoria? {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectFrom(CATEGORIAS)
                .where(CATEGORIAS.NOMBRE.eq(Categoria.SIN_CATEGORIA_NOMBRE))
                .and(CATEGORIAS.ACTIVO.eq(true))
                .fetchOne()
                ?.let { mapToCategoria(it) }
        }
    }

    private fun mapToCategoria(record: CategoriasRecord): Categoria {
        return Categoria(
            id = record.id?.let { Id(it) },
            nombre = record.nombre!!,
            descripcion = record.descripcion,
            color = record.color,
            orden = record.orden,
            activo = record.activo!!,
            creadoEn = record.creadoEn
        )
    }
}
