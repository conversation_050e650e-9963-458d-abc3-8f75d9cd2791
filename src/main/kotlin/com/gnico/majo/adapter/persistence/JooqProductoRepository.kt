package com.gnico.majo.adapter.persistence

import com.gnico.majo.infrastructure.config.Database
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Producto
import com.gnico.majo.application.domain.model.TipoIva
import com.gnico.majo.application.port.out.ProductoRepositoryPort
import com.gnico.majo.jooq.generated.tables.Productos.Companion.PRODUCTOS
import com.gnico.majo.jooq.generated.tables.records.ProductosRecord
import org.jooq.impl.DSL
import java.time.LocalDateTime

class JooqProductoRepository : ProductoRepositoryPort {
    override fun findAll(): List<Producto> {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectFrom(PRODUCTOS)
                .where(PRODUCTOS.ACTIVO.eq(true))
                .fetch()
                .map { mapToProducto(it) }
        }
    }

    override fun findByCodigo(codigo: Int): Producto? {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.selectFrom(PRODUCTOS)
                .where(PRODUCTOS.CODIGO.eq(codigo))
                .fetchOne()
                ?.let { mapToProducto(it) }
        }
    }

    override fun save(producto: Producto): Int {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            val record = dsl.newRecord(PRODUCTOS).apply {
                codigo = producto.codigo
                nombre = producto.nombre
                descripcion = producto.descripcion
                unidadMedidaId = producto.unidadMedida.value
                tipoIvaId = producto.tipoIva.id
                categoriaId = producto.categoria?.value
                precioUnitario = producto.precioUnitario
                stockActual = producto.stockActual
                activo = producto.activo
                creadoEn = LocalDateTime.now()
                actualizadoEn = LocalDateTime.now()
            }
            record.store()

            producto.codigo
        }
    }

    override fun update(producto: Producto): Boolean {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            val count = dsl.update(PRODUCTOS)
                .set(PRODUCTOS.NOMBRE, producto.nombre)
                .set(PRODUCTOS.DESCRIPCION, producto.descripcion)
                .set(PRODUCTOS.UNIDAD_MEDIDA_ID, producto.unidadMedida.value)
                .set(PRODUCTOS.TIPO_IVA_ID, producto.tipoIva.id)
                .set(PRODUCTOS.CATEGORIA_ID, producto.categoria?.value)
                .set(PRODUCTOS.PRECIO_UNITARIO, producto.precioUnitario)
                .set(PRODUCTOS.STOCK_ACTUAL, producto.stockActual)
                .set(PRODUCTOS.ACTIVO, producto.activo)
                .set(PRODUCTOS.ACTUALIZADO_EN, LocalDateTime.now())
                .where(PRODUCTOS.CODIGO.eq(producto.codigo))
                .execute()

            count > 0
        }
    }

    override fun delete(codigo: Int): Boolean {
        // Hard delete: eliminar físicamente el producto
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            val count = dsl.deleteFrom(PRODUCTOS)
                .where(PRODUCTOS.CODIGO.eq(codigo))
                .execute()

            count > 0
        }
    }

    override fun deleteAll(): Int {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.deleteFrom(PRODUCTOS).execute()
        }
    }

    override fun saveAll(productos: List<Producto>): Int {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            var count = 0

            productos.forEach { producto ->
                val record = dsl.newRecord(PRODUCTOS).apply {
                    codigo = producto.codigo
                    nombre = producto.nombre
                    descripcion = producto.descripcion
                    unidadMedidaId = producto.unidadMedida.value
                    tipoIvaId = producto.tipoIva.id
                    categoriaId = producto.categoria?.value
                    precioUnitario = producto.precioUnitario
                    stockActual = producto.stockActual
                    activo = producto.activo
                    creadoEn = LocalDateTime.now()
                    actualizadoEn = LocalDateTime.now()
                }
                record.store()
                count++
            }

            count
        }
    }

    override fun deleteMultiple(codigos: List<Int>): Int {
        if (codigos.isEmpty()) return 0

        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)
            dsl.deleteFrom(PRODUCTOS)
                .where(PRODUCTOS.CODIGO.`in`(codigos))
                .execute()
        }
    }

    override fun updateMultiple(
        codigos: List<Int>,
        categoriaId: Id?,
        precioUnitario: java.math.BigDecimal?,
        unidadMedidaId: Id?,
        stockActual: Int?
    ): Int {
        if (codigos.isEmpty()) return 0

        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            // Construir el query de actualización
            var updateQuery = dsl.update(PRODUCTOS)
                .set(PRODUCTOS.ACTUALIZADO_EN, LocalDateTime.now())

            // Manejar categoriaId con valores especiales
            categoriaId?.let {
                if (it.value == -1) {
                    // Valor especial para limpiar la categoría
                    updateQuery = updateQuery.set(PRODUCTOS.CATEGORIA_ID, null as Int?)
                } else {
                    updateQuery = updateQuery.set(PRODUCTOS.CATEGORIA_ID, it.value)
                }
            }

            precioUnitario?.let {
                updateQuery = updateQuery.set(PRODUCTOS.PRECIO_UNITARIO, it)
            }

            unidadMedidaId?.let {
                updateQuery = updateQuery.set(PRODUCTOS.UNIDAD_MEDIDA_ID, it.value)
            }

            // Manejar stockActual con valores especiales
            stockActual?.let {
                if (it == -1) {
                    // Valor especial para limpiar el stock
                    updateQuery = updateQuery.set(PRODUCTOS.STOCK_ACTUAL, null as Int?)
                } else {
                    updateQuery = updateQuery.set(PRODUCTOS.STOCK_ACTUAL, it)
                }
            }

            updateQuery.where(PRODUCTOS.CODIGO.`in`(codigos))
                .execute()
        }
    }

    private fun mapToProducto(record: ProductosRecord): Producto {
        return Producto.create(
            codigo = record.codigo!!,
            nombre = record.nombre!!,
            descripcion = record.descripcion,
            unidadMedida = Id(record.unidadMedidaId!!),
            tipoIva = TipoIva.fromIdOrThrow(record.tipoIvaId!!),
            categoria = record.categoriaId?.let { Id(it) },
            precioUnitario = record.precioUnitario,
            stockActual = record.stockActual,
            activo = record.activo!!,
            creadoEn = record.creadoEn,
            actualizadoEn = record.actualizadoEn
        )
    }
}
