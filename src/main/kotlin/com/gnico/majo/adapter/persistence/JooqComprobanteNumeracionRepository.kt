package com.gnico.majo.adapter.persistence

import com.gnico.majo.application.domain.model.ComprobanteNumeracion
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.port.out.ComprobanteNumeracionRepositoryPort
import com.gnico.majo.infrastructure.config.Database
import com.gnico.majo.jooq.generated.tables.ComprobanteNumeracion.Companion.COMPROBANTE_NUMERACION
import org.jooq.impl.DSL
import java.time.LocalDateTime

/**
 * Implementación JOOQ del repositorio de numeración de comprobantes
 */
class JooqComprobanteNumeracionRepository : ComprobanteNumeracionRepositoryPort {

    override fun obtenerNumeracion(puntoVenta: Int, tipoComprobante: String): ComprobanteNumeracion {
        return Database.dsl.transactionResult { config ->
            val ctx = DSL.using(config)
            val record = ctx.selectFrom(COMPROBANTE_NUMERACION)
                .where(COMPROBANTE_NUMERACION.PUNTO_VENTA.eq(puntoVenta))
                .and(COMPROBANTE_NUMERACION.TIPO_COMPROBANTE.eq(tipoComprobante))
                .fetchOne()

            if (record != null) {
                ComprobanteNumeracion.fromPersistence(
                    id = Id(record.id!!),
                    puntoVenta = record.puntoVenta!!,
                    tipoComprobante = record.tipoComprobante!!,
                    ultimoNumero = record.ultimoNumero!!,
                    creadoEn = record.creadoEn!!,
                    actualizadoEn = record.actualizadoEn!!
                )
            } else {
                // No existe, crear nueva
                val nuevaNumeracion = ComprobanteNumeracion.nueva(puntoVenta, tipoComprobante, 0)
                val id = guardarNumeracion(nuevaNumeracion)
                nuevaNumeracion.copy(id = id)
            }
        }
    }

    override fun obtenerYReservarSiguienteNumero(puntoVenta: Int, tipoComprobante: String): Int {
        return Database.dsl.transactionResult { config ->
            val ctx = DSL.using(config)
            // Obtener la numeración actual con lock para evitar concurrencia
            val record = ctx.selectFrom(COMPROBANTE_NUMERACION)
                .where(COMPROBANTE_NUMERACION.PUNTO_VENTA.eq(puntoVenta))
                .and(COMPROBANTE_NUMERACION.TIPO_COMPROBANTE.eq(tipoComprobante))
                .forUpdate() // Lock pesimista
                .fetchOne()

            if (record != null) {
                // Existe, incrementar y actualizar
                val ultimoNumero = record.ultimoNumero!!
                val siguienteNumero = ultimoNumero + 1

                ctx.update(COMPROBANTE_NUMERACION)
                    .set(COMPROBANTE_NUMERACION.ULTIMO_NUMERO, siguienteNumero)
                    .set(COMPROBANTE_NUMERACION.ACTUALIZADO_EN, LocalDateTime.now())
                    .where(COMPROBANTE_NUMERACION.ID.eq(record.id))
                    .execute()

                siguienteNumero
            } else {
                // No existe, crear nueva con número 1
                val nuevaNumeracion = ComprobanteNumeracion.nueva(puntoVenta, tipoComprobante, 1)
                guardarNumeracion(nuevaNumeracion)
                1
            }
        }
    }

    override fun actualizarUltimoNumero(puntoVenta: Int, tipoComprobante: String, nuevoNumero: Int): Boolean {
        return Database.dsl.transactionResult { config ->
            val ctx = DSL.using(config)
            val filasActualizadas = ctx.update(COMPROBANTE_NUMERACION)
                .set(COMPROBANTE_NUMERACION.ULTIMO_NUMERO, nuevoNumero)
                .set(COMPROBANTE_NUMERACION.ACTUALIZADO_EN, LocalDateTime.now())
                .where(COMPROBANTE_NUMERACION.PUNTO_VENTA.eq(puntoVenta))
                .and(COMPROBANTE_NUMERACION.TIPO_COMPROBANTE.eq(tipoComprobante))
                .and(COMPROBANTE_NUMERACION.ULTIMO_NUMERO.lt(nuevoNumero)) // Solo si es mayor
                .execute()

            filasActualizadas > 0
        }
    }

    override fun guardarNumeracion(numeracion: ComprobanteNumeracion): Id {
        return Database.dsl.transactionResult { config ->
            val ctx = DSL.using(config)
            if (numeracion.id != null) {
                // Actualizar existente
                ctx.update(COMPROBANTE_NUMERACION)
                    .set(COMPROBANTE_NUMERACION.PUNTO_VENTA, numeracion.puntoVenta)
                    .set(COMPROBANTE_NUMERACION.TIPO_COMPROBANTE, numeracion.tipoComprobante)
                    .set(COMPROBANTE_NUMERACION.ULTIMO_NUMERO, numeracion.ultimoNumero)
                    .set(COMPROBANTE_NUMERACION.ACTUALIZADO_EN, LocalDateTime.now())
                    .where(COMPROBANTE_NUMERACION.ID.eq(numeracion.id.value))
                    .execute()

                numeracion.id
            } else {
                // Insertar nuevo
                val record = ctx.newRecord(COMPROBANTE_NUMERACION).apply {
                    puntoVenta = numeracion.puntoVenta
                    tipoComprobante = numeracion.tipoComprobante
                    ultimoNumero = numeracion.ultimoNumero
                    creadoEn = numeracion.creadoEn
                    actualizadoEn = numeracion.actualizadoEn
                }

                record.store()
                Id(record.id!!)
            }
        }
    }

    override fun sincronizarConAfip(puntoVenta: Int, tipoComprobante: String, numeroAfip: Int): ComprobanteNumeracion {
        return Database.dsl.transactionResult { config ->
            val ctx = DSL.using(config)
            // Obtener numeración actual
            val numeracionActual = obtenerNumeracion(puntoVenta, tipoComprobante)

            // Solo actualizar si el número de AFIP es mayor
            if (numeroAfip > numeracionActual.ultimoNumero) {
                val numeracionActualizada = numeracionActual.conNuevoNumero(numeroAfip)
                guardarNumeracion(numeracionActualizada)
                numeracionActualizada
            } else {
                numeracionActual
            }
        }
    }
}
