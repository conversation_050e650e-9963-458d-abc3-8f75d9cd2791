package com.gnico.majo.adapter.persistence

import com.gnico.majo.infrastructure.config.Database
import com.gnico.majo.application.port.out.SalesReportPort
import com.gnico.majo.application.domain.model.*
import org.jooq.impl.DSL
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDateTime

class JooqSalesReportAdapter : SalesReportPort {

    override fun getSalesReport(filters: ReportFilters): SalesReport {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            val resumenGeneral = getSalesGeneralSummary(dsl, filters)
            val desglosePorMedioPago = getPaymentMethodBreakdown(filters)
            val desglosePorVendedor = getSellerBreakdown(filters)
            val productosTopVentas = getProductBreakdown(filters, 10)
            val estadisticasComprobantes = getComprobanteStatistics(filters)
            val alertas = getReportAlerts(filters)
            val histograma = getSalesHistogramInternal(dsl, filters)

            SalesReport(
                periodo = filters.periodo,
                filtros = filters,
                resumenGeneral = resumenGeneral,
                desglosePorMedioPago = desglosePorMedioPago,
                desglosePorVendedor = desglosePorVendedor,
                productosTopVentas = productosTopVentas,
                estadisticasComprobantes = estadisticasComprobantes,
                alertas = alertas,
                histograma = histograma
            )
        }
    }





    override fun getPaymentMethodBreakdown(filters: ReportFilters): List<PaymentMethodBreakdown> {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            val sql = """
                SELECT
                    v.medio_pago,
                    COUNT(*) as cantidad_ventas,
                    COALESCE(SUM(v.monto_total), 0) as monto_total
                FROM ventas v
                JOIN usuarios u ON v.usuario_username = u.username
                WHERE v.fecha_venta BETWEEN ? AND ?
                ${buildWhereClause(filters)}
                GROUP BY v.medio_pago
                ORDER BY SUM(v.monto_total) DESC
            """.trimIndent()

            val params = mutableListOf<Any>(filters.periodo.desde, filters.periodo.hasta)
            addFilterParams(filters, params)

            val results = dsl.fetch(sql, *params.toTypedArray())

            // Calcular total para porcentajes
            val montoTotalGeneral = results.sumOf {
                it.get("monto_total", BigDecimal::class.java) ?: BigDecimal.ZERO
            }

            results.map { record ->
                val medioPago = record.get("medio_pago", String::class.java) ?: ""
                val cantidadVentas = record.get("cantidad_ventas", Int::class.java) ?: 0
                val montoTotal = record.get("monto_total", BigDecimal::class.java) ?: BigDecimal.ZERO

                val medioEnum = MedioPago.fromString(medioPago)
                val porcentaje = if (montoTotalGeneral > BigDecimal.ZERO) {
                    montoTotal.divide(montoTotalGeneral, 4, RoundingMode.HALF_UP)
                        .multiply(BigDecimal(100))
                } else BigDecimal.ZERO

                val ticketPromedio = if (cantidadVentas > 0) {
                    montoTotal.divide(BigDecimal(cantidadVentas), 2, RoundingMode.HALF_UP)
                } else BigDecimal.ZERO

                PaymentMethodBreakdown(
                    medioPago = medioPago,
                    descripcion = medioEnum?.descripcion ?: medioPago,
                    cantidadVentas = cantidadVentas,
                    montoTotal = montoTotal,
                    porcentajeDelTotal = porcentaje,
                    ticketPromedio = ticketPromedio,
                    esElectronico = medioEnum != MedioPago.EFECTIVO
                )
            }
        }
    }

    override fun getSellerBreakdown(filters: ReportFilters): List<SellerBreakdown> {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            val sql = """
                SELECT
                    u.username,
                    u.nombre_display,
                    COUNT(*) as cantidad_ventas,
                    COALESCE(SUM(v.monto_total), 0) as monto_total,
                    COALESCE(SUM(CASE WHEN v.comprobante_emitido = true THEN 1 ELSE 0 END), 0) as ventas_con_comprobante,
                    COALESCE(SUM(CASE WHEN v.comprobante_emitido = false THEN 1 ELSE 0 END), 0) as ventas_sin_comprobante
                FROM ventas v
                JOIN usuarios u ON v.usuario_username = u.username
                WHERE v.fecha_venta BETWEEN ? AND ?
                ${buildWhereClause(filters)}
                GROUP BY u.username, u.nombre_display
                ORDER BY SUM(v.monto_total) DESC
            """.trimIndent()

            val params = mutableListOf<Any>(filters.periodo.desde, filters.periodo.hasta)
            addFilterParams(filters, params)

            val results = dsl.fetch(sql, *params.toTypedArray())

            // Calcular total para porcentajes
            val montoTotalGeneral = results.sumOf {
                it.get("monto_total", BigDecimal::class.java) ?: BigDecimal.ZERO
            }

            results.map { record ->
                val username = record.get("username", String::class.java) ?: ""
                val nombreDisplay = record.get("nombre_display", String::class.java) ?: ""
                val cantidadVentas = record.get("cantidad_ventas", Int::class.java) ?: 0
                val montoTotal = record.get("monto_total", BigDecimal::class.java) ?: BigDecimal.ZERO
                val ventasConComprobante = record.get("ventas_con_comprobante", Long::class.java)?.toInt() ?: 0
                val ventasSinComprobante = record.get("ventas_sin_comprobante", Long::class.java)?.toInt() ?: 0

                val porcentaje = if (montoTotalGeneral > BigDecimal.ZERO) {
                    montoTotal.divide(montoTotalGeneral, 4, RoundingMode.HALF_UP)
                        .multiply(BigDecimal(100))
                } else BigDecimal.ZERO

                val ticketPromedio = if (cantidadVentas > 0) {
                    montoTotal.divide(BigDecimal(cantidadVentas), 2, RoundingMode.HALF_UP)
                } else BigDecimal.ZERO

                SellerBreakdown(
                    username = username,
                    nombreDisplay = nombreDisplay,
                    cantidadVentas = cantidadVentas,
                    montoTotal = montoTotal,
                    ticketPromedio = ticketPromedio,
                    porcentajeDelTotal = porcentaje,
                    ventasConComprobante = ventasConComprobante,
                    ventasSinComprobante = ventasSinComprobante
                )
            }
        }
    }

    override fun getProductBreakdown(filters: ReportFilters, limit: Int): List<ProductBreakdown> {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            val sql = """
                SELECT
                    dv.producto_nombre,
                    COALESCE(um.nombre, 'un') as unidad_medida,
                    COALESCE(SUM(dv.cantidad), 0) as cantidad_vendida,
                    COALESCE(SUM(dv.subtotal), 0) as monto_total,
                    COUNT(*) as cantidad_transacciones
                FROM ventas v
                JOIN usuarios u ON v.usuario_username = u.username
                JOIN detalles_venta dv ON v.id = dv.venta_id
                LEFT JOIN productos p ON dv.producto_nombre = p.nombre
                LEFT JOIN unidades_medida um ON p.unidad_medida_id = um.id
                WHERE v.fecha_venta BETWEEN ? AND ?
                ${buildWhereClause(filters)}
                GROUP BY dv.producto_nombre, um.nombre
                ORDER BY SUM(dv.cantidad) DESC
                LIMIT ?
            """.trimIndent()

            val params = mutableListOf<Any>(filters.periodo.desde, filters.periodo.hasta)
            addFilterParams(filters, params)
            params.add(limit)

            val results = dsl.fetch(sql, *params.toTypedArray())

            results.map { record ->
                ProductBreakdown(
                    productoNombre = record.get("producto_nombre", String::class.java) ?: "",
                    cantidadVendida = record.get("cantidad_vendida", BigDecimal::class.java) ?: BigDecimal.ZERO,
                    unidadMedida = record.get("unidad_medida", String::class.java) ?: "",
                    montoTotal = record.get("monto_total", BigDecimal::class.java) ?: BigDecimal.ZERO,
                    cantidadTransacciones = record.get("cantidad_transacciones", Int::class.java) ?: 0
                )
            }
        }
    }

    override fun getComprobanteStatistics(filters: ReportFilters): ComprobanteStatistics {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            // Estadísticas generales de comprobantes
            val sqlGeneral = """
                SELECT
                    COUNT(*) as total_comprobantes,
                    COALESCE(SUM(c.imp_total), 0) as monto_total_facturado,
                    COALESCE(SUM(c.imp_iva), 0) as monto_total_iva
                FROM comprobantes c
                JOIN ventas v ON c.venta_id = v.id
                JOIN usuarios u ON v.usuario_username = u.username
                WHERE v.fecha_venta BETWEEN ? AND ?
                AND c.estado IN ('AUTORIZADO', 'AUTORIZADO_OFFLINE')
                ${buildWhereClause(filters)}
            """.trimIndent()

            val params = mutableListOf<Any>(filters.periodo.desde, filters.periodo.hasta)
            addFilterParams(filters, params)

            val resultGeneral = dsl.fetchOne(sqlGeneral, *params.toTypedArray())

            val totalComprobantes = resultGeneral?.get("total_comprobantes", Int::class.java) ?: 0
            val montoTotalFacturado = resultGeneral?.get("monto_total_facturado", BigDecimal::class.java) ?: BigDecimal.ZERO
            val montoTotalIva = resultGeneral?.get("monto_total_iva", BigDecimal::class.java) ?: BigDecimal.ZERO

            // Desglose por tipo de comprobante
            val desglosePorTipo = getComprobanteTypeBreakdown(dsl, filters)

            // Notas de crédito
            val notasCredito = getCreditNotesSummary(dsl, filters)

            ComprobanteStatistics(
                totalComprobantes = totalComprobantes,
                montoTotalFacturado = montoTotalFacturado,
                montoTotalIva = montoTotalIva,
                desglosePorTipo = desglosePorTipo,
                notasCredito = notasCredito
            )
        }
    }

    override fun getReportAlerts(filters: ReportFilters): ReportAlerts {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            // Ventas sin comprobante
            val ventasSinComprobante = getVentasSinComprobanteCount(dsl, filters)
            val montoSinComprobante = getVentasSinComprobanteMonto(dsl, filters)

            // Ventas electrónicas sin comprobante
            val filtrosElectronicos = filters.copy(soloMediosPagoElectronicos = true, soloConComprobante = false)
            val ventasElectronicasSinComprobante = getVentasSinComprobanteCount(dsl, filtrosElectronicos)
            val montoElectronicosSinComprobante = getVentasSinComprobanteMonto(dsl, filtrosElectronicos)

            // Ventas canceladas pendientes de nota de crédito
            val ventasCanceladasPendientesNC = getVentasCanceladasPendientesNC(dsl, filters)

            ReportAlerts(
                ventasSinComprobante = ventasSinComprobante,
                montoSinComprobante = montoSinComprobante,
                ventasElectronicasSinComprobante = ventasElectronicasSinComprobante,
                montoElectronicosSinComprobante = montoElectronicosSinComprobante,
                ventasCanceladasPendientesNC = ventasCanceladasPendientesNC
            )
        }
    }

    override fun getComparativeSummary(
        currentPeriod: ReportPeriod,
        previousPeriod: ReportPeriod,
        filters: ReportFilters
    ): ComparativeSummary {
        return Database.dsl.transactionResult { config ->
            val dsl = DSL.using(config)

            // Métricas del período actual
            val currentFilters = filters.copy(periodo = currentPeriod)
            val currentSummary = getSalesGeneralSummary(dsl, currentFilters)

            // Métricas del período anterior
            val previousFilters = filters.copy(periodo = previousPeriod)
            val previousSummary = getSalesGeneralSummary(dsl, previousFilters)

            // Calcular variaciones porcentuales
            val variacionVentas = if (previousSummary.totalVentas > 0) {
                BigDecimal(currentSummary.totalVentas - previousSummary.totalVentas)
                    .divide(BigDecimal(previousSummary.totalVentas), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal(100))
            } else BigDecimal.ZERO

            val variacionMonto = if (previousSummary.montoTotal > BigDecimal.ZERO) {
                currentSummary.montoTotal.subtract(previousSummary.montoTotal)
                    .divide(previousSummary.montoTotal, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal(100))
            } else BigDecimal.ZERO

            ComparativeSummary(
                ventasAnterior = previousSummary.totalVentas,
                montoAnterior = previousSummary.montoTotal,
                variacionVentas = variacionVentas,
                variacionMonto = variacionMonto
            )
        }
    }

    // ========== MÉTODOS AUXILIARES ==========

    /**
     * Construye la cláusula WHERE adicional para los filtros
     */
    private fun buildWhereClause(filters: ReportFilters): String {
        val conditions = mutableListOf<String>()

        // Filtro por usuarios
        filters.usuarios?.let { usuarios ->
            if (usuarios.isNotEmpty()) {
                val placeholders = usuarios.joinToString(",") { "?" }
                conditions.add("AND v.usuario_username IN ($placeholders)")
            }
        }

        // Filtro por medios de pago
        if (filters.soloMediosPagoElectronicos) {
            val mediosElectronicos = filters.getMediosPagoElectronicos()
            val placeholders = mediosElectronicos.joinToString(",") { "?" }
            conditions.add("AND v.medio_pago IN ($placeholders)")
        } else {
            filters.mediosPago?.let { medios ->
                if (medios.isNotEmpty()) {
                    val placeholders = medios.joinToString(",") { "?" }
                    conditions.add("AND v.medio_pago IN ($placeholders)")
                }
            }
        }

        // Filtro por comprobante emitido
        filters.soloConComprobante?.let { soloConComprobante ->
            if (soloConComprobante) {
                // Solo ventas CON comprobante
                conditions.add("AND v.comprobante_emitido = true")
            }
            // Si es false, no agregamos filtro (mostrar todas las ventas)
        }

        // Siempre excluir ventas canceladas en totales principales
        conditions.add("AND v.cancelada = false")

        return conditions.joinToString(" ")
    }

    /**
     * Construye la cláusula WHERE para consultas que incluyen ventas canceladas (para alertas)
     */
    private fun buildWhereClauseIncludingCancelled(filters: ReportFilters): String {
        val conditions = mutableListOf<String>()

        // Filtro por usuarios
        filters.usuarios?.let { usuarios ->
            if (usuarios.isNotEmpty()) {
                val placeholders = usuarios.joinToString(",") { "?" }
                conditions.add("AND v.usuario_username IN ($placeholders)")
            }
        }

        // Filtro por medios de pago
        if (filters.soloMediosPagoElectronicos) {
            val mediosElectronicos = filters.getMediosPagoElectronicos()
            val placeholders = mediosElectronicos.joinToString(",") { "?" }
            conditions.add("AND v.medio_pago IN ($placeholders)")
        } else {
            filters.mediosPago?.let { medios ->
                if (medios.isNotEmpty()) {
                    val placeholders = medios.joinToString(",") { "?" }
                    conditions.add("AND v.medio_pago IN ($placeholders)")
                }
            }
        }

        // Filtro por comprobante emitido
        filters.soloConComprobante?.let { soloConComprobante ->
            if (soloConComprobante) {
                // Solo ventas CON comprobante
                conditions.add("AND v.comprobante_emitido = true")
            }
            // Si es false, no agregamos filtro (mostrar todas las ventas)
        }

        // NO filtrar por canceladas - incluir todas las ventas

        return conditions.joinToString(" ")
    }

    /**
     * Agrega los parámetros de filtros a la lista de parámetros
     */
    private fun addFilterParams(filters: ReportFilters, params: MutableList<Any>) {
        // Parámetros para usuarios
        filters.usuarios?.let { usuarios ->
            if (usuarios.isNotEmpty()) {
                params.addAll(usuarios)
            }
        }

        // Parámetros para medios de pago
        if (filters.soloMediosPagoElectronicos) {
            val mediosElectronicos = filters.getMediosPagoElectronicos()
            params.addAll(mediosElectronicos)
        } else {
            filters.mediosPago?.let { medios ->
                if (medios.isNotEmpty()) {
                    params.addAll(medios)
                }
            }
        }

        // Parámetro para comprobante emitido (solo cuando es true)
        filters.soloConComprobante?.let { soloConComprobante ->
            if (soloConComprobante) {
                // No necesitamos agregar parámetros porque usamos valor literal true
            }
            // Si es false, no agregamos parámetros
        }
    }
    /**
     * Obtiene el resumen general de ventas usando SQL directo
     */
    private fun getSalesGeneralSummary(dsl: org.jooq.DSLContext, filters: ReportFilters): SalesGeneralSummary {
        // Consulta principal para totales (excluyendo canceladas)
        val sql = """
            SELECT
                COUNT(*) as total_ventas,
                COALESCE(SUM(v.monto_total), 0) as monto_total,
                COALESCE(SUM(CASE WHEN v.comprobante_emitido = true THEN 1 ELSE 0 END), 0) as ventas_con_comprobante,
                COALESCE(SUM(CASE WHEN v.comprobante_emitido = false THEN 1 ELSE 0 END), 0) as ventas_sin_comprobante,
                COALESCE(SUM(CASE WHEN v.comprobante_emitido = true THEN v.monto_total ELSE 0 END), 0) as monto_con_comprobante,
                COALESCE(SUM(CASE WHEN v.comprobante_emitido = false THEN v.monto_total ELSE 0 END), 0) as monto_sin_comprobante
            FROM ventas v
            JOIN usuarios u ON v.usuario_username = u.username
            WHERE v.fecha_venta BETWEEN ? AND ?
            ${buildWhereClause(filters)}
        """.trimIndent()

        val params = mutableListOf<Any>(filters.periodo.desde, filters.periodo.hasta)
        addFilterParams(filters, params)

        val result = dsl.fetchOne(sql, *params.toTypedArray())

        val totalVentas = result?.get("total_ventas", Int::class.java) ?: 0
        val montoTotal = result?.get("monto_total", BigDecimal::class.java) ?: BigDecimal.ZERO
        val ventasConComprobante = result?.get("ventas_con_comprobante", Long::class.java)?.toInt() ?: 0
        val ventasSinComprobante = result?.get("ventas_sin_comprobante", Long::class.java)?.toInt() ?: 0
        val montoConComprobante = result?.get("monto_con_comprobante", BigDecimal::class.java) ?: BigDecimal.ZERO
        val montoSinComprobante = result?.get("monto_sin_comprobante", BigDecimal::class.java) ?: BigDecimal.ZERO
        // Consulta separada para obtener información de ventas canceladas
        val sqlCanceladas = """
            SELECT
                COUNT(*) as ventas_canceladas,
                COALESCE(SUM(v.monto_total), 0) as monto_cancelado
            FROM ventas v
            JOIN usuarios u ON v.usuario_username = u.username
            WHERE v.fecha_venta BETWEEN ? AND ?
            AND v.cancelada = true
            ${buildWhereClauseIncludingCancelled(filters)}
        """.trimIndent()

        val paramsCanceladas = mutableListOf<Any>(filters.periodo.desde, filters.periodo.hasta)
        addFilterParams(filters, paramsCanceladas)

        val resultCanceladas = dsl.fetchOne(sqlCanceladas, *paramsCanceladas.toTypedArray())
        val ventasCanceladas = resultCanceladas?.get("ventas_canceladas", Int::class.java) ?: 0
        val montoCancelado = resultCanceladas?.get("monto_cancelado", BigDecimal::class.java) ?: BigDecimal.ZERO

        // Calcular ticket promedio
        val ticketPromedio = if (totalVentas > 0) {
            montoTotal.divide(BigDecimal(totalVentas), 2, RoundingMode.HALF_UP)
        } else BigDecimal.ZERO

        // Obtener estadísticas de medios electrónicos pendientes
        val filtrosElectronicos = filters.copy(soloMediosPagoElectronicos = true, soloConComprobante = false)
        val ventasElectronicasPendientes = getVentasSinComprobanteCount(dsl, filtrosElectronicos)
        val montoElectronicosPendientes = getVentasSinComprobanteMonto(dsl, filtrosElectronicos)

        return SalesGeneralSummary(
            totalVentas = totalVentas,
            montoTotal = montoTotal,
            ticketPromedio = ticketPromedio,
            ventasConComprobante = ventasConComprobante,
            ventasSinComprobante = ventasSinComprobante,
            montoConComprobante = montoConComprobante,
            montoSinComprobante = montoSinComprobante,
            ventasCanceladas = ventasCanceladas,
            montoCancelado = montoCancelado,
            ventasElectronicasPendientes = ventasElectronicasPendientes,
            montoElectronicosPendientes = montoElectronicosPendientes
        )
    }



    /**
     * Obtiene la cantidad de ventas sin comprobante
     */
    private fun getVentasSinComprobanteCount(dsl: org.jooq.DSLContext, filters: ReportFilters): Int {
        val sql = """
            SELECT COUNT(*) as cantidad
            FROM ventas v
            JOIN usuarios u ON v.usuario_username = u.username
            WHERE v.fecha_venta BETWEEN ? AND ?
            AND v.comprobante_emitido = false
            ${buildWhereClause(filters)}
        """.trimIndent()

        val params = mutableListOf<Any>(filters.periodo.desde, filters.periodo.hasta)
        addFilterParams(filters, params)

        val result = dsl.fetchOne(sql, *params.toTypedArray())
        return result?.get("cantidad", Int::class.java) ?: 0
    }

    /**
     * Obtiene el monto total de ventas sin comprobante
     */
    private fun getVentasSinComprobanteMonto(dsl: org.jooq.DSLContext, filters: ReportFilters): BigDecimal {
        val sql = """
            SELECT COALESCE(SUM(v.monto_total), 0) as monto_total
            FROM ventas v
            JOIN usuarios u ON v.usuario_username = u.username
            WHERE v.fecha_venta BETWEEN ? AND ?
            AND v.comprobante_emitido = false
            ${buildWhereClause(filters)}
        """.trimIndent()

        val params = mutableListOf<Any>(filters.periodo.desde, filters.periodo.hasta)
        addFilterParams(filters, params)

        val result = dsl.fetchOne(sql, *params.toTypedArray())
        return result?.get("monto_total", BigDecimal::class.java) ?: BigDecimal.ZERO
    }
    /**
     * Obtiene la cantidad de ventas canceladas pendientes de nota de crédito
     */
    private fun getVentasCanceladasPendientesNC(dsl: org.jooq.DSLContext, filters: ReportFilters): Int {
        val sql = """
            SELECT COUNT(*) as cantidad
            FROM ventas v
            JOIN usuarios u ON v.usuario_username = u.username
            WHERE v.fecha_venta BETWEEN ? AND ?
            AND v.cancelada = true
            AND v.comprobante_emitido = true
            AND v.nota_credito_generada = false
            ${buildWhereClauseIncludingCancelled(filters)}
        """.trimIndent()

        val params = mutableListOf<Any>(filters.periodo.desde, filters.periodo.hasta)
        addFilterParams(filters, params)

        val result = dsl.fetchOne(sql, *params.toTypedArray())
        return result?.get("cantidad", Int::class.java) ?: 0
    }

    /**
     * Obtiene el desglose por tipo de comprobante
     */
    private fun getComprobanteTypeBreakdown(dsl: org.jooq.DSLContext, filters: ReportFilters): List<ComprobanteTypeBreakdown> {
        val sql = """
            SELECT
                c.tipo_comprobante,
                COUNT(*) as cantidad,
                COALESCE(SUM(c.imp_total), 0) as monto_total,
                COALESCE(SUM(c.imp_iva), 0) as monto_iva
            FROM comprobantes c
            JOIN ventas v ON c.venta_id = v.id
            JOIN usuarios u ON v.usuario_username = u.username
            WHERE v.fecha_venta BETWEEN ? AND ?
            AND c.estado IN ('AUTORIZADO', 'AUTORIZADO_OFFLINE')
            ${buildWhereClause(filters)}
            GROUP BY c.tipo_comprobante
            ORDER BY SUM(c.imp_total) DESC
        """.trimIndent()

        val params = mutableListOf<Any>(filters.periodo.desde, filters.periodo.hasta)
        addFilterParams(filters, params)

        val results = dsl.fetch(sql, *params.toTypedArray())

        return results.map { record ->
            ComprobanteTypeBreakdown(
                tipoComprobante = record.get("tipo_comprobante", String::class.java) ?: "",
                cantidad = record.get("cantidad", Int::class.java) ?: 0,
                montoTotal = record.get("monto_total", BigDecimal::class.java) ?: BigDecimal.ZERO,
                montoIva = record.get("monto_iva", BigDecimal::class.java) ?: BigDecimal.ZERO
            )
        }
    }

    /**
     * Obtiene el resumen de notas de crédito
     */
    private fun getCreditNotesSummary(dsl: org.jooq.DSLContext, filters: ReportFilters): CreditNotesSummary {
        val sql = """
            SELECT
                COUNT(*) as cantidad_notas,
                COALESCE(SUM(c.imp_total), 0) as monto_total,
                COALESCE(SUM(c.imp_iva), 0) as monto_iva
            FROM comprobantes c
            JOIN ventas v ON c.venta_id = v.id
            JOIN usuarios u ON v.usuario_username = u.username
            WHERE v.fecha_venta BETWEEN ? AND ?
            AND c.tipo_comprobante = 'NOTA_CREDITO_B'
            AND c.estado IN ('AUTORIZADO', 'AUTORIZADO_OFFLINE')
            ${buildWhereClauseIncludingCancelled(filters)}
        """.trimIndent()

        val params = mutableListOf<Any>(filters.periodo.desde, filters.periodo.hasta)
        addFilterParams(filters, params)

        val result = dsl.fetchOne(sql, *params.toTypedArray())

        val cantidadNotas = result?.get("cantidad_notas", Int::class.java) ?: 0
        val montoTotal = result?.get("monto_total", BigDecimal::class.java) ?: BigDecimal.ZERO
        val montoIva = result?.get("monto_iva", BigDecimal::class.java) ?: BigDecimal.ZERO

        // Obtener ventas pendientes de nota de crédito
        val ventasPendientesNC = getVentasCanceladasPendientesNC(dsl, filters)

        return CreditNotesSummary(
            cantidadNotas = cantidadNotas,
            montoTotal = montoTotal,
            montoIva = montoIva,
            ventasPendientesNotaCredito = ventasPendientesNC
        )
    }



    /**
     * Método interno para generar histograma que puede ser reutilizado
     */
    private fun getSalesHistogramInternal(dsl: org.jooq.DSLContext, filters: ReportFilters): SalesHistogram {
        // Determinar el tipo de bucket basado en el rango de fechas
        val bucketType = determineBucketType(filters.periodo)

        // Obtener buckets con datos
        val buckets = getSalesHistogramBuckets(dsl, filters, bucketType)

        // Calcular resumen
        val resumen = calculateHistogramSummary(buckets)

        return SalesHistogram(
            periodo = filters.periodo,
            filtros = filters,
            bucketType = bucketType,
            buckets = buckets,
            resumen = resumen
        )
    }

    /**
     * Determina el tipo de bucket basado en el rango de fechas
     */
    private fun determineBucketType(periodo: ReportPeriod): HistogramBucketType {
        val duration = java.time.Duration.between(periodo.desde, periodo.hasta)

        return when {
            duration.toDays() <= 1 -> HistogramBucketType.HOUR
            duration.toDays() <= 31 -> HistogramBucketType.DAY
            else -> HistogramBucketType.MONTH
        }
    }

    /**
     * Obtiene los buckets del histograma con datos de ventas
     */
    private fun getSalesHistogramBuckets(
        dsl: org.jooq.DSLContext,
        filters: ReportFilters,
        bucketType: HistogramBucketType
    ): List<SalesHistogramBucket> {
        val dateFormat = when (bucketType) {
            HistogramBucketType.HOUR -> "YYYY-MM-DD HH24:00"
            HistogramBucketType.DAY -> "YYYY-MM-DD"
            HistogramBucketType.MONTH -> "YYYY-MM"
        }

        val dateTrunc = when (bucketType) {
            HistogramBucketType.HOUR -> "hour"
            HistogramBucketType.DAY -> "day"
            HistogramBucketType.MONTH -> "month"
        }

        val sql = """
            SELECT
                DATE_TRUNC('$dateTrunc', v.fecha_venta) as bucket_start,
                TO_CHAR(DATE_TRUNC('$dateTrunc', v.fecha_venta), '$dateFormat') as periodo_label,
                COUNT(*) as cantidad_ventas,
                COALESCE(SUM(v.monto_total), 0) as monto_total
            FROM ventas v
            JOIN usuarios u ON v.usuario_username = u.username
            WHERE v.fecha_venta BETWEEN ? AND ?
            ${buildWhereClause(filters)}
            GROUP BY DATE_TRUNC('$dateTrunc', v.fecha_venta)
            ORDER BY DATE_TRUNC('$dateTrunc', v.fecha_venta)
        """.trimIndent()

        val params = mutableListOf<Any>(filters.periodo.desde, filters.periodo.hasta)
        addFilterParams(filters, params)

        val results = dsl.fetch(sql, *params.toTypedArray())

        return results.map { record ->
            val bucketStart = record.get("bucket_start", LocalDateTime::class.java)!!
            val periodoLabel = record.get("periodo_label", String::class.java) ?: ""
            val cantidadVentas = record.get("cantidad_ventas", Int::class.java) ?: 0
            val montoTotal = record.get("monto_total", BigDecimal::class.java) ?: BigDecimal.ZERO

            val bucketEnd = when (bucketType) {
                HistogramBucketType.HOUR -> bucketStart.plusHours(1).minusSeconds(1)
                HistogramBucketType.DAY -> bucketStart.plusDays(1).minusSeconds(1)
                HistogramBucketType.MONTH -> bucketStart.plusMonths(1).minusSeconds(1)
            }

            SalesHistogramBucket(
                periodo = periodoLabel,
                fechaInicio = bucketStart,
                fechaFin = bucketEnd,
                cantidadVentas = cantidadVentas,
                montoTotal = montoTotal
            )
        }
    }

    /**
     * Calcula el resumen del histograma
     */
    private fun calculateHistogramSummary(buckets: List<SalesHistogramBucket>): SalesHistogramSummary {
        val totalVentas = buckets.sumOf { it.cantidadVentas }
        val montoTotal = buckets.sumOf { it.montoTotal }

        val promedioVentasPorBucket = if (buckets.isNotEmpty()) {
            totalVentas.toDouble() / buckets.size
        } else 0.0

        val promedioMontoPorBucket = if (buckets.isNotEmpty()) {
            montoTotal.divide(BigDecimal(buckets.size), 2, RoundingMode.HALF_UP)
        } else BigDecimal.ZERO

        val bucketConMasVentas = buckets.maxByOrNull { it.cantidadVentas }
        val bucketConMayorMonto = buckets.maxByOrNull { it.montoTotal }

        return SalesHistogramSummary(
            totalVentas = totalVentas,
            montoTotal = montoTotal,
            promedioVentasPorBucket = promedioVentasPorBucket,
            promedioMontoPorBucket = promedioMontoPorBucket,
            bucketConMasVentas = bucketConMasVentas,
            bucketConMayorMonto = bucketConMayorMonto
        )
    }
}