package com.gnico.majo.adapter.persistence

import com.gnico.majo.application.domain.model.WsaaCredentials
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.EstadoCredencial
import com.gnico.majo.application.port.out.WsaaCredentialsRepository
import com.gnico.majo.infrastructure.config.Database
import org.jooq.impl.DSL.*
import org.slf4j.LoggerFactory
import java.time.LocalDateTime

/**
 * Implementación JOOQ del repositorio de credenciales WSAA
 */
class JooqWsaaCredentialsRepository : WsaaCredentialsRepository {
    
    private val logger = LoggerFactory.getLogger(JooqWsaaCredentialsRepository::class.java)
    private val dsl = Database.dsl
    
    override fun findActiveCredentials(servicio: String): WsaaCredentials? {
        logger.debug("Buscando credenciales activas para servicio: $servicio")
        
        return dsl.transactionResult { config ->
            val ctx = config.dsl()
            
            ctx.select(
                field("id"),
                field("servicio"),
                field("token"),
                field("sign"),
                field("fecha_generacion"),
                field("fecha_expiracion"),
                field("estado"),
                field("creado_en")
            )
            .from(table("wsaa_credenciales"))
            .where(
                field("servicio").eq(servicio)
                .and(field("estado").eq(EstadoCredencial.ACTIVO.value))
                .and(field("fecha_expiracion").gt(LocalDateTime.now()))
            )
            .orderBy(field("fecha_generacion").desc())
            .limit(1)
            .fetchOne()?.let { record ->
                WsaaCredentials.fromDatabase(
                    id = record.get("id", Long::class.java).toInt(),
                    servicio = record.get("servicio", String::class.java),
                    token = record.get("token", String::class.java),
                    sign = record.get("sign", String::class.java),
                    fechaGeneracion = record.get("fecha_generacion", LocalDateTime::class.java),
                    fechaExpiracion = record.get("fecha_expiracion", LocalDateTime::class.java),
                    estado = record.get("estado", String::class.java),
                    creadoEn = record.get("creado_en", LocalDateTime::class.java)
                )
            }
        }
    }
    
    override fun save(credentials: WsaaCredentials): Id {
        logger.info("Guardando nuevas credenciales WSAA para servicio: ${credentials.servicio}")
        
        return dsl.transactionResult { config ->
            val ctx = config.dsl()
            
            // Primero marcar todas las credenciales existentes como expiradas
            ctx.update(table("wsaa_credenciales"))
                .set(field("estado"), EstadoCredencial.EXPIRADO.value)
                .where(
                    field("servicio").eq(credentials.servicio)
                    .and(field("estado").eq(EstadoCredencial.ACTIVO.value))
                )
                .execute()
            
            // Insertar las nuevas credenciales
            val id = ctx.insertInto(table("wsaa_credenciales"))
                .set(field("servicio"), credentials.servicio)
                .set(field("token"), credentials.token)
                .set(field("sign"), credentials.sign)
                .set(field("fecha_generacion"), credentials.fechaGeneracion)
                .set(field("fecha_expiracion"), credentials.fechaExpiracion)
                .set(field("estado"), credentials.estado.value)
                .set(field("creado_en"), credentials.creadoEn)
                .returningResult(field("id"))
                .fetchOne()
                ?.get("id", Long::class.java)
                ?: throw RuntimeException("Error al insertar credenciales WSAA")
            
            logger.info("Credenciales WSAA guardadas con ID: $id")
            Id(id.toInt())
        }
    }
    
    override fun update(credentials: WsaaCredentials): Boolean {
        logger.debug("Actualizando credenciales WSAA ID: ${credentials.id?.value}")
        
        return dsl.transactionResult { config ->
            val ctx = config.dsl()
            
            val updated = ctx.update(table("wsaa_credenciales"))
                .set(field("servicio"), credentials.servicio)
                .set(field("token"), credentials.token)
                .set(field("sign"), credentials.sign)
                .set(field("fecha_generacion"), credentials.fechaGeneracion)
                .set(field("fecha_expiracion"), credentials.fechaExpiracion)
                .set(field("estado"), credentials.estado.value)
                .where(field("id").eq(credentials.id?.value))
                .execute()
            
            updated > 0
        }
    }
    
    override fun markAllAsExpired(servicio: String): Int {
        logger.info("Marcando todas las credenciales como expiradas para servicio: $servicio")
        
        return dsl.transactionResult { config ->
            val ctx = config.dsl()
            
            ctx.update(table("wsaa_credenciales"))
                .set(field("estado"), EstadoCredencial.EXPIRADO.value)
                .where(
                    field("servicio").eq(servicio)
                    .and(field("estado").eq(EstadoCredencial.ACTIVO.value))
                )
                .execute()
        }
    }
    
    override fun deleteExpiredOlderThan(days: Int): Int {
        logger.info("Eliminando credenciales expiradas más antiguas que $days días")
        
        return dsl.transactionResult { config ->
            val ctx = config.dsl()
            
            val cutoffDate = LocalDateTime.now().minusDays(days.toLong())
            
            ctx.deleteFrom(table("wsaa_credenciales"))
                .where(
                    field("estado").eq(EstadoCredencial.EXPIRADO.value)
                    .and(field("fecha_expiracion").lt(cutoffDate))
                )
                .execute()
        }
    }
    
    override fun findNearExpiry(): List<WsaaCredentials> {
        logger.debug("Buscando credenciales próximas a vencer")
        
        return dsl.transactionResult { config ->
            val ctx = config.dsl()
            
            val oneHourFromNow = LocalDateTime.now().plusHours(1)
            
            ctx.select(
                field("id"),
                field("servicio"),
                field("token"),
                field("sign"),
                field("fecha_generacion"),
                field("fecha_expiracion"),
                field("estado"),
                field("creado_en")
            )
            .from(table("wsaa_credenciales"))
            .where(
                field("estado").eq(EstadoCredencial.ACTIVO.value)
                .and(field("fecha_expiracion").between(LocalDateTime.now(), oneHourFromNow))
            )
            .fetch()
            .map { record ->
                WsaaCredentials.fromDatabase(
                    id = record.get("id", Long::class.java).toInt(),
                    servicio = record.get("servicio", String::class.java),
                    token = record.get("token", String::class.java),
                    sign = record.get("sign", String::class.java),
                    fechaGeneracion = record.get("fecha_generacion", LocalDateTime::class.java),
                    fechaExpiracion = record.get("fecha_expiracion", LocalDateTime::class.java),
                    estado = record.get("estado", String::class.java),
                    creadoEn = record.get("creado_en", LocalDateTime::class.java)
                )
            }
        }
    }
    
    override fun findExpired(): List<WsaaCredentials> {
        logger.debug("Buscando credenciales expiradas")
        
        return dsl.transactionResult { config ->
            val ctx = config.dsl()
            
            ctx.select(
                field("id"),
                field("servicio"),
                field("token"),
                field("sign"),
                field("fecha_generacion"),
                field("fecha_expiracion"),
                field("estado"),
                field("creado_en")
            )
            .from(table("wsaa_credenciales"))
            .where(
                field("estado").eq(EstadoCredencial.ACTIVO.value)
                .and(field("fecha_expiracion").lt(LocalDateTime.now()))
            )
            .fetch()
            .map { record ->
                WsaaCredentials.fromDatabase(
                    id = record.get("id", Long::class.java).toInt(),
                    servicio = record.get("servicio", String::class.java),
                    token = record.get("token", String::class.java),
                    sign = record.get("sign", String::class.java),
                    fechaGeneracion = record.get("fecha_generacion", LocalDateTime::class.java),
                    fechaExpiracion = record.get("fecha_expiracion", LocalDateTime::class.java),
                    estado = record.get("estado", String::class.java),
                    creadoEn = record.get("creado_en", LocalDateTime::class.java)
                )
            }
        }
    }
    
    override fun hasValidCredentials(servicio: String): Boolean {
        logger.debug("Verificando si existen credenciales válidas para servicio: $servicio")
        
        return dsl.transactionResult { config ->
            val ctx = config.dsl()
            
            ctx.selectCount()
                .from(table("wsaa_credenciales"))
                .where(
                    field("servicio").eq(servicio)
                    .and(field("estado").eq(EstadoCredencial.ACTIVO.value))
                    .and(field("fecha_expiracion").gt(LocalDateTime.now()))
                )
                .fetchOne(0, Int::class.java)?.let { it > 0 } ?: false
        }
    }
}
