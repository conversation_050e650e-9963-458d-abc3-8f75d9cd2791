package com.gnico.majo.demo

import com.gnico.majo.application.domain.model.*
import com.gnico.majo.infrastructure.config.PrinterConfiguration
import com.gnico.majo.infrastructure.printer.StyledTicketFormatter
import com.github.anastaciocintra.escpos.EscPos
import java.io.ByteArrayOutputStream
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.LocalDate

/**
 * Demostración de la funcionalidad de descuentos en tickets
 * Sin usar impresora física - solo simulación
 */
object DiscountTicketDemo {
    
    @JvmStatic
    fun main(args: Array<String>) {
        println("=== DEMOSTRACIÓN DE TICKETS CON DESCUENTO ===")
        println()
        
        // Configuración de prueba
        val config = PrinterConfiguration(
            companyName = "Test Company",
            companyCUIT = "20*********",
            companyIIBB = "*********",
            companyAddress = "Test Address 123",
            companyStart = "01/01/2020",
            companyPhone = "************",
            companyEmail = "<EMAIL>",
            companyWebsite = "www.test.com",
            printerIp = "*************",
            printerPort = 9100
        )
        
        val formatter = StyledTicketFormatter(config)
        
        // Test 1: Venta con descuento
        println("1️⃣  === VENTA CON DESCUENTO 10% ===")
        val saleWithDiscount = createSaleWithDiscount()
        val comprobante = createTestComprobante()
        
        println("Datos de la venta:")
        println("- Subtotal sin descuento: ${saleWithDiscount.calculateSubtotalSinDescuento()}")
        println("- Monto descuento: ${saleWithDiscount.calculateMontoDescuento()}")
        println("- Total final: ${saleWithDiscount.montoTotal}")
        println("- Tiene descuento: ${saleWithDiscount.hasDescuento()}")
        println()
        
        // Verificar que los métodos funcionan correctamente
        val expectedSubtotal = BigDecimal("250.00")
        val expectedDiscount = BigDecimal("25.00")
        val expectedTotal = BigDecimal("225.00")
        
        println("✅ Verificaciones:")
        println("- Subtotal correcto: ${saleWithDiscount.calculateSubtotalSinDescuento() == expectedSubtotal}")
        println("- Descuento correcto: ${saleWithDiscount.calculateMontoDescuento() == expectedDiscount}")
        println("- Total correcto: ${saleWithDiscount.montoTotal == expectedTotal}")
        println("- Detecta descuento: ${saleWithDiscount.hasDescuento()}")
        println()
        
        // Simular ticket fiscal con descuento
        println("CONTENIDO DEL TICKET FISCAL CON DESCUENTO:")
        val ticketWithDiscount = simulateTicket { escpos ->
            formatter.formatFiscalTicket(escpos, comprobante, saleWithDiscount)
        }
        
        // Verificar que el ticket contiene las líneas de descuento
        println("🔍 Verificaciones del ticket:")
        println("- Contiene 'Subtotal': ${ticketWithDiscount.contains("Subtotal")}")
        println("- Contiene 'Descuento': ${ticketWithDiscount.contains("Descuento")}")
        println("- Contiene '10%': ${ticketWithDiscount.contains("10")}")
        println("- Contiene monto descuento: ${ticketWithDiscount.contains("25")}")
        println()
        
        println("2️⃣  === VENTA SIN DESCUENTO ===")
        val saleWithoutDiscount = createSaleWithoutDiscount()
        
        println("Datos de la venta:")
        println("- Subtotal sin descuento: ${saleWithoutDiscount.calculateSubtotalSinDescuento()}")
        println("- Monto descuento: ${saleWithoutDiscount.calculateMontoDescuento()}")
        println("- Total final: ${saleWithoutDiscount.montoTotal}")
        println("- Tiene descuento: ${saleWithoutDiscount.hasDescuento()}")
        println()
        
        // Simular ticket fiscal sin descuento
        println("CONTENIDO DEL TICKET FISCAL SIN DESCUENTO:")
        val ticketWithoutDiscount = simulateTicket { escpos ->
            formatter.formatFiscalTicket(escpos, comprobante, saleWithoutDiscount)
        }
        
        // Verificar que el ticket NO contiene líneas de descuento
        println("🔍 Verificaciones del ticket:")
        println("- NO contiene 'Subtotal': ${!ticketWithoutDiscount.contains("Subtotal")}")
        println("- NO contiene 'Descuento': ${!ticketWithoutDiscount.contains("Descuento")}")
        println()
        
        println("3️⃣  === TICKET NO FISCAL CON DESCUENTO ===")
        println("CONTENIDO DEL TICKET NO FISCAL:")
        val nonFiscalTicket = simulateTicket { escpos ->
            formatter.formatNonFiscalTicket(escpos, saleWithDiscount)
        }
        
        println("🔍 Verificaciones del ticket no fiscal:")
        println("- Contiene 'Subtotal': ${nonFiscalTicket.contains("Subtotal")}")
        println("- Contiene 'Descuento': ${nonFiscalTicket.contains("Descuento")}")
        println()
        
        println("✅ DEMOSTRACIÓN COMPLETADA EXITOSAMENTE!")
        println("💡 Los tickets muestran correctamente el desglose de descuentos")
        println("📋 Funcionalidades verificadas:")
        println("   - Cálculo de subtotal sin descuento")
        println("   - Cálculo de monto de descuento")
        println("   - Detección de ventas con descuento")
        println("   - Formato de ticket fiscal con descuento")
        println("   - Formato de ticket fiscal sin descuento")
        println("   - Formato de ticket no fiscal con descuento")
    }
    
    private fun simulateTicket(ticketGenerator: (EscPos) -> Unit): String {
        val outputStream = ByteArrayOutputStream()
        val escpos = EscPos(outputStream)
        
        return try {
            ticketGenerator(escpos)
            
            val rawOutput = outputStream.toByteArray()
            val readableOutput = convertEscPosToReadable(rawOutput)
            
            println(readableOutput)
            println("=" * 50)
            println()
            
            readableOutput
            
        } catch (e: Exception) {
            println("❌ Error al generar ticket: ${e.message}")
            e.printStackTrace()
            ""
        }
    }
    
    private fun convertEscPosToReadable(rawOutput: ByteArray): String {
        // Convertir bytes ESC/POS a texto legible
        val content = String(rawOutput, Charsets.ISO_8859_1)
        
        // Filtrar caracteres de control ESC/POS y mantener solo texto legible
        return content.replace(Regex("[\u0000-\u001F\u007F-\u009F]"), " ")
            .replace(Regex("\\s+"), " ")
            .trim()
    }
    
    private fun createSaleWithDiscount(): Sale {
        val usuario = Usuario("test_user", "Test User", "Test User", true)
        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto A",
                cantidad = BigDecimal("2"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21,
                porcentajeDescuento = BigDecimal("10") // 10% descuento
            ),
            SaleItem.create(
                productoNombre = "Producto B",
                cantidad = BigDecimal("1"),
                precioUnitario = BigDecimal("50.00"),
                tipoIva = TipoIva.IVA_21,
                porcentajeDescuento = BigDecimal("10") // 10% descuento
            )
        )
        
        return Sale.create(
            usuario = usuario,
            items = items,
            medioPago = "EFECTIVO",
            porcentajeDescuento = BigDecimal("10")
        )
    }
    
    private fun createSaleWithoutDiscount(): Sale {
        val usuario = Usuario("test_user", "Test User", "Test User", true)
        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto A",
                cantidad = BigDecimal("2"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21
            )
        )
        
        return Sale.create(
            usuario = usuario,
            items = items,
            medioPago = "EFECTIVO"
        )
    }
    
    private fun createTestComprobante(): Comprobante {
        return Comprobante.fromPersistence(
            id = Id(1),
            venta = Id(1),
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1,
            numeroComprobante = 123,
            cae = "74*********012",
            fechaEmision = LocalDateTime.now(),
            fechaVencimientoCae = LocalDate.now().plusDays(10),
            impTotal = BigDecimal("225.00"),
            impTotConc = BigDecimal.ZERO,
            impNeto = BigDecimal("185.95"),
            impIva = BigDecimal("39.05"),
            impTrib = BigDecimal.ZERO,
            monId = "PES",
            monCotiz = BigDecimal.ONE,
            estado = "EMITIDO"
        )
    }
    
    private operator fun String.times(n: Int): String = this.repeat(n)
}
