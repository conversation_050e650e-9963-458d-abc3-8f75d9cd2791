package com.gnico.majo

import com.gnico.majo.adapter.controller.rest.ProductoController
import com.gnico.majo.adapter.controller.rest.SaleController
import com.gnico.majo.adapter.controller.rest.SaleCancellationController

import com.gnico.majo.adapter.controller.rest.UsuarioController
import com.gnico.majo.adapter.controller.rest.ComprobanteController
import com.gnico.majo.adapter.controller.rest.PrintController
import com.gnico.majo.adapter.controller.rest.CategoriaController
import com.gnico.majo.adapter.controller.rest.ReportController
import com.gnico.majo.adapter.controller.rest.CaeaController
import com.gnico.majo.infrastructure.routes.configureProductoRoutes
import com.gnico.majo.infrastructure.routes.configureSaleRoutes

import com.gnico.majo.infrastructure.routes.configureUsuarioRoutes
import com.gnico.majo.infrastructure.routes.configureComprobanteRoutes
import com.gnico.majo.infrastructure.routes.configurePrintRoutes
import com.gnico.majo.infrastructure.routes.configureCategoriaRoutes
import com.gnico.majo.infrastructure.routes.configureReportRoutes
import com.gnico.majo.infrastructure.routes.configureCaeaRoutes
import com.gnico.majo.infrastructure.automation.CAEAStartupService
import com.gnico.majo.infrastructure.config.appModule
import com.gnico.majo.infrastructure.startup.ApplicationStartup
import io.ktor.serialization.kotlinx.json.json
import io.ktor.server.application.*
import io.ktor.server.plugins.contentnegotiation.ContentNegotiation
import io.ktor.server.plugins.cors.routing.CORS
import io.ktor.server.routing.*
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import org.koin.core.context.stopKoin
import org.koin.ktor.plugin.Koin
import org.koin.ktor.ext.inject
import org.koin.logger.slf4jLogger
import kotlinx.coroutines.launch


fun main(args: Array<String>) {
    io.ktor.server.netty.EngineMain.main(args)
}

fun Application.module() {
    install(ContentNegotiation) {
        json()
    }

    install(CORS) {
        // Allow all HTTP methods
        allowMethod(HttpMethod.Get)
        allowMethod(HttpMethod.Post)
        allowMethod(HttpMethod.Put)
        allowMethod(HttpMethod.Delete)
        allowMethod(HttpMethod.Patch)
        allowMethod(HttpMethod.Options)
        allowMethod(HttpMethod.Head)

        // Allow all common headers
        allowHeader(HttpHeaders.Authorization)
        allowHeader(HttpHeaders.ContentType)
        allowHeader(HttpHeaders.Accept)
        allowHeader(HttpHeaders.Origin)

        // Allow requests from any host
        anyHost()

        // Allow credentials if needed
        allowCredentials = true

        // Allow all headers for preflight requests
        allowNonSimpleContentTypes = true
    }

    install(Koin) {
        slf4jLogger()
        modules(appModule)
    }

    // Inicializar aplicación con validación de credenciales AFIP
    val applicationStartup by inject<ApplicationStartup>()
    val startupResult = applicationStartup.initialize()

    if (!startupResult.success) {
        log.error("❌ Error crítico durante la inicialización: ${startupResult.error}")
        // La aplicación continuará pero con funcionalidad limitada
    }

    val saleController by inject<SaleController>()
    val saleCancellationController by inject<SaleCancellationController>()
    configureSaleRoutes(saleController, saleCancellationController)

    val productoController by inject<ProductoController>()
    configureProductoRoutes(productoController)



    val usuarioController by inject<UsuarioController>()
    configureUsuarioRoutes(usuarioController)

    val comprobanteController by inject<ComprobanteController>()
    configureComprobanteRoutes(comprobanteController)

    val printController by inject<PrintController>()
    configurePrintRoutes(printController)

    val categoriaController by inject<CategoriaController>()
    configureCategoriaRoutes(categoriaController)

    // Configurar rutas de reportes
    val reportController by inject<ReportController>()
    configureReportRoutes(reportController)

    // Configurar rutas de CAEA
    val caeaController by inject<CaeaController>()
    configureCaeaRoutes(caeaController)

    // Ejecutar automatización CAEA al final del startup
    val caeaStartupService by inject<CAEAStartupService>()
    launch {
        try {
            caeaStartupService.ejecutarAutomatizacion()
        } catch (e: Exception) {
            environment.log.error("Error en automatización CAEA al startup: ${e.message}", e)
        }
    }

    monitor.subscribe(ApplicationStopping) {
        applicationStartup.shutdown()
        stopKoin()
    }
}

