package com.gnico.majo.infrastructure.routes

import com.gnico.majo.adapter.controller.rest.CategoriaController
import com.gnico.majo.adapter.controller.dto.ErrorResponse
import com.gnico.majo.adapter.controller.dto.CategoriaDto
import com.gnico.majo.adapter.controller.dto.BulkUpdateCategoriasOrdenRequest
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.request.receive
import io.ktor.server.response.respond
import io.ktor.server.routing.delete
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route
import io.ktor.server.routing.routing

fun Application.configureCategoriaRoutes(categoriaController: CategoriaController) {
    routing {

        route("/api/categorias") {

            // Obtener todas las categorías
            get {
                try {
                    val response = categoriaController.getAllCategorias()
                    call.respond(HttpStatusCode.OK, response)
                } catch (e: Exception) {
                    call.respond(
                        HttpStatusCode.InternalServerError,
                        ErrorResponse(e.message ?: "Error interno del servidor")
                    )
                }
            }

            // Obtener la categoría especial "Sin Categoría"
            get("/sin-categoria") {
                try {
                    val categoria = categoriaController.getSinCategoriaEspecial()
                    if (categoria != null) {
                        call.respond(HttpStatusCode.OK, categoria)
                    } else {
                        call.respond(HttpStatusCode.NotFound, ErrorResponse("Categoría especial 'Sin Categoría' no encontrada"))
                    }
                } catch (e: Exception) {
                    call.respond(
                        HttpStatusCode.InternalServerError,
                        ErrorResponse(e.message ?: "Error interno del servidor")
                    )
                }
            }

            // Obtener una categoría por ID
            get("/{id}") {
                try {
                    val id = call.parameters["id"]?.toIntOrNull()
                        ?: return@get call.respond(HttpStatusCode.BadRequest, ErrorResponse("ID inválido"))

                    val categoria = categoriaController.getCategoriaById(id)
                    if (categoria != null) {
                        call.respond(HttpStatusCode.OK, categoria)
                    } else {
                        call.respond(HttpStatusCode.NotFound, ErrorResponse("Categoría no encontrada"))
                    }
                } catch (e: Exception) {
                    call.respond(
                        HttpStatusCode.InternalServerError,
                        ErrorResponse(e.message ?: "Error interno del servidor")
                    )
                }
            }

            // Crear una nueva categoría
            post {
                try {
                    val request = call.receive<CategoriaDto>()
                    val response = categoriaController.createCategoria(request)
                    call.respond(HttpStatusCode.Created, response)
                } catch (e: IllegalArgumentException) {
                    call.respond(HttpStatusCode.BadRequest, ErrorResponse(e.message ?: "Solicitud inválida"))
                } catch (e: Exception) {
                    call.respond(
                        HttpStatusCode.InternalServerError,
                        ErrorResponse(e.message ?: "Error interno del servidor")
                    )
                }
            }

            // Actualizar una categoría existente
            put("/{id}") {
                try {
                    val id = call.parameters["id"]?.toIntOrNull()
                        ?: return@put call.respond(HttpStatusCode.BadRequest, ErrorResponse("ID inválido"))

                    val request = call.receive<CategoriaDto>()
                    val updated = categoriaController.updateCategoria(id, request)

                    if (updated) {
                        call.respond(HttpStatusCode.OK, mapOf("mensaje" to "Categoría actualizada exitosamente"))
                    } else {
                        call.respond(HttpStatusCode.NotFound, ErrorResponse("Categoría no encontrada"))
                    }
                } catch (e: IllegalArgumentException) {
                    call.respond(HttpStatusCode.BadRequest, ErrorResponse(e.message ?: "Solicitud inválida"))
                } catch (e: Exception) {
                    call.respond(
                        HttpStatusCode.InternalServerError,
                        ErrorResponse(e.message ?: "Error interno del servidor")
                    )
                }
            }

            // Eliminar una categoría (eliminación física)
            delete("/{id}") {
                try {
                    val id = call.parameters["id"]?.toIntOrNull()
                        ?: return@delete call.respond(HttpStatusCode.BadRequest, ErrorResponse("ID inválido"))

                    val deleted = categoriaController.deleteCategoria(id)

                    if (deleted) {
                        call.respond(HttpStatusCode.OK, mapOf("mensaje" to "Categoría eliminada exitosamente"))
                    } else {
                        call.respond(HttpStatusCode.NotFound, ErrorResponse("Categoría no encontrada"))
                    }
                } catch (e: IllegalArgumentException) {
                    call.respond(HttpStatusCode.BadRequest, ErrorResponse(e.message ?: "Solicitud inválida"))
                } catch (e: Exception) {
                    call.respond(
                        HttpStatusCode.InternalServerError,
                        ErrorResponse(e.message ?: "Error interno del servidor")
                    )
                }
            }

            // Actualizar orden de múltiples categorías
            put("/orden") {
                try {
                    val request = call.receive<BulkUpdateCategoriasOrdenRequest>()
                    val response = categoriaController.updateOrdenCategorias(request)
                    call.respond(HttpStatusCode.OK, response)
                } catch (e: IllegalArgumentException) {
                    call.respond(HttpStatusCode.BadRequest, ErrorResponse(e.message ?: "Solicitud inválida"))
                } catch (e: Exception) {
                    call.respond(
                        HttpStatusCode.InternalServerError,
                        ErrorResponse(e.message ?: "Error interno del servidor")
                    )
                }
            }
        }
    }
}
