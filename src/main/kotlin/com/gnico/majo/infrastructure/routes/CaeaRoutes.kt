package com.gnico.majo.infrastructure.routes

import com.gnico.majo.adapter.controller.rest.CaeaController
import io.ktor.server.application.*
import io.ktor.server.routing.*

/**
 * Configuración de rutas para gestión de CAEA
 *
 * Endpoints disponibles:
 * - POST /api/caea/solicitar - Solicitar CAEA a AFIP (usa punto de venta de configuración)
 * - POST /api/caea/informar - Informar CAEA automáticamente (usa punto de venta de configuración)
 * - GET /api/caea - Obtener CAEAs del punto de venta configurado
 * - GET /api/caea/{caeaCode}/comprobantes - Obtener comprobantes emitidos con CAEA
 */
fun Application.configureCaeaRoutes(caeaController: CaeaController) {
    routing {
        route("/api/caea") {
            
            // Solicitar CAEA a AFIP
            post("/solicitar") {
                caeaController.solicitarCAEA(call)
            }
            
            // Informar movimientos CAEA automáticamente
            post("/informar") {
                caeaController.informarMovimientosAutomatico(call)
            }
            
            // Obtener CAEAs del punto de venta configurado
            get {
                caeaController.getCaeas(call)
            }
            
            // Consultar comprobantes emitidos con un CAEA específico
            route("/{caeaCode}/comprobantes") {
                get {
                    caeaController.getComprobantesCAEA(call)
                }
            }
        }
    }
}
