package com.gnico.majo.infrastructure.routes

import com.gnico.majo.adapter.controller.rest.UsuarioController
import io.ktor.server.application.*
import io.ktor.server.routing.*

fun Application.configureUsuarioRoutes(controller: UsuarioController) {
    routing {
        route("/api/usuarios") {
            // Obtener todos los usuarios
            get {
                controller.getAllUsers(call)
            }
            
            // Obtener solo usuarios activos
            get("/active") {
                controller.getAllActiveUsers(call)
            }
            
            // Crear nuevo usuario
            post {
                controller.createUser(call)
            }

            // Obtener usuario específico por username
            get("/{username}") {
                controller.getUserByUsername(call)
            }
            
            // Actualizar usuario
            put("/{username}") {
                controller.updateUser(call)
            }
            
            // Eliminar usuario (soft delete)
            delete("/{username}") {
                controller.deleteUser(call)
            }
        }
    }
}
