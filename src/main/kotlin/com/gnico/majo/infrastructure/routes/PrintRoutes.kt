package com.gnico.majo.infrastructure.routes

import com.gnico.majo.adapter.controller.rest.PrintController
import com.gnico.majo.adapter.controller.dto.ErrorResponse
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.response.respond
import io.ktor.server.routing.post
import io.ktor.server.routing.route
import io.ktor.server.routing.routing

/**
 * Configuración de rutas para impresión de tickets
 *
 * Endpoints disponibles:
 * - POST /api/print/ticket/{ventaId}?comprobanteId={comprobanteId} - Impresión unificada
 * - POST /api/print/report - Impresión de reporte de ventas
 *
 * Para búsquedas de ventas/comprobantes, usar SaleRoutes:
 * - GET /api/sales/numero/{numeroVenta} - Buscar venta por número
 * - GET /api/sales - Buscar ventas con filtros
 * - GET /api/sales/{ventaId} - Buscar venta por ID
 */
fun Application.configurePrintRoutes(printController: PrintController) {
    routing {
        route("/api/print") {

            // POST /api/print/ticket/{ventaId} - Endpoint unificado para impresión de tickets
            post("/ticket/{ventaId}") {
                try {
                    val ventaIdParam = call.parameters["ventaId"]
                    if (ventaIdParam.isNullOrBlank()) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("ventaId es requerido"))
                        return@post
                    }

                    val ventaId = ventaIdParam.toIntOrNull()
                    if (ventaId == null) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("ventaId debe ser un número válido"))
                        return@post
                    }

                    // Parámetro opcional para comprobante
                    val comprobanteId = call.request.queryParameters["comprobanteId"]?.toIntOrNull()

                    println("🔍 POST /api/print/ticket/$ventaId" + if (comprobanteId != null) " (comprobante: $comprobanteId)" else "")
                    val response = printController.imprimirTicket(ventaId, comprobanteId)
                    println("✅ Ticket impreso: ${response.message}")
                    call.respond(HttpStatusCode.OK, response)

                } catch (e: Exception) {
                    println("❌ Error al imprimir ticket: ${e.message}")
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            // POST /api/print/report - Endpoint para impresión de reportes de ventas
            post("/report") {
                try {
                    val periodo = call.request.queryParameters["periodo"]
                    val fechaDesde = call.request.queryParameters["fechaDesde"]
                    val fechaHasta = call.request.queryParameters["fechaHasta"]
                    val usuarios = call.request.queryParameters.getAll("usuarios") ?: emptyList()
                    val mediosPago = call.request.queryParameters.getAll("mediosPago") ?: emptyList()
                    val incluirCanceladas = call.request.queryParameters["incluirCanceladas"]?.toBoolean() ?: false
                    val soloConComprobante = call.request.queryParameters["soloConComprobante"]?.toBoolean()
                    val soloMediosPagoElectronicos =
                        call.request.queryParameters["soloMediosPagoElectronicos"]?.toBoolean() ?: false

                    println("🔍 POST /api/print/report - periodo=$periodo, fechaDesde=$fechaDesde, fechaHasta=$fechaHasta")
                    println("   usuarios=$usuarios, mediosPago=$mediosPago, incluirCanceladas=$incluirCanceladas")
                    println("   soloConComprobante=$soloConComprobante, soloMediosPagoElectronicos=$soloMediosPagoElectronicos")

                    val response = printController.imprimirReporteVentas(
                        periodo = periodo,
                        fechaDesde = fechaDesde,
                        fechaHasta = fechaHasta,
                        usuarios = usuarios,
                        mediosPago = mediosPago,
                        incluirCanceladas = incluirCanceladas,
                        soloConComprobante = soloConComprobante,
                        soloMediosPagoElectronicos = soloMediosPagoElectronicos
                    )

                    println("✅ Reporte impreso: ${response.message}")
                    call.respond(HttpStatusCode.OK, response)

                } catch (e: Exception) {
                    println("❌ Error al imprimir reporte: ${e.message}")
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }
        }
    }
}
