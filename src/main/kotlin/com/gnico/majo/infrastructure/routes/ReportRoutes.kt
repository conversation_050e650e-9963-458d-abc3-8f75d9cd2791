package com.gnico.majo.infrastructure.routes

import com.gnico.majo.adapter.controller.rest.ReportController
import com.gnico.majo.adapter.controller.dto.ErrorResponse
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.response.*
import io.ktor.server.routing.*

/**
 * Configuración de rutas para reportes y estadísticas
 */
fun Application.configureReportRoutes(reportController: ReportController) {
    routing {
        route("/api/reports") {
            // GET /api/reports/sales - Reporte completo de ventas
            get("/sales") {
                try {
                    val periodo = call.request.queryParameters["periodo"]
                    val fechaDesde = call.request.queryParameters["fechaDesde"]
                    val fechaHasta = call.request.queryParameters["fechaHasta"]
                    val usuarios = call.request.queryParameters.getAll("usuarios") ?: emptyList()
                    val mediosPago = call.request.queryParameters.getAll("mediosPago") ?: emptyList()
                    val soloConComprobante = call.request.queryParameters["soloConComprobante"]?.toBoolean()
                    val soloMediosPagoElectronicos =
                        call.request.queryParameters["soloMediosPagoElectronicos"]?.toBoolean() ?: false

                    println("🔍 GET /api/reports/sales - periodo=$periodo, fechaDesde=$fechaDesde, fechaHasta=$fechaHasta")
                    println("   usuarios=$usuarios, mediosPago=$mediosPago")
                    println("   soloConComprobante=$soloConComprobante, soloMediosPagoElectronicos=$soloMediosPagoElectronicos")

                    val report = reportController.getSalesReport(
                        periodo = periodo,
                        fechaDesde = fechaDesde,
                        fechaHasta = fechaHasta,
                        usuarios = usuarios,
                        mediosPago = mediosPago,
                        soloConComprobante = soloConComprobante,
                        soloMediosPagoElectronicos = soloMediosPagoElectronicos
                    )

                    println("✅ Reporte generado: ${report.resumenGeneral.totalVentas} ventas, monto total: ${report.resumenGeneral.montoTotal}")
                    call.respond(HttpStatusCode.OK, report)

                } catch (e: IllegalArgumentException) {
                    println("❌ Error en parámetros: ${e.message}")
                    call.respond(HttpStatusCode.BadRequest, ErrorResponse(e.message ?: "Parámetros inválidos"))
                } catch (e: Exception) {
                    println("❌ Error interno: ${e.message}")
                    e.printStackTrace()
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Error interno del servidor"))
                }
            }


        }
    }
}
