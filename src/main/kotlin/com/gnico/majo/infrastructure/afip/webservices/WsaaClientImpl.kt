package com.gnico.majo.infrastructure.afip.webservices

import com.gnico.majo.infrastructure.afip.utils.TimeBuilder
import com.gnico.majo.application.domain.model.AfipCredentials
import https.wsaahomo_afip_gov_ar.ws.services.logincms.LoginCMS
import https.wsaahomo_afip_gov_ar.ws.services.logincms.LoginCMSService
import https.wsaahomo_afip_gov_ar.ws.services.logincms.LoginFault_Exception
import jakarta.xml.ws.soap.SOAPFaultException
import jakarta.xml.ws.BindingProvider
import org.bouncycastle.cert.jcajce.JcaCertStore
import org.bouncycastle.cms.CMSProcessableByteArray
import org.bouncycastle.cms.CMSSignedDataGenerator
import org.bouncycastle.cms.jcajce.JcaSignerInfoGeneratorBuilder
import org.bouncycastle.jce.provider.BouncyCastleProvider
import org.bouncycastle.operator.jcajce.JcaContentSignerBuilder
import org.bouncycastle.operator.jcajce.JcaDigestCalculatorProviderBuilder
import org.slf4j.LoggerFactory
import java.io.InputStream
import java.net.ConnectException
import java.net.SocketException
import java.net.SocketTimeoutException
import java.security.KeyStore
import java.security.PrivateKey
import java.security.Security
import java.security.cert.X509Certificate
import java.util.*
import javax.net.ssl.HttpsURLConnection
import javax.net.ssl.SSLContext
import javax.net.ssl.TrustManager
import javax.net.ssl.X509TrustManager


/**
 * Cliente mejorado para el webservice WSAA de AFIP
 * Maneja la autenticación y obtención de credenciales
 * Incluye configuración robusta para evitar errores de conexión
 */
class WsaaClientImpl(
    private val configuration: AfipConfiguration
) {

    private val logger = LoggerFactory.getLogger(WsaaClientImpl::class.java)

    companion object {
        private const val CONNECTION_TIMEOUT = 10000 // 10 segundos
        private const val REQUEST_TIMEOUT = 30000 // 30 segundos
        private const val MAX_RETRIES = 3
        private const val RETRY_DELAY_MS = 2000L // 2 segundos
    }

    init {
        // Configurar SSL para evitar problemas de conexión
        configureSSL()
    }
    
    /**
     * Realiza login en WSAA y obtiene credenciales
     */
    fun login(service: String = "wsfe"): AfipCredentials? {
        logger.info("Iniciando autenticación WSAA para servicio: $service")

        logger.info("Verificando cuit existe: ${configuration.cuit}")
        logger.info("Verificando path existe: ${configuration.certificatePath}")
        logger.info("Verificando path existe: ${configuration.certificatePassword}")

        return try {
            logger.info("Generando TRA...")
            val tra = generateTRA(service)
            logger.info("TRA generado: ${tra}")

            logger.info("Firmando TRA con certificado...")
            val signedCms = signXmlWithCMS(tra)
            logger.info("TRA firmado correctamente")

            logger.info("Enviando solicitud a AFIP...")
            val response = sendLoginRequest(signedCms)


            AfipCredentials(
                token = response.token,
                sign = response.sign,
                cuit = configuration.cuit
            )
        } catch (e: Exception) {
            logger.error("Error en autenticación WSAA: ${e.message}", e)
            null
        }
    }
    
    fun sendLoginRequest(signedCms: String): TokenAndSign {
        return executeWithRetry { attemptLoginRequest(signedCms) }
    }

    private fun attemptLoginRequest(signedCms: String): TokenAndSign {
        val loginService = LoginCMSService()
        val port: LoginCMS = loginService.loginCms

        // Configurar timeouts y propiedades del cliente
        configureWebServiceClient(port)

        try {
            val response = port.loginCms(signedCms)
            val token = extractValue(response, "token")
            val sign = extractValue(response, "sign")

            logger.info("AFIP WSAA authentication successful!")
            return TokenAndSign(token, sign)

        } catch (e: LoginFault_Exception) {
            logger.error("AFIP WSAA authentication failed: ${e.message}")
            logger.error("Fault info: ${e.faultInfo}")
            throw AfipAuthenticationException("Error de autenticación AFIP: ${e.message}", e)

        } catch (e: SOAPFaultException) {
            val fault = e.fault
            val faultCode = fault?.faultCode
            val faultString = fault?.faultString
            logger.error("AFIP WSAA SOAP Fault detected!")
            logger.error("Fault Code: $faultCode")
            logger.error("Fault String: $faultString")

            // Manejar caso específico de token ya existente
            if (faultCode?.contains("alreadyAuthenticated") == true) {
                logger.warn("AFIP indica que ya existe un token válido. Esto puede ser normal si se ejecutaron múltiples intentos recientes.")
                logger.info("Recomendación: Esperar unos minutos antes del próximo intento o verificar tokens existentes en base de datos.")
            }

            throw AfipAuthenticationException("Error SOAP en AFIP: $faultString", e)

        } catch (e: SocketException) {
            logger.error("Socket error during AFIP WSAA authentication: ${e.message}")
            // NO envolver en AfipAuthenticationException para permitir retry
            throw e

        } catch (e: java.net.ConnectException) {
            logger.error("Connection error during AFIP WSAA authentication: ${e.message}")
            // NO envolver en AfipAuthenticationException para permitir retry
            throw e

        } catch (e: java.net.SocketTimeoutException) {
            logger.error("Timeout error during AFIP WSAA authentication: ${e.message}")
            // NO envolver en AfipAuthenticationException para permitir retry
            throw e

        } catch (e: Exception) {
            // Para otros errores, verificar si contienen indicadores de problemas de red
            if (e.message?.contains("Connection reset") == true ||
                e.message?.contains("Connection refused") == true ||
                e.message?.contains("timeout") == true) {
                logger.error("Network error during AFIP WSAA authentication: ${e.message}")
                // NO envolver para permitir retry
                throw e
            } else {
                logger.error("Unexpected error during AFIP WSAA authentication: ${e.message}", e)
                throw AfipAuthenticationException("Error inesperado en autenticación AFIP: ${e.message}", e)
            }
        }
    }
    
    fun generateTRA(service: String): String {
        val uniqueId = System.currentTimeMillis() / 1000
        val generationTime = TimeBuilder.getTenMinutesBefore()
        val expirationTime = TimeBuilder.getTenMinutesLater()
        
        return """<?xml version="1.0" encoding="UTF-8"?>
            <loginTicketRequest version="1.0">
                <header>
                    <uniqueId>$uniqueId</uniqueId>
                    <generationTime>$generationTime</generationTime>
                    <expirationTime>$expirationTime</expirationTime>
                </header>
                <service>$service</service>
            </loginTicketRequest>""".trimIndent()
    }
    
    fun signXmlWithCMS(xml: String): String {
        Security.addProvider(BouncyCastleProvider())
        
        // Cargar el keystore PKCS12
        val p12Stream: InputStream = Thread.currentThread().contextClassLoader
            .getResourceAsStream(configuration.certificatePath)
            ?: throw IllegalArgumentException("Certificado P12 no encontrado: ${configuration.certificatePath}")
        
        val keystore = KeyStore.getInstance("PKCS12")
        p12Stream.use {
            keystore.load(it, configuration.certificatePassword.toCharArray())
        }
        
        // Obtener clave privada y certificado
        val privateKey = keystore.getKey(configuration.certificateAlias, configuration.certificatePassword.toCharArray()) as PrivateKey
        val cert = keystore.getCertificate(configuration.certificateAlias) as X509Certificate
        val certChain = keystore.getCertificateChain(configuration.certificateAlias).map { it as X509Certificate }
        
        // Preparar CMS signed data
        val certList = mutableListOf<X509Certificate>(cert)
        certList.addAll(certChain)
        val certStore = JcaCertStore(certList)
        
        val signedDataGenerator = CMSSignedDataGenerator()
        val contentSigner = JcaContentSignerBuilder("SHA256withRSA").build(privateKey)
        val signerInfoGenerator = JcaSignerInfoGeneratorBuilder(
            JcaDigestCalculatorProviderBuilder().build()
        ).build(contentSigner, cert)
        
        signedDataGenerator.addSignerInfoGenerator(signerInfoGenerator)
        signedDataGenerator.addCertificates(certStore)
        
        // Crear CMS signed data con el contenido XML
        val cmsProcessable = CMSProcessableByteArray(xml.toByteArray(Charsets.UTF_8))
        val signedData = signedDataGenerator.generate(cmsProcessable, true)
        
        return Base64.getEncoder().encodeToString(signedData.encoded)
    }
    
    fun extractValue(xml: String, tagName: String): String {
        val startTag = "<$tagName>"
        val endTag = "</$tagName>"
        val startIndex = xml.indexOf(startTag)
        val endIndex = xml.indexOf(endTag)

        if (startIndex == -1 || endIndex == -1) {
            throw IllegalArgumentException("Tag '$tagName' no encontrado en la respuesta XML")
        }

        return xml.substring(startIndex + startTag.length, endIndex)
    }

    /**
     * Configura SSL para evitar problemas de conexión con AFIP
     */
    private fun configureSSL() {
        try {
            // Crear un TrustManager que acepta todos los certificados
            val trustAllCerts = arrayOf<TrustManager>(object : X509TrustManager {
                override fun getAcceptedIssuers(): Array<java.security.cert.X509Certificate>? = null
                override fun checkClientTrusted(certs: Array<java.security.cert.X509Certificate>, authType: String) {}
                override fun checkServerTrusted(certs: Array<java.security.cert.X509Certificate>, authType: String) {}
            })

            // Instalar el TrustManager que acepta todos los certificados
            val sc = SSLContext.getInstance("SSL")
            sc.init(null, trustAllCerts, java.security.SecureRandom())
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.socketFactory)

            // Configurar propiedades del sistema para SSL
            System.setProperty("https.protocols", "TLSv1.2,TLSv1.3")
            System.setProperty("jdk.tls.client.protocols", "TLSv1.2,TLSv1.3")

            logger.debug("SSL configurado correctamente para AFIP")
        } catch (e: Exception) {
            logger.warn("No se pudo configurar SSL: ${e.message}")
        }
    }

    /**
     * Configura el cliente del webservice con timeouts y headers apropiados
     */
    private fun configureWebServiceClient(port: LoginCMS) {
        val bindingProvider = port as BindingProvider
        val requestContext = bindingProvider.requestContext

        // Configurar timeouts
        requestContext[BindingProvider.ENDPOINT_ADDRESS_PROPERTY] = getWsaaUrl()
        requestContext["com.sun.xml.ws.connect.timeout"] = CONNECTION_TIMEOUT
        requestContext["com.sun.xml.ws.request.timeout"] = REQUEST_TIMEOUT
        requestContext["com.sun.xml.internal.ws.connect.timeout"] = CONNECTION_TIMEOUT
        requestContext["com.sun.xml.internal.ws.request.timeout"] = REQUEST_TIMEOUT

        // Configurar User-Agent y headers HTTP
        requestContext["com.sun.xml.ws.transport.http.client.HttpTransportPipe.dump"] = false

        // Headers HTTP adicionales
        val httpHeaders = mutableMapOf<String, List<String>>()
        httpHeaders["User-Agent"] = listOf("AFIP-WS-Client/1.0")
        httpHeaders["Accept"] = listOf("text/xml, application/soap+xml")
        httpHeaders["Connection"] = listOf("keep-alive")
        requestContext["com.sun.xml.ws.api.message.MessageContextProperties.HTTP_REQUEST_HEADERS"] = httpHeaders

        logger.debug("Cliente webservice configurado con timeouts: connect=$CONNECTION_TIMEOUT, request=$REQUEST_TIMEOUT")
    }

    /**
     * Obtiene la URL del servicio WSAA según el entorno
     */
    private fun getWsaaUrl(): String {
        return if (configuration.isProduction) {
            "https://wsaa.afip.gov.ar/ws/services/LoginCms"
        } else {
            "https://wsaahomo.afip.gov.ar/ws/services/LoginCms"
        }
    }

    /**
     * Ejecuta una operación con lógica de retry
     */
    private fun <T> executeWithRetry(operation: () -> T): T {
        var lastException: Exception? = null
        logger.info("Ejecutando operación WSAA con retry (máximo $MAX_RETRIES intentos)")

        for (attempt in 0 until MAX_RETRIES) {
            try {
                logger.debug("Intento ${attempt + 1}/$MAX_RETRIES")
                return operation()

            } catch (e: AfipAuthenticationException) {
                // No reintentar errores de autenticación específicos de AFIP (credenciales inválidas, etc.)
                logger.error("Error de autenticación AFIP (no se reintenta): ${e.message}")
                throw e

            } catch (e: SocketException) {
                lastException = e
                logger.warn("Intento ${attempt + 1}/$MAX_RETRIES falló - SocketException: ${e.message}")
                if (shouldRetry(attempt)) {
                    waitBeforeRetry(attempt)
                    continue
                }

            } catch (e: java.net.ConnectException) {
                lastException = e
                logger.warn("Intento ${attempt + 1}/$MAX_RETRIES falló - ConnectException: ${e.message}")
                if (shouldRetry(attempt)) {
                    waitBeforeRetry(attempt)
                    continue
                }

            } catch (e: java.net.SocketTimeoutException) {
                lastException = e
                logger.warn("Intento ${attempt + 1}/$MAX_RETRIES falló - SocketTimeoutException: ${e.message}")
                if (shouldRetry(attempt)) {
                    waitBeforeRetry(attempt)
                    continue
                }

            } catch (e: Exception) {
                // Verificar si es un error de red que vale la pena reintentar
                if (isNetworkError(e)) {
                    lastException = e
                    logger.warn("Intento ${attempt + 1}/$MAX_RETRIES falló - Error de red: ${e.message}")
                    if (shouldRetry(attempt)) {
                        waitBeforeRetry(attempt)
                        continue
                    }
                } else {
                    // Para otros errores, no reintentar
                    logger.error("Error no recuperable (no se reintenta): ${e.message}")
                    throw AfipAuthenticationException("Error no recuperable en WSAA: ${e.message}", e)
                }
            }
        }

        // Si llegamos aquí, todos los intentos fallaron
        logger.error("Todos los intentos de WSAA fallaron después de $MAX_RETRIES intentos")
        throw AfipAuthenticationException(
            "WSAA falló después de $MAX_RETRIES intentos. Último error: ${lastException?.message}",
            lastException
        )
    }

    /**
     * Determina si debemos reintentar basado en el número de intento
     */
    private fun shouldRetry(attempt: Int): Boolean {
        return attempt < MAX_RETRIES - 1
    }

    /**
     * Espera antes del siguiente intento con backoff exponencial
     */
    private fun waitBeforeRetry(attempt: Int) {
        val delay = RETRY_DELAY_MS * (attempt + 1) // Backoff lineal
        logger.info("Esperando ${delay}ms antes del siguiente intento...")
        Thread.sleep(delay)
    }

    /**
     * Determina si un error es de red y vale la pena reintentar
     */
    private fun isNetworkError(e: Exception): Boolean {
        val message = e.message?.lowercase() ?: ""
        return message.contains("connection reset") ||
               message.contains("connection refused") ||
               message.contains("timeout") ||
               message.contains("network is unreachable") ||
               message.contains("host is unreachable") ||
               message.contains("connection timed out")
    }
}

/**
 * Configuración para AFIP
 */
data class AfipConfiguration(
    val cuit: Long,
    val certificatePath: String,
    val certificatePassword: String,
    val certificateAlias: String = "1",
    val isProduction: Boolean = false
)

/**
 * Datos de token y sign de AFIP
 */
data class TokenAndSign(val token: String, val sign: String)

/**
 * Excepción específica para errores de autenticación AFIP
 */
class AfipAuthenticationException(message: String, cause: Throwable? = null) : Exception(message, cause)
