package com.gnico.majo.infrastructure.afip

import com.gnico.majo.application.domain.model.WsaaCredentials
import com.gnico.majo.application.domain.model.AfipCredentials
import com.gnico.majo.application.domain.model.EstadoCredencial
import com.gnico.majo.application.port.out.WsaaCredentialsRepository
import com.gnico.majo.infrastructure.afip.webservices.WsaaClientImpl
import com.gnico.majo.infrastructure.afip.webservices.AfipConfiguration
import com.gnico.majo.infrastructure.afip.webservices.AfipAuthenticationException
import org.slf4j.LoggerFactory
import java.time.LocalDateTime

/**
 * Servicio para manejar credenciales WSAA con persistencia en base de datos
 * Incluye validación automática y renovación de credenciales
 */
class WsaaCredentialsService(
    private val credentialsRepository: WsaaCredentialsRepository,
    private val wsaaClient: WsaaClientImpl,
    private val configuration: AfipConfiguration
) {
    
    private val logger = LoggerFactory.getLogger(WsaaCredentialsService::class.java)
    
    /**
     * Obtiene credenciales válidas para un servicio, renovándolas si es necesario
     */
    fun getValidCredentials(servicio: String = "wsfe"): AfipCredentials? {
        logger.info("Obteniendo credenciales válidas para servicio: $servicio")
        
        return try {
            // Buscar credenciales existentes en base de datos
            val existingCredentials = credentialsRepository.findActiveCredentials(servicio)
            
            when {
                existingCredentials == null -> {
                    logger.info("No se encontraron credenciales para $servicio, solicitando nuevas")
                    requestNewCredentials(servicio)
                }
                
                existingCredentials.isExpired() -> {
                    logger.info("Credenciales expiradas para $servicio, solicitando nuevas")
                    markAsExpiredAndRequestNew(servicio, existingCredentials)
                }
                
                existingCredentials.isNearExpiry() -> {
                    logger.info("Credenciales próximas a vencer para $servicio (${existingCredentials.getMinutesUntilExpiry()} min), renovando")
                    requestNewCredentials(servicio)
                }
                
                else -> {
                    logger.info("Credenciales válidas encontradas para $servicio, válidas por ${existingCredentials.getMinutesUntilExpiry()} minutos más")
                    existingCredentials.toAfipCredentials(configuration.cuit)
                }
            }
        } catch (e: Exception) {
            logger.error("Error al obtener credenciales para $servicio: ${e.message}", e)
            null
        }
    }
    
    /**
     * Fuerza la renovación de credenciales para un servicio
     */
    fun renewCredentials(servicio: String = "wsfe"): AfipCredentials? {
        logger.info("Forzando renovación de credenciales para servicio: $servicio")
        return requestNewCredentials(servicio)
    }
    
    /**
     * Verifica si existen credenciales válidas sin renovarlas
     */
    fun hasValidCredentials(servicio: String = "wsfe"): Boolean {
        val credentials = credentialsRepository.findActiveCredentials(servicio)
        return credentials?.isValid() == true
    }
    
    /**
     * Realiza limpieza de credenciales expiradas
     */
    fun cleanupExpiredCredentials(olderThanDays: Int = 7): Int {
        logger.info("Limpiando credenciales expiradas más antiguas que $olderThanDays días")

        return try {
            // Marcar credenciales expiradas como tal (solo las que no están ya marcadas)
            val expired = credentialsRepository.findExpired()
            var markedAsExpired = 0

            expired.forEach { credential ->
                // Solo actualizar si no está ya marcada como expirada
                if (credential.estado != EstadoCredencial.EXPIRADO) {
                    credentialsRepository.update(credential.markAsExpired())
                    markedAsExpired++
                }
            }

            // Eliminar credenciales muy antiguas
            val deleted = credentialsRepository.deleteExpiredOlderThan(olderThanDays)
            logger.info("Limpieza completada: $markedAsExpired marcadas como expiradas, $deleted eliminadas")

            deleted
        } catch (e: Exception) {
            logger.error("Error durante limpieza de credenciales: ${e.message}", e)
            0
        }
    }
    
    /**
     * Obtiene estadísticas de credenciales
     */
    fun getCredentialsStats(): CredentialsStats {
        return try {
            val nearExpiry = credentialsRepository.findNearExpiry()
            val expired = credentialsRepository.findExpired()
            
            CredentialsStats(
                nearExpiryCount = nearExpiry.size,
                expiredCount = expired.size,
                services = nearExpiry.map { it.servicio }.distinct() + expired.map { it.servicio }.distinct()
            )
        } catch (e: Exception) {
            logger.error("Error al obtener estadísticas de credenciales: ${e.message}", e)
            CredentialsStats(0, 0, emptyList())
        }
    }
    
    /**
     * Valida todas las credenciales al iniciar el programa
     */
    fun validateAllCredentialsOnStartup(): ValidationResult {
        logger.info("Validando todas las credenciales al iniciar el programa")
        
        val services = listOf("wsfe") // Agregar más servicios según sea necesario
        val results = mutableMapOf<String, Boolean>()
        var totalRenewed = 0
        
        services.forEach { servicio ->
            try {
                val credentials = getValidCredentials(servicio)
                val isValid = credentials != null
                results[servicio] = isValid
                
                if (isValid) {
                    logger.info("✓ Credenciales válidas para $servicio")
                } else {
                    logger.warn("✗ No se pudieron obtener credenciales válidas para $servicio")
                }
                
                // Si se renovaron credenciales, incrementar contador
                val existing = credentialsRepository.findActiveCredentials(servicio)
                if (existing != null && existing.fechaGeneracion.isAfter(LocalDateTime.now().minusMinutes(5))) {
                    totalRenewed++
                }
                
            } catch (e: Exception) {
                logger.error("Error validando credenciales para $servicio: ${e.message}", e)
                results[servicio] = false
            }
        }
        
        // Realizar limpieza
        val cleaned = cleanupExpiredCredentials()
        
        val validServices = results.filter { it.value }.keys.size
        val totalServices = results.size
        
        logger.info("Validación completada: $validServices/$totalServices servicios válidos, $totalRenewed renovadas, $cleaned limpiadas")
        
        return ValidationResult(
            totalServices = totalServices,
            validServices = validServices,
            renewedCredentials = totalRenewed,
            cleanedCredentials = cleaned,
            serviceResults = results
        )
    }
    
    private fun requestNewCredentials(servicio: String): AfipCredentials? {
        return try {
            logger.info("Solicitando nuevas credenciales a AFIP para servicio: $servicio")
            
            val afipCredentials = wsaaClient.login(servicio)
            if (afipCredentials != null) {
                // Crear modelo de dominio
                val wsaaCredentials = WsaaCredentials.fromAfipResponse(
                    servicio = servicio,
                    token = afipCredentials.token,
                    sign = afipCredentials.sign,
                    duracionHoras = 12L // AFIP tokens duran 12 horas
                )
                
                // Guardar en base de datos
                credentialsRepository.save(wsaaCredentials)
                
                logger.info("Nuevas credenciales guardadas para $servicio, válidas hasta: ${wsaaCredentials.fechaExpiracion}")
                afipCredentials
            } else {
                logger.error("No se pudieron obtener credenciales de AFIP para $servicio")
                null
            }
        } catch (e: AfipAuthenticationException) {
            logger.error("Error de autenticación AFIP para $servicio: ${e.message}", e)
            null
        } catch (e: Exception) {
            logger.error("Error inesperado al solicitar credenciales para $servicio: ${e.message}", e)
            null
        }
    }
    
    private fun markAsExpiredAndRequestNew(servicio: String, expiredCredentials: WsaaCredentials): AfipCredentials? {
        // Marcar como expiradas
        credentialsRepository.update(expiredCredentials.markAsExpired())
        
        // Solicitar nuevas
        return requestNewCredentials(servicio)
    }
}

/**
 * Estadísticas de credenciales
 */
data class CredentialsStats(
    val nearExpiryCount: Int,
    val expiredCount: Int,
    val services: List<String>
)

/**
 * Resultado de validación de credenciales
 */
data class ValidationResult(
    val totalServices: Int,
    val validServices: Int,
    val renewedCredentials: Int,
    val cleanedCredentials: Int,
    val serviceResults: Map<String, Boolean>
) {
    fun isAllValid(): Boolean = validServices == totalServices
    
    fun getSuccessRate(): Double = if (totalServices > 0) {
        validServices.toDouble() / totalServices.toDouble()
    } else {
        0.0
    }
}
