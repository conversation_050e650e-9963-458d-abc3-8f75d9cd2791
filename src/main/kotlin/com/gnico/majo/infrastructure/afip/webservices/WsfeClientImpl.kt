package com.gnico.majo.infrastructure.afip.webservices

import com.gnico.majo.infrastructure.afip.utils.TimeBuilder
import com.gnico.majo.application.domain.model.*
import fev1.dif.afip.gov.ar.*
import jakarta.xml.ws.BindingProvider
import org.slf4j.LoggerFactory
import java.net.ConnectException
import java.net.SocketException
import java.net.SocketTimeoutException

/**
 * Cliente mejorado para el webservice WSFE de AFIP
 * Implementa la lógica completa de solicitud de CAE
 * Incluye configuración robusta para evitar errores de conexión
 */
class WsfeClientImpl {

    private val logger = LoggerFactory.getLogger(WsfeClientImpl::class.java)

    companion object {
        private const val CONNECTION_TIMEOUT = 10000 // 10 segundos
        private const val REQUEST_TIMEOUT = 30000 // 30 segundos
        private const val MAX_RETRIES = 3
        private const val RETRY_DELAY_MS = 2000L // 2 segundos
    }
    
    /**
     * Solicita un CAE para una venta específica
     */
    fun requestCAE(request: AfipCAERequest): WsfeResponse {
        logger.info("Solicitando CAE para venta ${request.sale.numeroVenta}")

        return executeWithRetry {
            attemptRequestCAE(request)
        }
    }

    private fun attemptRequestCAE(request: AfipCAERequest): WsfeResponse {
        val service = Service()
        val port = service.serviceSoap

        // Configurar cliente con timeouts
        configureWebServiceClient(port, "wsfe")

        try {
            // 1. Crear autenticación
            val auth = createAuthRequest(request.credentials)

            // 2. Crear solicitud de factura
            val caeRequest = createCAERequest(request)

            // 3. Llamar al servicio
            val response = port.fecaeSolicitar(auth, caeRequest)

            // 4. Procesar respuesta
            return processResponse(response, request.sale.numeroVenta)

        } catch (e: SocketException) {
            logger.error("Socket error during CAE request for sale ${request.sale.numeroVenta}: ${e.message}")
            // NO envolver para permitir retry
            throw e

        } catch (e: ConnectException) {
            logger.error("Connection error during CAE request for sale ${request.sale.numeroVenta}: ${e.message}")
            // NO envolver para permitir retry
            throw e

        } catch (e: SocketTimeoutException) {
            logger.error("Timeout error during CAE request for sale ${request.sale.numeroVenta}: ${e.message}")
            // NO envolver para permitir retry
            throw e

        } catch (ex: Exception) {
            // Verificar si es un error de red
            if (ex.message?.contains("Connection reset") == true ||
                ex.message?.contains("Connection refused") == true ||
                ex.message?.contains("timeout") == true) {
                logger.error("Network error during CAE request for sale ${request.sale.numeroVenta}: ${ex.message}")
                // NO envolver para permitir retry
                throw ex
            } else {
                logger.error("Error al solicitar CAE para venta ${request.sale.numeroVenta}: ${ex.message}", ex)
                throw AfipWebserviceException("Error al comunicarse con AFIP: ${ex.message}", ex)
            }
        }
    }
    
    /**
     * Obtiene el último número de comprobante para un punto de venta y tipo
     */
    fun getLastInvoiceNumber(
        credentials: AfipCredentials,
        puntoVenta: Int,
        tipoComprobante: TipoComprobanteAfip
    ): Long {
        logger.info("Obteniendo último número de comprobante para PV: $puntoVenta, Tipo: ${tipoComprobante.codigo}")

        return executeWithRetry {
            attemptGetLastInvoiceNumber(credentials, puntoVenta, tipoComprobante)
        }
    }

    private fun attemptGetLastInvoiceNumber(
        credentials: AfipCredentials,
        puntoVenta: Int,
        tipoComprobante: TipoComprobanteAfip
    ): Long {
        val service = Service()
        val port = service.serviceSoap

        // Configurar cliente con timeouts
        configureWebServiceClient(port, "wsfe")

        try {
            val auth = createAuthRequest(credentials)
            val response = port.feCompUltimoAutorizado(auth, puntoVenta, tipoComprobante.codigo)

            return response?.cbteNro?.toLong() ?: 0L

        } catch (e: SocketException) {
            logger.error("Socket error getting last invoice number: ${e.message}")
            throw AfipWebserviceException("Error de conexión con AFIP: ${e.message}", e)

        } catch (ex: Exception) {
            logger.error("Error al obtener último número de comprobante: ${ex.message}", ex)
            return 0L
        }
    }
    
    private fun createAuthRequest(credentials: AfipCredentials): FEAuthRequest {
        return FEAuthRequest().apply {
            token = credentials.token
            sign = credentials.sign
            cuit = credentials.cuit
        }
    }
    
    private fun createCAERequest(request: AfipCAERequest): FECAERequest {
        val invoiceData = request.toAfipInvoiceData()
        
        // Obtener próximo número de comprobante
        val lastNumber = getLastInvoiceNumber(
            request.credentials,
            request.puntoVenta,
            request.tipoComprobante
        )
        val nextNumber = lastNumber + 1
        
        // Crear detalle de la factura
        val condicionIva = request.obtenerCondicionIvaReceptor()
        val invoice = FECAEDetRequest().apply {
            concepto = invoiceData.concepto
            docTipo = invoiceData.docTipo
            docNro = invoiceData.docNro
            cbteDesde = nextNumber
            cbteHasta = nextNumber
            cbteFch = TimeBuilder.getCurrentDate()
            impTotal = invoiceData.impTotal.toDouble()
            impTotConc = invoiceData.impTotConc.toDouble()
            impNeto = invoiceData.impNeto.toDouble()
            impIVA = invoiceData.impIva.toDouble()
            impTrib = invoiceData.impTrib.toDouble()
            monId = invoiceData.monId
            monCotiz = invoiceData.monCotiz.toDouble()
            condicionIVAReceptorId = condicionIva.codigo
        }

        // Log detallado de los datos enviados
        logger.info("Datos enviados a AFIP para venta ${request.sale.numeroVenta}:")
        logger.info("  - Tipo comprobante: ${request.tipoComprobante.codigo} (${request.tipoComprobante.descripcion})")
        logger.info("  - Punto de venta: ${request.puntoVenta}")
        logger.info("  - Número: $nextNumber")
        logger.info("  - Condición IVA receptor: ${condicionIva.codigo} (${condicionIva.descripcion})")
        logger.info("  - Doc tipo: ${invoiceData.docTipo}, Doc nro: ${invoiceData.docNro}")
        logger.info("  - Importes: Total=${invoiceData.impTotal}, Neto=${invoiceData.impNeto}, IVA=${invoiceData.impIva}")
        
        // Agregar detalles de IVA si existen
        if (invoiceData.ivaDetails.isNotEmpty()) {
            val ivaArray = ArrayOfAlicIva().apply {
                invoiceData.ivaDetails.forEach { ivaDetail ->
                    alicIva.add(AlicIva().apply {
                        id = ivaDetail.id
                        baseImp = ivaDetail.baseImponible.toDouble()
                        importe = ivaDetail.importe.toDouble()
                    })
                }
            }
            invoice.iva = ivaArray
        }

        // Agregar comprobante asociado si es requerido (notas de crédito/débito)
        if (request.comprobanteAsociado != null) {
            val cbteAsocArray = ArrayOfCbteAsoc().apply {
                cbteAsoc.add(CbteAsoc().apply {
                    tipo = request.comprobanteAsociado.getTipoComprobanteAfipCodigo()
                    ptoVta = request.comprobanteAsociado.puntoVenta
                    nro = request.comprobanteAsociado.numeroComprobante.toLong()
                    // cuit se deja null para comprobantes propios
                })
            }
            invoice.cbtesAsoc = cbteAsocArray

            logger.info("Comprobante asociado agregado: Tipo=${request.comprobanteAsociado.getTipoComprobanteAfipCodigo()}, PV=${request.comprobanteAsociado.puntoVenta}, Nro=${request.comprobanteAsociado.numeroComprobante}")
        }
        
        // Crear la solicitud principal
        return FECAERequest().apply {
            feCabReq = FECAECabRequest().apply {
                cantReg = 1
                ptoVta = request.puntoVenta
                cbteTipo = request.tipoComprobante.codigo
            }
            feDetReq = ArrayOfFECAEDetRequest().apply {
                fecaeDetRequest.add(invoice)
            }
        }
    }
    
    private fun processResponse(response: FECAEResponse?, saleNumber: String): WsfeResponse {
        if (response == null) {
            throw AfipWebserviceException("No se recibió respuesta del servicio AFIP")
        }
        
        // Verificar errores globales
        if (response.errors?.getErr()?.isNotEmpty() == true) {
            val errors = response.errors.getErr().map { "${it.code}: ${it.msg}" }
            logger.error("Errores en respuesta AFIP para venta $saleNumber: $errors")
            return WsfeResponse(
                cae = "ERROR",
                caeFchVto = "",
                resultado = "R",
                numeroComprobante = 0,
                observaciones = errors
            )
        }
        
        // Procesar respuesta del detalle
        val detResp = response.feDetResp?.getFECAEDetResponse()?.firstOrNull()
        if (detResp == null) {
            throw AfipWebserviceException("No se recibió detalle de respuesta de AFIP")
        }
        
        val observaciones = mutableListOf<String>()
        
        // Agregar eventos como observaciones
        response.events?.getEvt()?.forEach { evt ->
            observaciones.add("${evt.code}: ${evt.msg}")
        }
        
        // Agregar observaciones del detalle
        detResp.observaciones?.getObs()?.forEach { obs ->
            observaciones.add("${obs.code}: ${obs.msg}")
        }

        // Log detallado de errores y observaciones
        if (observaciones.isNotEmpty()) {
            logger.warn("Observaciones/Errores AFIP para venta $saleNumber: ${observaciones.joinToString("; ")}")
        }

        logger.info("CAE procesado para venta $saleNumber: ${detResp.resultado}, CAE: ${detResp.cae}")
        
        return WsfeResponse(
            cae = detResp.cae ?: "ERROR",
            caeFchVto = detResp.caeFchVto ?: "",
            resultado = detResp.resultado ?: "R",
            numeroComprobante = detResp.cbteDesde ?: 0,
            observaciones = observaciones
        )
    }

    /**
     * Configura el cliente del webservice con timeouts y headers apropiados
     */
    private fun configureWebServiceClient(port: Any, serviceName: String) {
        val bindingProvider = port as BindingProvider
        val requestContext = bindingProvider.requestContext

        // Configurar URL del endpoint según el entorno
        val endpointUrl = if (serviceName == "wsfe") {
            "https://wswhomo.afip.gov.ar/wsfev1/service.asmx" // Homologación por defecto
        } else {
            requestContext[BindingProvider.ENDPOINT_ADDRESS_PROPERTY] as String
        }

        // Configurar timeouts
        requestContext[BindingProvider.ENDPOINT_ADDRESS_PROPERTY] = endpointUrl
        requestContext["com.sun.xml.ws.connect.timeout"] = CONNECTION_TIMEOUT
        requestContext["com.sun.xml.ws.request.timeout"] = REQUEST_TIMEOUT
        requestContext["com.sun.xml.internal.ws.connect.timeout"] = CONNECTION_TIMEOUT
        requestContext["com.sun.xml.internal.ws.request.timeout"] = REQUEST_TIMEOUT

        // Headers HTTP adicionales
        val httpHeaders = mutableMapOf<String, List<String>>()
        httpHeaders["User-Agent"] = listOf("AFIP-WS-Client/1.0")
        httpHeaders["Accept"] = listOf("text/xml, application/soap+xml")
        httpHeaders["Connection"] = listOf("keep-alive")
        requestContext["com.sun.xml.ws.api.message.MessageContextProperties.HTTP_REQUEST_HEADERS"] = httpHeaders

        logger.debug("Cliente webservice $serviceName configurado con timeouts: connect=$CONNECTION_TIMEOUT, request=$REQUEST_TIMEOUT")
    }

    /**
     * Ejecuta una operación con lógica de retry
     */
    private fun <T> executeWithRetry(operation: () -> T): T {
        var lastException: Exception? = null
        logger.info("Ejecutando operación WSFE con retry (máximo $MAX_RETRIES intentos)")

        for (attempt in 0 until MAX_RETRIES) {
            try {
                logger.debug("Intento ${attempt + 1}/$MAX_RETRIES")
                return operation()

            } catch (e: AfipWebserviceException) {
                // Para errores específicos de AFIP, no reintentar (excepto errores de conexión)
                if (e.cause is SocketException || e.cause is ConnectException || e.cause is SocketTimeoutException) {
                    lastException = e
                    logger.warn("Intento ${attempt + 1}/$MAX_RETRIES falló - Error de conexión AFIP: ${e.message}")
                    if (shouldRetry(attempt)) {
                        waitBeforeRetry(attempt)
                        continue
                    }
                } else {
                    logger.error("Error de AFIP (no se reintenta): ${e.message}")
                    throw e
                }

            } catch (e: SocketException) {
                lastException = e
                logger.warn("Intento ${attempt + 1}/$MAX_RETRIES falló - SocketException: ${e.message}")
                if (shouldRetry(attempt)) {
                    waitBeforeRetry(attempt)
                    continue
                }

            } catch (e: ConnectException) {
                lastException = e
                logger.warn("Intento ${attempt + 1}/$MAX_RETRIES falló - ConnectException: ${e.message}")
                if (shouldRetry(attempt)) {
                    waitBeforeRetry(attempt)
                    continue
                }

            } catch (e: SocketTimeoutException) {
                lastException = e
                logger.warn("Intento ${attempt + 1}/$MAX_RETRIES falló - SocketTimeoutException: ${e.message}")
                if (shouldRetry(attempt)) {
                    waitBeforeRetry(attempt)
                    continue
                }

            } catch (e: Exception) {
                // Verificar si es un error de red que vale la pena reintentar
                if (isNetworkError(e)) {
                    lastException = e
                    logger.warn("Intento ${attempt + 1}/$MAX_RETRIES falló - Error de red: ${e.message}")
                    if (shouldRetry(attempt)) {
                        waitBeforeRetry(attempt)
                        continue
                    }
                } else {
                    // Para otros errores, no reintentar
                    logger.error("Error no recuperable (no se reintenta): ${e.message}")
                    throw AfipWebserviceException("Error no recuperable en WSFE: ${e.message}", e)
                }
            }
        }

        // Si llegamos aquí, todos los intentos fallaron
        logger.error("Todos los intentos de WSFE fallaron después de $MAX_RETRIES intentos")
        throw AfipWebserviceException(
            "WSFE falló después de $MAX_RETRIES intentos. Último error: ${lastException?.message}",
            lastException
        )
    }

    /**
     * Determina si debemos reintentar basado en el número de intento
     */
    private fun shouldRetry(attempt: Int): Boolean {
        return attempt < MAX_RETRIES - 1
    }

    /**
     * Espera antes del siguiente intento con backoff lineal
     */
    private fun waitBeforeRetry(attempt: Int) {
        val delay = RETRY_DELAY_MS * (attempt + 1)
        logger.info("Esperando ${delay}ms antes del siguiente intento...")
        Thread.sleep(delay)
    }

    /**
     * Determina si un error es de red y vale la pena reintentar
     */
    private fun isNetworkError(e: Exception): Boolean {
        val message = e.message?.lowercase() ?: ""
        return message.contains("connection reset") ||
               message.contains("connection refused") ||
               message.contains("timeout") ||
               message.contains("network is unreachable") ||
               message.contains("host is unreachable") ||
               message.contains("connection timed out")
    }

    /**
     * Solicita un CAEA (Código de Autorización Electrónico Anticipado) a AFIP
     */
    fun requestCAEA(request: AfipCAEARequest): WsfeCAEAResponse {
        logger.info("Solicitando CAEA para punto de venta ${request.puntoVenta}, período ${request.periodo}, orden ${request.orden}")

        return executeWithRetry {
            attemptRequestCAEA(request)
        }
    }

    private fun attemptRequestCAEA(request: AfipCAEARequest): WsfeCAEAResponse {
        val service = Service()
        val port = service.serviceSoap

        // Configurar cliente con timeouts
        configureWebServiceClient(port, "wsfe")

        try {
            // 1. Crear autenticación
            val auth = createAuthRequest(request.credentials)

            // 2. Llamar al servicio FECAEASolicitar con los parámetros correctos
            val response = port.fecaeaSolicitar(auth, request.periodo.toInt(), request.orden.toShort())

            // 3. Procesar respuesta
            return processCAEAGetResponse(response, request.periodo)

        } catch (e: SocketException) {
            logger.error("Socket error during CAEA request for period ${request.periodo}: ${e.message}")
            throw e
        } catch (e: ConnectException) {
            logger.error("Connection error during CAEA request for period ${request.periodo}: ${e.message}")
            throw e
        } catch (e: SocketTimeoutException) {
            logger.error("Timeout during CAEA request for period ${request.periodo}: ${e.message}")
            throw e
        } catch (ex: Exception) {
            if (ex.message?.contains("Connection reset") == true ||
                ex.message?.contains("Connection refused") == true ||
                ex.message?.contains("timeout") == true) {
                logger.error("Network error during CAEA request for period ${request.periodo}: ${ex.message}")
                throw ex
            } else {
                logger.error("Error al solicitar CAEA para período ${request.periodo}: ${ex.message}", ex)
                throw AfipWebserviceException("Error al comunicarse con AFIP: ${ex.message}", ex)
            }
        }
    }

    private fun processCAEAGetResponse(response: FECAEAGetResponse?, periodo: String): WsfeCAEAResponse {
        if (response == null) {
            throw AfipWebserviceException("No se recibió respuesta del servicio AFIP")
        }

        // Verificar errores globales
        if (response.errors?.getErr()?.isNotEmpty() == true) {
            val errors = response.errors.getErr().map { "${it.code}: ${it.msg}" }
            logger.error("Errores en respuesta AFIP para período $periodo: $errors")
            return WsfeCAEAResponse(
                caea = "ERROR",
                periodo = periodo,
                orden = 0,
                fechaDesde = "",
                fechaHasta = "",
                fechaTopeInforme = "",
                resultado = "R",
                observaciones = errors
            )
        }

        // Procesar respuesta del detalle
        val detResp = response.resultGet
        if (detResp == null) {
            throw AfipWebserviceException("No se recibió detalle de respuesta de AFIP")
        }

        val observaciones = mutableListOf<String>()

        // Agregar observaciones si existen
        detResp.observaciones?.getObs()?.forEach { obs ->
            observaciones.add("${obs.code}: ${obs.msg}")
        }

        logger.info("CAEA procesado para período $periodo: CAEA: ${detResp.caea}")

        return WsfeCAEAResponse(
            caea = detResp.caea ?: "ERROR",
            periodo = periodo,
            orden = detResp.orden.toInt(),
            fechaDesde = detResp.fchVigDesde ?: "",
            fechaHasta = detResp.fchVigHasta ?: "",
            fechaTopeInforme = detResp.fchTopeInf ?: "",
            resultado = "A", // Si llegamos aquí, fue exitoso
            observaciones = observaciones
        )
    }

    /**
     * Informa a AFIP que no hubo movimientos para un CAEA específico
     */
    fun informarCAEASinMovimiento(request: AfipCAEASinMovimientoRequest): WsfeCAEAMovimientosResponse {
        logger.info("Informando CAEA sin movimientos: ${request.caeaCode}, PV: ${request.puntoVenta}")

        return executeWithRetry {
            attemptInformarCAEASinMovimiento(request)
        }
    }

    private fun attemptInformarCAEASinMovimiento(request: AfipCAEASinMovimientoRequest): WsfeCAEAMovimientosResponse {
        val service = Service()
        val port = service.serviceSoap

        configureWebServiceClient(port, "wsfe")

        try {
            val auth = createAuthRequest(request.credentials)

            // Llamar al servicio con los parámetros correctos
            val response = port.fecaeaSinMovimientoInformar(auth, request.puntoVenta, request.caeaCode)

            return processCAEASinMovResponse(response)

        } catch (e: SocketException) {
            logger.error("Socket error during CAEA sin movimiento inform: ${e.message}")
            throw e
        } catch (ex: Exception) {
            if (isNetworkError(ex.message)) {
                logger.error("Network error during CAEA sin movimiento inform: ${ex.message}")
                throw ex
            } else {
                logger.error("Error al informar CAEA sin movimientos: ${ex.message}", ex)
                throw AfipWebserviceException("Error al comunicarse con AFIP: ${ex.message}", ex)
            }
        }
    }

    /**
     * Informa a AFIP todos los comprobantes emitidos con un CAEA específico
     */
    fun informarCAEAMovimientos(request: AfipCAEAMovimientosRequest): WsfeCAEAMovimientosResponse {
        logger.info("Informando movimientos CAEA: ${request.caeaCode}, ${request.comprobantes.size} comprobantes")

        return executeWithRetry {
            attemptInformarCAEAMovimientos(request)
        }
    }

    private fun attemptInformarCAEAMovimientos(request: AfipCAEAMovimientosRequest): WsfeCAEAMovimientosResponse {
        val service = Service()
        val port = service.serviceSoap

        configureWebServiceClient(port, "wsfe")

        try {
            val auth = createAuthRequest(request.credentials)

            // Crear solicitud de informe con movimientos
            val informeRequest = createCAEAMovimientosRequest(request)

            val response = port.fecaeaRegInformativo(auth, informeRequest)

            return processCAEAResponse(response)

        } catch (e: SocketException) {
            logger.error("Socket error during CAEA movimientos inform: ${e.message}")
            throw e
        } catch (ex: Exception) {
            if (isNetworkError(ex.message)) {
                logger.error("Network error during CAEA movimientos inform: ${ex.message}")
                throw ex
            } else {
                logger.error("Error al informar movimientos CAEA: ${ex.message}", ex)
                throw AfipWebserviceException("Error al comunicarse con AFIP: ${ex.message}", ex)
            }
        }
    }

    private fun createCAEAMovimientosRequest(request: AfipCAEAMovimientosRequest): FECAEARequest {
        val informeRequest = FECAEARequest()

        // Configurar cabecera
        val cabRequest = FECAEACabRequest()
        cabRequest.cantReg = request.comprobantes.size
        cabRequest.ptoVta = request.puntoVenta

        informeRequest.feCabReq = cabRequest

        // Configurar detalles de comprobantes
        val detRequestArray = ArrayOfFECAEADetRequest()

        request.comprobantes.forEach { comprobante ->
            val detRequest = FECAEADetRequest()

            // Propiedades heredadas de FEDetRequest
            detRequest.concepto = 1 // Productos (según especificación del usuario)
            detRequest.docTipo = 99 // Consumidor final tipo B (según especificación del usuario)
            detRequest.docNro = 0L // Consumidor final sin documento
            detRequest.cbteDesde = comprobante.numeroComprobante
            detRequest.cbteHasta = comprobante.numeroComprobante
            detRequest.cbteFch = comprobante.fechaEmision.format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"))
            detRequest.impTotal = comprobante.montoTotal.toDouble()
            detRequest.impTotConc = comprobante.montoTotalConceptos.toDouble()
            detRequest.impNeto = comprobante.montoNeto.toDouble()
            detRequest.impOpEx = comprobante.montoExento.toDouble()
            detRequest.impTrib = comprobante.montoTributos.toDouble()
            detRequest.impIVA = comprobante.montoIva.toDouble()
            detRequest.monId = "PES" // Moneda pesos
            detRequest.monCotiz = 1.0 // Cotización pesos

            // Propiedades específicas de FECAEADetRequest
            detRequest.caea = comprobante.caeaUtilizado

            detRequestArray.fecaeaDetRequest.add(detRequest)
        }

        informeRequest.feDetReq = detRequestArray

        return informeRequest
    }

    private fun processCAEASinMovResponse(response: FECAEASinMovResponse?): WsfeCAEAMovimientosResponse {
        if (response == null) {
            throw AfipWebserviceException("No se recibió respuesta del servicio AFIP")
        }

        val observaciones = mutableListOf<String>()

        // Verificar errores
        response.errors?.getErr()?.forEach { error ->
            observaciones.add("${error.code}: ${error.msg}")
        }

        val resultado = response.resultado ?: "R"
        logger.info("Informe CAEA sin movimientos procesado: $resultado")

        return WsfeCAEAMovimientosResponse(
            resultado = resultado,
            observaciones = observaciones,
            fechaProceso = response.fchProceso
        )
    }

    private fun processCAEAResponse(response: FECAEAResponse?): WsfeCAEAMovimientosResponse {
        if (response == null) {
            throw AfipWebserviceException("No se recibió respuesta del servicio AFIP")
        }

        val observaciones = mutableListOf<String>()

        // Verificar errores
        response.errors?.getErr()?.forEach { error ->
            observaciones.add("${error.code}: ${error.msg}")
        }

        val resultado = "A" // Asumimos éxito si no hay errores
        logger.info("Informe CAEA con movimientos procesado: $resultado")

        return WsfeCAEAMovimientosResponse(
            resultado = resultado,
            observaciones = observaciones,
            fechaProceso = null
        )
    }

    /**
     * Consulta un CAEA existente en AFIP
     */
    fun consultarCAEA(request: AfipCAEAConsultaRequest): WsfeCAEAConsultaResponse {
        logger.info("Consultando CAEA existente: período ${request.periodo}, orden ${request.orden}, PV ${request.puntoVenta}")

        return executeWithRetry {
            attemptConsultarCAEA(request)
        }
    }

    private fun attemptConsultarCAEA(request: AfipCAEAConsultaRequest): WsfeCAEAConsultaResponse {
        val service = Service()
        val port = service.serviceSoap

        configureWebServiceClient(port, "wsfe")

        try {
            val auth = createAuthRequest(request.credentials)

            // Llamar al servicio FECAEAConsultar
            val response = port.fecaeaConsultar(auth, request.periodo.toInt(), request.orden.toShort())

            return processCAEAConsultaResponse(response, request.periodo, request.orden)

        } catch (e: SocketException) {
            logger.error("Socket error during CAEA consulta: ${e.message}")
            throw e
        } catch (ex: Exception) {
            if (isNetworkError(ex.message)) {
                logger.error("Network error during CAEA consulta: ${ex.message}")
                throw ex
            } else {
                logger.error("Error al consultar CAEA: ${ex.message}", ex)
                throw AfipWebserviceException("Error al comunicarse con AFIP: ${ex.message}", ex)
            }
        }
    }

    private fun processCAEAConsultaResponse(response: FECAEAGetResponse?, periodo: String, orden: Int): WsfeCAEAConsultaResponse {
        if (response == null) {
            throw AfipWebserviceException("No se recibió respuesta del servicio AFIP")
        }

        // Verificar errores globales
        if (response.errors?.getErr()?.isNotEmpty() == true) {
            val errors = response.errors.getErr().map { "${it.code}: ${it.msg}" }
            logger.error("Errores en consulta CAEA para período $periodo: $errors")
            return WsfeCAEAConsultaResponse(
                encontrado = false,
                caea = null,
                periodo = periodo,
                orden = orden,
                fechaDesde = null,
                fechaHasta = null,
                fechaTopeInforme = null,
                observaciones = errors
            )
        }

        // Procesar respuesta del detalle
        val detResp = response.resultGet
        if (detResp == null) {
            return WsfeCAEAConsultaResponse(
                encontrado = false,
                caea = null,
                periodo = periodo,
                orden = orden,
                fechaDesde = null,
                fechaHasta = null,
                fechaTopeInforme = null,
                observaciones = listOf("No se encontró CAEA para el período consultado")
            )
        }

        val observaciones = mutableListOf<String>()

        // Agregar observaciones si existen
        detResp.observaciones?.getObs()?.forEach { obs ->
            observaciones.add("${obs.code}: ${obs.msg}")
        }

        logger.info("CAEA consultado para período $periodo: ${detResp.caea}")

        return WsfeCAEAConsultaResponse(
            encontrado = true,
            caea = detResp.caea,
            periodo = periodo,
            orden = orden,
            fechaDesde = detResp.fchVigDesde,
            fechaHasta = detResp.fchVigHasta,
            fechaTopeInforme = detResp.fchTopeInf,
            observaciones = observaciones
        )
    }

    private fun isNetworkError(message: String?): Boolean {
        return message?.contains("Connection reset") == true ||
               message?.contains("Connection refused") == true ||
               message?.contains("timeout") == true
    }
}

/**
 * Excepción específica para errores de webservices de AFIP
 */
class AfipWebserviceException(message: String, cause: Throwable? = null) : Exception(message, cause)
