package com.gnico.majo.infrastructure.config

import org.slf4j.LoggerFactory
import javax.net.ssl.HttpsURLConnection
import javax.net.ssl.SSLContext
import javax.net.ssl.TrustManager
import javax.net.ssl.X509TrustManager

/**
 * Configuración de red para mejorar la conectividad con servicios externos
 * Especialmente útil para resolver problemas de conexión con AFIP
 */
object NetworkConfiguration {
    
    private val logger = LoggerFactory.getLogger(NetworkConfiguration::class.java)
    
    /**
     * Inicializa la configuración de red global
     */
    fun initialize() {
        logger.info("Inicializando configuración de red...")
        
        configureSystemProperties()
        configureSSL()
        configureTimeouts()
        
        logger.info("Configuración de red completada")
    }
    
    /**
     * Configura propiedades del sistema para mejorar la conectividad
     */
    private fun configureSystemProperties() {
        // Configuración de protocolos SSL/TLS
        System.setProperty("https.protocols", "TLSv1.2,TLSv1.3")
        System.setProperty("jdk.tls.client.protocols", "TLSv1.2,TLSv1.3")
        
        // Configuración de DNS
        System.setProperty("networkaddress.cache.ttl", "60")
        System.setProperty("networkaddress.cache.negative.ttl", "10")
        
        // Configuración de conexiones HTTP
        System.setProperty("http.keepAlive", "true")
        System.setProperty("http.maxConnections", "10")
        
        // Configuración para evitar problemas con IPv6
        System.setProperty("java.net.preferIPv4Stack", "true")
        
        // Configuración de User-Agent
        System.setProperty("http.agent", "AFIP-Client/1.0")
        
        logger.debug("Propiedades del sistema configuradas")
    }
    
    /**
     * Configura SSL para evitar problemas de certificados
     */
    private fun configureSSL() {
        try {
            // Crear un TrustManager que acepta todos los certificados
            // NOTA: En producción, se debería usar un TrustManager más restrictivo
            val trustAllCerts = arrayOf<TrustManager>(object : X509TrustManager {
                override fun getAcceptedIssuers(): Array<java.security.cert.X509Certificate>? = null
                override fun checkClientTrusted(certs: Array<java.security.cert.X509Certificate>, authType: String) {}
                override fun checkServerTrusted(certs: Array<java.security.cert.X509Certificate>, authType: String) {}
            })
            
            // Instalar el TrustManager
            val sc = SSLContext.getInstance("TLS")
            sc.init(null, trustAllCerts, java.security.SecureRandom())
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.socketFactory)
            
            // Configurar hostname verifier (para desarrollo/testing)
            HttpsURLConnection.setDefaultHostnameVerifier { _, _ -> true }
            
            logger.debug("SSL configurado correctamente")
        } catch (e: Exception) {
            logger.warn("No se pudo configurar SSL: ${e.message}")
        }
    }
    
    /**
     * Configura timeouts globales
     */
    private fun configureTimeouts() {
        // Timeouts de conexión (en milisegundos)
        System.setProperty("sun.net.client.defaultConnectTimeout", "30000") // 30 segundos
        System.setProperty("sun.net.client.defaultReadTimeout", "60000")    // 60 segundos
        
        // Timeouts específicos para HTTPS
        System.setProperty("sun.net.https.client.defaultConnectTimeout", "30000")
        System.setProperty("sun.net.https.client.defaultReadTimeout", "60000")
        
        logger.debug("Timeouts configurados: connect=30s, read=60s")
    }
    
    /**
     * Verifica la conectividad con los servicios de AFIP
     */
    fun testAfipConnectivity(): ConnectivityResult {
        val results = mutableMapOf<String, Boolean>()
        
        // URLs de AFIP para testing
        val afipUrls = mapOf(
            "WSAA Homologación" to "https://wsaahomo.afip.gov.ar/ws/services/LoginCms",
            "WSFE Homologación" to "https://wswhomo.afip.gov.ar/wsfev1/service.asmx",
            "WSAA Producción" to "https://wsaa.afip.gov.ar/ws/services/LoginCms",
            "WSFE Producción" to "https://servicios1.afip.gov.ar/wsfev1/service.asmx"
        )
        
        afipUrls.forEach { (name, url) ->
            results[name] = testConnection(url)
        }
        
        return ConnectivityResult(results)
    }
    
    /**
     * Prueba la conectividad a una URL específica
     */
    private fun testConnection(url: String): Boolean {
        return try {
            val connection = java.net.URL(url).openConnection() as HttpsURLConnection
            connection.connectTimeout = 10000 // 10 segundos para test
            connection.readTimeout = 10000
            connection.requestMethod = "HEAD"
            connection.connect()
            
            val responseCode = connection.responseCode
            connection.disconnect()
            
            responseCode in 200..299 || responseCode == 405 // 405 = Method Not Allowed es OK para SOAP
        } catch (e: Exception) {
            logger.debug("Error conectando a $url: ${e.message}")
            false
        }
    }
}

/**
 * Resultado de las pruebas de conectividad
 */
data class ConnectivityResult(
    val results: Map<String, Boolean>
) {
    fun isAllSuccessful(): Boolean = results.values.all { it }
    
    fun getSuccessfulCount(): Int = results.values.count { it }
    
    fun getTotalCount(): Int = results.size
    
    fun getFailedServices(): List<String> = results.filterValues { !it }.keys.toList()
    
    fun getSummary(): String {
        val successful = getSuccessfulCount()
        val total = getTotalCount()
        return "Conectividad AFIP: $successful/$total servicios accesibles"
    }
}
