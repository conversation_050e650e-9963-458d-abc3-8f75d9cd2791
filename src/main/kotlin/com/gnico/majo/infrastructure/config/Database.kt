package com.gnico.majo.infrastructure.config

import com.gnico.majo.utils.Env
import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource
import org.jooq.SQLDialect
import org.jooq.impl.DSL

object Database {

    private lateinit var dataSource: HikariDataSource

    val dsl by lazy {
        DSL.using(dataSource, SQLDialect.POSTGRES)
    }

    fun init() {
        val config = HikariConfig().apply {
            jdbcUrl = Env.get("POSTGRES_DB_URL")
            username = Env.get("POSTGRES_USER")
            password = Env.get("POSTGRES_PASSWORD")
            maximumPoolSize = 10
            isAutoCommit = false
            transactionIsolation = "TRANSACTION_REPEATABLE_READ"
        }
        dataSource = HikariDataSource(config)
    }

    fun close() {
        dataSource.close()
    }
}