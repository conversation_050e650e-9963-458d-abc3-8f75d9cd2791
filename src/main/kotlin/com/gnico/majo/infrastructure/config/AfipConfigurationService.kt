package com.gnico.majo.infrastructure.config

import com.gnico.majo.infrastructure.afip.webservices.AfipConfiguration
import org.slf4j.LoggerFactory

/**
 * Servicio de configuración para AFIP
 * Centraliza la configuración de los webservices de AFIP
 */
class AfipConfigurationService {
    
    private val logger = LoggerFactory.getLogger(AfipConfigurationService::class.java)
    
    /**
     * Obtiene la configuración de AFIP desde variables de entorno o valores por defecto
     */
    fun getConfiguration(): AfipConfiguration {
        val cuit = System.getenv("AFIP_CUIT")?.toLongOrNull() ?: 20349249902L
        val certificatePath = System.getenv("AFIP_CERTIFICATE_PATH") ?: "afip/Certificado.p12"
        val certificatePassword = System.getenv("AFIP_CERTIFICATE_PASSWORD") ?: "gnico"
        val certificateAlias = System.getenv("AFIP_CERTIFICATE_ALIAS") ?: "1"
        val isProduction = System.getenv("AFIP_PRODUCTION")?.toBoolean() ?: false
        
        logger.info("Configuración AFIP cargada - CUIT: $cuit, Producción: $isProduction")
        
        return AfipConfiguration(
            cuit = cuit,
            certificatePath = certificatePath,
            certificatePassword = certificatePassword,
            certificateAlias = certificateAlias,
            isProduction = isProduction
        )
    }
    
    /**
     * Valida que la configuración sea correcta
     */
    fun validateConfiguration(config: AfipConfiguration): Boolean {
        return try {
            require(config.cuit > 0) { "CUIT debe ser mayor a 0" }
            require(config.certificatePath.isNotBlank()) { "Ruta del certificado no puede estar vacía" }
            require(config.certificatePassword.isNotBlank()) { "Contraseña del certificado no puede estar vacía" }
            require(config.certificateAlias.isNotBlank()) { "Alias del certificado no puede estar vacío" }
            
            // Verificar que el certificado existe
            val certificateStream = Thread.currentThread().contextClassLoader
                .getResourceAsStream(config.certificatePath)
            
            if (certificateStream == null) {
                logger.error("Certificado AFIP no encontrado en: ${config.certificatePath}")
                return false
            }
            
            certificateStream.close()
            logger.info("Configuración AFIP validada correctamente")
            true
            
        } catch (e: Exception) {
            logger.error("Error validando configuración AFIP: ${e.message}", e)
            false
        }
    }
}
