package com.gnico.majo.infrastructure.config


import com.gnico.majo.adapter.persistence.JooqProductoRepository
import com.gnico.majo.adapter.persistence.JooqSaleRepository
import com.gnico.majo.adapter.persistence.JooqSalesReportAdapter
import com.gnico.majo.adapter.persistence.JooqCategoriaRepository
import com.gnico.majo.adapter.persistence.JooqCaeaRepository
import com.gnico.majo.adapter.persistence.JooqUsuarioRepository
import com.gnico.majo.adapter.persistence.JooqComprobanteNumeracionRepository


import com.gnico.majo.adapter.printer.EscPosPrinterAdapter
import com.gnico.majo.adapter.afip.AfipServiceAdapter
import com.gnico.majo.adapter.persistence.JooqWsaaCredentialsRepository
import com.gnico.majo.adapter.controller.rest.ProductoController
import com.gnico.majo.adapter.controller.rest.SaleController
import com.gnico.majo.adapter.controller.rest.SaleCancellationController

import com.gnico.majo.adapter.controller.rest.UsuarioController
import com.gnico.majo.adapter.controller.rest.ComprobanteController
import com.gnico.majo.adapter.controller.rest.PrintController
import com.gnico.majo.adapter.controller.rest.CategoriaController
import com.gnico.majo.adapter.controller.rest.ReportController
import com.gnico.majo.adapter.controller.rest.CaeaController
import com.gnico.majo.infrastructure.automation.CAEAStartupService
import com.gnico.majo.application.port.`in`.ProductoService
import com.gnico.majo.application.port.`in`.SaleService
import com.gnico.majo.application.port.`in`.SaleCancellationService

import com.gnico.majo.application.port.`in`.UsuarioService
import com.gnico.majo.application.port.`in`.ComprobanteService
import com.gnico.majo.application.port.`in`.PrintService
import com.gnico.majo.application.port.`in`.CategoriaService
import com.gnico.majo.application.port.`in`.ReportService
import com.gnico.majo.application.port.out.PrinterPort
import com.gnico.majo.application.port.out.AfipService
import com.gnico.majo.application.port.out.SalesReportPort
import com.gnico.majo.application.port.out.ProductoRepositoryPort
import com.gnico.majo.application.port.out.SaleRepositoryPort
import com.gnico.majo.application.port.out.WsaaCredentialsRepository
import com.gnico.majo.application.port.out.CaeaRepositoryPort
import com.gnico.majo.application.port.out.CategoriaRepositoryPort
import com.gnico.majo.application.port.out.ComprobanteNumeracionRepositoryPort

import com.gnico.majo.application.port.out.UsuarioRepository


import com.gnico.majo.application.usecase.ProductoServiceImpl
import com.gnico.majo.application.usecase.SaleServiceImpl
import com.gnico.majo.application.usecase.SaleCancellationServiceImpl

import com.gnico.majo.application.usecase.UsuarioServiceImpl
import com.gnico.majo.application.usecase.ComprobanteServiceImpl
import com.gnico.majo.application.usecase.PrintServiceImpl
import com.gnico.majo.application.usecase.CategoriaServiceImpl
import com.gnico.majo.application.usecase.ReportServiceImpl
import com.gnico.majo.infrastructure.afip.WsaaCredentialsService
import com.gnico.majo.infrastructure.afip.webservices.WsaaClientImpl
import com.gnico.majo.infrastructure.config.AfipConfigurationService
import com.gnico.majo.infrastructure.startup.AfipStartupService
import com.gnico.majo.infrastructure.startup.ApplicationStartup
import org.koin.dsl.module


/**
 * Main application module for dependency injection
 */
val appModule = module {
    // Repositories
    single<SaleRepositoryPort> { JooqSaleRepository() }
    single<UsuarioRepository> { JooqUsuarioRepository() }
    single<CategoriaRepositoryPort> { JooqCategoriaRepository() }
    single<CaeaRepositoryPort> { JooqCaeaRepository() }
    single<SalesReportPort> { JooqSalesReportAdapter() }
    single<ProductoRepositoryPort> { JooqProductoRepository() }


    single<WsaaCredentialsRepository> { JooqWsaaCredentialsRepository() }
    single<ComprobanteNumeracionRepositoryPort> { JooqComprobanteNumeracionRepository() }

    // AFIP Configuration and Services
    single<AfipConfigurationService> { AfipConfigurationService() }
    single<WsaaClientImpl> { WsaaClientImpl(get<AfipConfigurationService>().getConfiguration()) }
    single<WsaaCredentialsService> {
        WsaaCredentialsService(
            credentialsRepository = get(),
            wsaaClient = get(),
            configuration = get<AfipConfigurationService>().getConfiguration()
        )
    }
    single<AfipStartupService> { AfipStartupService(get()) }
    single<ApplicationStartup> { ApplicationStartup() }

    // Services
    single<PrinterPort> { EscPosPrinterAdapter() }
    single<AfipService> { AfipServiceAdapter(get(), get()) }
    single<SaleCancellationService> {
        SaleCancellationServiceImpl(
            saleRepository = get(),
            usuarioRepository = get(),
            comprobanteService = get()
        )
    }
    single<SaleService> {
        SaleServiceImpl(
            saleRepository = get(),
            usuarioRepository = get(),
            comprobanteService = get(),
            printService = get(),
            saleCancellationService = get()
        )
    }
    single<ProductoService> { ProductoServiceImpl(productoRepository = get()) }

    single<UsuarioService> {
        UsuarioServiceImpl(
            usuarioRepository = get(),
            saleRepository = get()
        )
    }
    single<CategoriaService> { CategoriaServiceImpl(categoriaRepository = get()) }
    single<ComprobanteService> {
        ComprobanteServiceImpl(
            saleRepository = get(),
            afipService = get(),
            numeracionRepository = get()
        )
    }
    single<PrintService> {
        PrintServiceImpl(
            saleRepository = get(),
            printer = get(),
            reportService = get()
        )
    }
    single<ReportService> {
        ReportServiceImpl(
            salesReportPort = get()
        )
    }

    // Controllers
    single { SaleController(saleService = get()) }
    single { SaleCancellationController(saleService = get(), saleCancellationService = get()) }
    single { ProductoController(productoService = get()) }

    single { UsuarioController(usuarioService = get()) }
    single { ComprobanteController(comprobanteService = get()) }
    single { PrintController(printService = get()) }
    single { CategoriaController(categoriaService = get()) }
    single { ReportController(reportService = get()) }
    single { CaeaController(afipService = get(), caeaRepository = get()) }

    // Automation Services
    single { CAEAStartupService(afipService = get<AfipService>(), caeaRepository = get()) }
}
