package com.gnico.majo.infrastructure.config

import com.gnico.majo.utils.Env
import org.slf4j.LoggerFactory

/**
 * Configuración para impresora térmica y datos de la empresa
 */
data class PrinterConfiguration(
    val printerIp: String,
    val printerPort: Int,
    val companyName: String,
    val companyCUIT: String,
    val companyIIBB: String,
    val companyStart: String,
    val companyAddress: String,
    val companyPhone: String,
    val companyEmail: String,
    val companyWebsite: String,
    val cutDelayMs: Long = 500L, // Delay antes del corte en milisegundos
    val feedLinesBeforeCut: Int = 4, // Líneas de alimentación antes del corte
    val enabled: Boolean = true // Habilitar/deshabilitar impresión
) {
    companion object {
        private val logger = LoggerFactory.getLogger(PrinterConfiguration::class.java)
        
        /**
         * Carga la configuración desde variables de entorno
         */
        fun fromEnvironment(): PrinterConfiguration {
            return try {
                val enabled = Env.getOrDefault("PRINTER_ENABLED", "true").toBoolean()

                PrinterConfiguration(
                    printerIp = Env.getOrDefault("PRINTER_IP", "*************"),
                    printerPort = Env.getOrDefault("PRINTER_PORT", "9100").toInt(),
                    companyName = Env.getOrDefault("COMPANY_NAME", "Mi Empresa"),
                    companyCUIT = Env.getOrDefault("COMPANY_CUIT", "20*********"),
                    companyIIBB = Env.getOrDefault("COMPANY_IIBB", "*********"),
                    companyStart = Env.getOrDefault("COMPANY_START", "01/01/2020"),
                    companyAddress = Env.getOrDefault("COMPANY_ADDRESS", "Dirección no configurada"),
                    companyPhone = Env.getOrDefault("COMPANY_PHONE", "Teléfono no configurado"),
                    companyEmail = Env.getOrDefault("COMPANY_EMAIL", "<EMAIL>"),
                    companyWebsite = Env.getOrDefault("COMPANY_WEBSITE", "www.empresa.com"),
                    cutDelayMs = Env.getOrDefault("PRINTER_CUT_DELAY_MS", "500").toLong(),
                    feedLinesBeforeCut = Env.getOrDefault("PRINTER_FEED_LINES_BEFORE_CUT", "4").toInt(),
                    enabled = enabled
                ).also {
                    if (enabled) {
                        logger.info("Configuración de impresora cargada - IP: ${it.printerIp}:${it.printerPort}")
                    } else {
                        logger.info("Impresión deshabilitada por configuración (PRINTER_ENABLED=false)")
                    }
                }
            } catch (e: Exception) {
                logger.error("Error al cargar configuración de impresora: ${e.message}")
                // En caso de error, crear configuración con impresión deshabilitada
                logger.warn("Creando configuración de impresora con valores por defecto y impresión deshabilitada")
                PrinterConfiguration(
                    printerIp = "*************",
                    printerPort = 9100,
                    companyName = "Mi Empresa",
                    companyCUIT = "20*********",
                    companyIIBB = "*********",
                    companyStart = "01/01/2020",
                    companyAddress = "Dirección no configurada",
                    companyPhone = "Teléfono no configurado",
                    companyEmail = "<EMAIL>",
                    companyWebsite = "www.empresa.com",
                    enabled = false
                )
            }
        }
    }
    
    /**
     * Valida que la configuración sea correcta
     */
    fun validate(): Boolean {
        // Si la impresión está deshabilitada, la configuración es válida
        if (!enabled) return true

        return printerIp.isNotBlank() &&
               printerPort > 0 &&
               companyName.isNotBlank() &&
               companyCUIT.isNotBlank()
    }
}
