package com.gnico.majo.infrastructure.startup

import com.gnico.majo.infrastructure.afip.WsaaCredentialsService
import com.gnico.majo.infrastructure.afip.ValidationResult
import org.slf4j.LoggerFactory
import kotlinx.coroutines.runBlocking

/**
 * Servicio de inicialización para AFIP
 * Valida y renueva credenciales al iniciar el programa
 */
class AfipStartupService(
    private val wsaaCredentialsService: WsaaCredentialsService
) {
    
    private val logger = LoggerFactory.getLogger(AfipStartupService::class.java)
    
    /**
     * Inicializa y valida las credenciales AFIP al arrancar el programa
     */
    fun initializeAfipCredentials(): ValidationResult {
        logger.info("=== Iniciando validación de credenciales AFIP ===")
        
        return runBlocking {
            try {
                val result = wsaaCredentialsService.validateAllCredentialsOnStartup()
                
                logValidationResult(result)
                
                if (result.isAllValid()) {
                    logger.info("✅ Todas las credenciales AFIP están válidas y listas para usar")
                } else {
                    logger.warn("⚠️  Algunas credenciales AFIP no están disponibles. Tasa de éxito: ${(result.getSuccessRate() * 100).toInt()}%")
                    
                    if (result.validServices == 0) {
                        logger.error("❌ No hay credenciales AFIP válidas disponibles. El sistema funcionará en modo limitado.")
                    }
                }
                
                result
                
            } catch (e: Exception) {
                logger.error("❌ Error crítico durante inicialización de credenciales AFIP: ${e.message}", e)
                
                // Devolver resultado de fallo
                ValidationResult(
                    totalServices = 1,
                    validServices = 0,
                    renewedCredentials = 0,
                    cleanedCredentials = 0,
                    serviceResults = mapOf("wsfe" to false)
                )
            }
        }
    }
    
    /**
     * Verifica periódicamente el estado de las credenciales
     */
    fun checkCredentialsHealth(): CredentialsHealthStatus {
        logger.debug("Verificando estado de salud de credenciales AFIP")
        
        return try {
            val stats = wsaaCredentialsService.getCredentialsStats()
            val hasValidWsfe = wsaaCredentialsService.hasValidCredentials("wsfe")
            
            val status = when {
                !hasValidWsfe -> CredentialsHealthStatus.CRITICAL
                stats.nearExpiryCount > 0 -> CredentialsHealthStatus.WARNING
                else -> CredentialsHealthStatus.HEALTHY
            }
            
            logger.debug("Estado de credenciales: $status (próximas a vencer: ${stats.nearExpiryCount}, expiradas: ${stats.expiredCount})")
            
            CredentialsHealthStatus(
                status = status.status,
                hasValidWsfe = hasValidWsfe,
                nearExpiryCount = stats.nearExpiryCount,
                expiredCount = stats.expiredCount,
                services = stats.services
            )
            
        } catch (e: Exception) {
            logger.error("Error al verificar estado de credenciales: ${e.message}", e)
            CredentialsHealthStatus.CRITICAL
        }
    }
    
    /**
     * Fuerza la renovación de credenciales si es necesario
     */
    fun forceCredentialsRenewal(): Boolean {
        logger.info("Forzando renovación de credenciales AFIP")
        
        return try {
            val credentials = wsaaCredentialsService.renewCredentials("wsfe")
            val success = credentials != null
            
            if (success) {
                logger.info("✅ Credenciales renovadas exitosamente")
            } else {
                logger.error("❌ No se pudieron renovar las credenciales")
            }
            
            success
            
        } catch (e: Exception) {
            logger.error("Error al renovar credenciales: ${e.message}", e)
            false
        }
    }
    
    private fun logValidationResult(result: ValidationResult) {
        logger.info("--- Resultado de validación de credenciales AFIP ---")
        logger.info("Servicios totales: ${result.totalServices}")
        logger.info("Servicios válidos: ${result.validServices}")
        logger.info("Credenciales renovadas: ${result.renewedCredentials}")
        logger.info("Credenciales limpiadas: ${result.cleanedCredentials}")
        logger.info("Tasa de éxito: ${(result.getSuccessRate() * 100).toInt()}%")
        
        result.serviceResults.forEach { (service, isValid) ->
            val status = if (isValid) "✅ VÁLIDO" else "❌ INVÁLIDO"
            logger.info("  - $service: $status")
        }
        
        logger.info("--- Fin de validación ---")
    }
}

/**
 * Estado de salud de las credenciales
 */
data class CredentialsHealthStatus(
    val status: String,
    val hasValidWsfe: Boolean,
    val nearExpiryCount: Int,
    val expiredCount: Int,
    val services: List<String>
) {
    companion object {
        val HEALTHY = CredentialsHealthStatus("HEALTHY", true, 0, 0, emptyList())
        val WARNING = CredentialsHealthStatus("WARNING", true, 1, 0, emptyList())
        val CRITICAL = CredentialsHealthStatus("CRITICAL", false, 0, 0, emptyList())
    }
    
    fun isHealthy(): Boolean = status == "HEALTHY"
    fun isWarning(): Boolean = status == "WARNING"
    fun isCritical(): Boolean = status == "CRITICAL"
}
