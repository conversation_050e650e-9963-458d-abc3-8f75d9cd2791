package com.gnico.majo.infrastructure.automation

import java.time.LocalDate
import java.time.YearMonth

/**
 * Utilidad para cálculos de períodos CAEA
 */
object CAEAPeriodCalculator {

    /**
     * Información de un período CAEA
     */
    data class PeriodoCAEA(
        val periodo: String,      // YYYYMM
        val orden: Int,           // 1 o 2
        val fechaInicio: LocalDate,
        val fechaFin: LocalDate,
        val fechaLimiteInforme: LocalDate  // 8 días después del fin
    ) {
        fun estaVigente(fecha: LocalDate = LocalDate.now()): Boolean {
            return fecha in fechaInicio..fechaFin
        }
        
        fun estaVencido(fecha: LocalDate = LocalDate.now()): Boolean {
            return fecha > fechaFin
        }
        
        fun puedeInformarse(fecha: LocalDate = LocalDate.now()): Boolean {
            return estaVencido(fecha) && fecha <= fechaLimiteInforme
        }
        
        fun yaNoSePuedeInformar(fecha: LocalDate = LocalDate.now()): Boolean {
            return fecha > fechaLimiteInforme
        }
    }

    /**
     * Obtiene el período CAEA actual para una fecha dada
     */
    fun getPeriodoActual(fecha: LocalDate = LocalDate.now()): PeriodoCAEA {
        val yearMonth = YearMonth.from(fecha)
        val dia = fecha.dayOfMonth
        
        return if (dia <= 15) {
            // Orden 1: del 1 al 15
            createPeriodo(yearMonth, 1)
        } else {
            // Orden 2: del 16 al último día del mes
            createPeriodo(yearMonth, 2)
        }
    }

    /**
     * Obtiene el próximo período CAEA después de una fecha dada
     */
    fun getProximoPeriodo(fecha: LocalDate = LocalDate.now()): PeriodoCAEA {
        val periodoActual = getPeriodoActual(fecha)
        
        return if (periodoActual.orden == 1) {
            // Si estamos en orden 1, el próximo es orden 2 del mismo mes
            createPeriodo(YearMonth.from(fecha), 2)
        } else {
            // Si estamos en orden 2, el próximo es orden 1 del mes siguiente
            val siguienteMes = YearMonth.from(fecha).plusMonths(1)
            createPeriodo(siguienteMes, 1)
        }
    }

    /**
     * Verifica si se puede solicitar CAEA anticipado para el próximo período
     * Se puede solicitar hasta 4 días antes del inicio del próximo período
     */
    fun puedeSolicitarProximoPeriodo(fecha: LocalDate = LocalDate.now()): Boolean {
        val proximoPeriodo = getProximoPeriodo(fecha)
        val fechaLimiteSolicitud = proximoPeriodo.fechaInicio.minusDays(4)
        
        return fecha >= fechaLimiteSolicitud
    }

    /**
     * Obtiene todos los períodos CAEA vencidos que pueden ser informados
     */
    fun getPeriodosVencidosParaInformar(fecha: LocalDate = LocalDate.now()): List<PeriodoCAEA> {
        val periodos = mutableListOf<PeriodoCAEA>()
        
        // Verificar los últimos 3 meses para encontrar períodos vencidos
        for (i in 0..2) {
            val mes = YearMonth.from(fecha).minusMonths(i.toLong())
            
            val periodo1 = createPeriodo(mes, 1)
            val periodo2 = createPeriodo(mes, 2)
            
            if (periodo1.puedeInformarse(fecha)) {
                periodos.add(periodo1)
            }
            
            if (periodo2.puedeInformarse(fecha)) {
                periodos.add(periodo2)
            }
        }
        
        return periodos.sortedBy { it.fechaFin }
    }

    /**
     * Obtiene períodos CAEA que ya no pueden ser informados (pasaron los 8 días)
     * NOTA: Este método devuelve períodos teóricos, no CAEAs reales.
     * Para verificar CAEAs reales, usar el repositorio directamente.
     */
    fun getPeriodosVencidosSinInformar(fecha: LocalDate = LocalDate.now()): List<PeriodoCAEA> {
        val periodos = mutableListOf<PeriodoCAEA>()

        // Verificar los últimos 6 meses para encontrar períodos no informados
        for (i in 0..5) {
            val mes = YearMonth.from(fecha).minusMonths(i.toLong())

            val periodo1 = createPeriodo(mes, 1)
            val periodo2 = createPeriodo(mes, 2)

            if (periodo1.yaNoSePuedeInformar(fecha)) {
                periodos.add(periodo1)
            }

            if (periodo2.yaNoSePuedeInformar(fecha)) {
                periodos.add(periodo2)
            }
        }

        return periodos.sortedBy { it.fechaFin }
    }

    /**
     * Convierte período y orden a string de período AFIP (YYYYMM)
     */
    fun toPeriodoString(yearMonth: YearMonth): String {
        return yearMonth.toString().replace("-", "")
    }

    /**
     * Crea un período CAEA desde un período string (YYYYMM) y orden
     */
    fun createPeriodoFromCAEA(periodoString: String, orden: Int): PeriodoCAEA {
        val year = periodoString.substring(0, 4).toInt()
        val month = periodoString.substring(4, 6).toInt()
        val yearMonth = YearMonth.of(year, month)
        return createPeriodo(yearMonth, orden)
    }

    /**
     * Crea un período CAEA para un mes y orden específicos
     */
    private fun createPeriodo(yearMonth: YearMonth, orden: Int): PeriodoCAEA {
        val fechaInicio = if (orden == 1) {
            yearMonth.atDay(1)
        } else {
            yearMonth.atDay(16)
        }
        
        val fechaFin = if (orden == 1) {
            yearMonth.atDay(15)
        } else {
            yearMonth.atEndOfMonth()
        }
        
        val fechaLimiteInforme = fechaFin.plusDays(8)
        
        return PeriodoCAEA(
            periodo = toPeriodoString(yearMonth),
            orden = orden,
            fechaInicio = fechaInicio,
            fechaFin = fechaFin,
            fechaLimiteInforme = fechaLimiteInforme
        )
    }

    /**
     * Obtiene información detallada del estado de períodos para debugging
     * NOTA: Este método muestra períodos teóricos, no CAEAs reales en BD
     */
    fun getEstadoPeriodos(fecha: LocalDate = LocalDate.now()): String {
        val actual = getPeriodoActual(fecha)
        val proximo = getProximoPeriodo(fecha)
        val puedeProximo = puedeSolicitarProximoPeriodo(fecha)
        val vencidosInformar = getPeriodosVencidosParaInformar(fecha)

        return buildString {
            appendLine("=== Estado Períodos CAEA para $fecha ===")
            appendLine("Período Actual: ${actual.periodo}-${actual.orden} (${actual.fechaInicio} al ${actual.fechaFin})")
            appendLine("Próximo Período: ${proximo.periodo}-${proximo.orden} (${proximo.fechaInicio} al ${proximo.fechaFin})")
            appendLine("Puede solicitar próximo: $puedeProximo")
            appendLine("Períodos vencidos para informar: ${vencidosInformar.size}")
            vencidosInformar.forEach { p ->
                appendLine("  - ${p.periodo}-${p.orden} (límite informe: ${p.fechaLimiteInforme})")
            }
            appendLine("NOTA: Para ver CAEAs reales, consultar directamente la base de datos")
        }
    }
}
