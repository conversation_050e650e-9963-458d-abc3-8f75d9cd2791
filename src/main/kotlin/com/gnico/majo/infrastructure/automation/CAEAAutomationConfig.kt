package com.gnico.majo.infrastructure.automation

/**
 * Configuración para la automatización de CAEA
 */
data class CAEAAutomationConfig(
    val enabled: Boolean = true,
    val puntosVentaOffline: List<Int> = listOf(2), // Puntos de venta que usan CAEA
    val solicitarAmbosOrdenes: Boolean = false,    // Solicitar orden 1 y 2 cuando sea posible (deshabilitado por defecto)
    val informarAutomaticamente: Boolean = true,   // Informar CAEAs vencidos automáticamente
    val alertarCAEAsVencidos: Boolean = false,     // Alertar sobre CAEAs que ya no se pueden informar (deshabilitado por defecto)
    val logDetallado: Boolean = true               // Log detallado de operaciones
) {
    
    /**
     * Verifica si un punto de venta debe ser automatizado
     */
    fun debeAutomatizar(puntoVenta: Int): Boolean {
        return enabled && puntoVenta in puntosVentaOffline
    }
    
    /**
     * Obtiene la configuración desde variables de entorno o valores por defecto
     */
    companion object {
        fun fromEnvironment(): CAEAAutomationConfig {
            val enabled = System.getenv("CAEA_AUTOMATION_ENABLED")?.toBoolean() ?: true
            
            val puntosVenta = System.getenv("CAEA_PUNTOS_VENTA_OFFLINE")
                ?.split(",")
                ?.mapNotNull { it.trim().toIntOrNull() }
                ?: listOf(2)
            
            val solicitarAmbos = System.getenv("CAEA_SOLICITAR_AMBOS_ORDENES")?.toBoolean() ?: false
            val informarAuto = System.getenv("CAEA_INFORMAR_AUTOMATICAMENTE")?.toBoolean() ?: true
            val alertarVencidos = System.getenv("CAEA_ALERTAR_VENCIDOS")?.toBoolean() ?: false
            val logDetallado = System.getenv("CAEA_LOG_DETALLADO")?.toBoolean() ?: true
            
            return CAEAAutomationConfig(
                enabled = enabled,
                puntosVentaOffline = puntosVenta,
                solicitarAmbosOrdenes = solicitarAmbos,
                informarAutomaticamente = informarAuto,
                alertarCAEAsVencidos = alertarVencidos,
                logDetallado = logDetallado
            )
        }
    }
}
