package com.gnico.majo.infrastructure.automation

import com.gnico.majo.application.domain.model.CaeaCode
import com.gnico.majo.application.port.out.AfipService
import com.gnico.majo.application.port.out.CaeaRepositoryPort
import com.gnico.majo.infrastructure.automation.CAEAPeriodCalculator.PeriodoCAEA
import org.slf4j.LoggerFactory
import java.time.LocalDate

/**
 * Servicio de automatización de CAEA que se ejecuta al iniciar la aplicación
 */
class CAEAStartupService(
    private val afipService: AfipService,
    private val caeaRepository: CaeaRepositoryPort,
    private val config: CAEAAutomationConfig = CAEAAutomationConfig.fromEnvironment()
) {
    
    private val logger = LoggerFactory.getLogger(CAEAStartupService::class.java)

    /**
     * Ejecuta la automatización completa de CAEA al startup
     */
    suspend fun ejecutarAutomatizacion() {
        if (!config.enabled) {
            logger.info("Automatización CAEA deshabilitada")
            return
        }

        logger.info("=== Iniciando Automatización CAEA ===")
        
        if (config.logDetallado) {
            logger.info(CAEAPeriodCalculator.getEstadoPeriodos())
            mostrarEstadoCAEAsReales()
        }

        // Actualizar estados de CAEAs vencidos automáticamente
        actualizarEstadosVencidos()

        var operacionesRealizadas = 0

        for (puntoVenta in config.puntosVentaOffline) {
            logger.info("Procesando automatización para punto de venta $puntoVenta")
            
            try {
                operacionesRealizadas += procesarPuntoVenta(puntoVenta)
            } catch (e: Exception) {
                logger.error("Error al procesar automatización para PV $puntoVenta: ${e.message}", e)
            }
        }

        logger.info("=== Automatización CAEA completada: $operacionesRealizadas operaciones realizadas ===")
    }

    /**
     * Procesa automatización para un punto de venta específico
     */
    private suspend fun procesarPuntoVenta(puntoVenta: Int): Int {
        var operaciones = 0
        
        // 1. Verificar y solicitar CAEA para período actual
        operaciones += verificarCAEAPeriodoActual(puntoVenta)
        
        // 2. Verificar y solicitar CAEA para próximo período (si es posible)
        operaciones += verificarCAEAProximoPeriodo(puntoVenta)
        
        // 3. Informar CAEAs vencidos pendientes
        if (config.informarAutomaticamente) {
            operaciones += informarCAEAsVencidos(puntoVenta)
        }
        
        // 4. Alertar sobre CAEAs que ya no se pueden informar (si está habilitado)
        if (config.alertarCAEAsVencidos) {
            operaciones += alertarCAEAsNoInformables(puntoVenta)
        }
        
        return operaciones
    }

    /**
     * Verifica si existe CAEA para el período actual y lo solicita si es necesario
     */
    private suspend fun verificarCAEAPeriodoActual(puntoVenta: Int): Int {
        val periodoActual = CAEAPeriodCalculator.getPeriodoActual()
        
        logger.info("Verificando CAEA período actual: ${periodoActual.periodo}-${periodoActual.orden} para PV $puntoVenta")
        
        val caeaExistente = buscarCAEAExistente(puntoVenta, periodoActual.periodo, periodoActual.orden)
        
        if (caeaExistente != null) {
            logger.info("CAEA ya existe para período actual ${periodoActual.periodo}-${periodoActual.orden}: ${caeaExistente.caea}")
            return 0
        }
        
        // Antes de solicitar, consultar si existe en AFIP
        logger.info("CAEA no encontrado en BD local. Consultando en AFIP...")
        val caeaEnAfip = consultarYSincronizarCAEA(puntoVenta, periodoActual.periodo, periodoActual.orden)

        if (caeaEnAfip != null) {
            logger.info("✅ CAEA sincronizado desde AFIP: ${caeaEnAfip.caea}")
            return 1 // Operación de sincronización
        }

        // Si no existe ni en BD ni en AFIP, solicitar nuevo CAEA
        logger.info("Solicitando CAEA para período actual ${periodoActual.periodo}-${periodoActual.orden}")

        return solicitarCAEA(puntoVenta, periodoActual.periodo, periodoActual.orden)
    }

    /**
     * Verifica si se puede y debe solicitar CAEA para el próximo período
     */
    private suspend fun verificarCAEAProximoPeriodo(puntoVenta: Int): Int {
        if (!CAEAPeriodCalculator.puedeSolicitarProximoPeriodo()) {
            logger.debug("Aún no se puede solicitar CAEA para el próximo período")
            return 0
        }
        
        val proximoPeriodo = CAEAPeriodCalculator.getProximoPeriodo()
        
        logger.info("Verificando CAEA próximo período: ${proximoPeriodo.periodo}-${proximoPeriodo.orden} para PV $puntoVenta")
        
        val caeaExistente = buscarCAEAExistente(puntoVenta, proximoPeriodo.periodo, proximoPeriodo.orden)
        
        if (caeaExistente != null) {
            logger.info("CAEA ya existe para próximo período ${proximoPeriodo.periodo}-${proximoPeriodo.orden}: ${caeaExistente.caea}")
            return 0
        }
        
        // Antes de solicitar, consultar si existe en AFIP
        logger.info("CAEA próximo período no encontrado en BD local. Consultando en AFIP...")
        val caeaEnAfip = consultarYSincronizarCAEA(puntoVenta, proximoPeriodo.periodo, proximoPeriodo.orden)

        if (caeaEnAfip != null) {
            logger.info("✅ CAEA próximo período sincronizado desde AFIP: ${caeaEnAfip.caea}")
            return 1 // Operación de sincronización
        }

        // Si no existe ni en BD ni en AFIP, solicitar nuevo CAEA
        logger.info("Solicitando CAEA anticipado para próximo período ${proximoPeriodo.periodo}-${proximoPeriodo.orden}")

        return solicitarCAEA(puntoVenta, proximoPeriodo.periodo, proximoPeriodo.orden)
    }

    /**
     * Informa CAEAs vencidos que están dentro del plazo de 8 días
     */
    private suspend fun informarCAEAsVencidos(puntoVenta: Int): Int {
        val periodosVencidos = CAEAPeriodCalculator.getPeriodosVencidosParaInformar()
        var operaciones = 0
        
        for (periodo in periodosVencidos) {
            val caeaExistente = buscarCAEAExistente(puntoVenta, periodo.periodo, periodo.orden)
            
            if (caeaExistente != null && !caeaRepository.isInformado(caeaExistente.caea)) {
                logger.info("Informando CAEA vencido: ${caeaExistente.caea} (período ${periodo.periodo}-${periodo.orden})")
                
                try {
                    val response = afipService.informarMovimientosCAEAAutomatico(caeaExistente.caea, puntoVenta)
                    
                    if (response.isApproved()) {
                        logger.info("CAEA ${caeaExistente.caea} informado exitosamente")
                        operaciones++
                    } else {
                        logger.error("Error al informar CAEA ${caeaExistente.caea}: ${response.observaciones}")
                    }
                } catch (e: Exception) {
                    logger.error("Excepción al informar CAEA ${caeaExistente.caea}: ${e.message}", e)
                }
            }
        }
        
        return operaciones
    }

    /**
     * Alerta sobre CAEAs que ya no pueden ser informados (pasaron los 8 días)
     * Solo alerta sobre CAEAs que realmente existen en la base de datos y son recientes
     */
    private suspend fun alertarCAEAsNoInformables(puntoVenta: Int): Int {
        return try {
            // Obtener todos los CAEAs existentes para este punto de venta
            val caeaExistentes = caeaRepository.findByPuntoVenta(puntoVenta)
            logger.debug("Encontrados ${caeaExistentes.size} CAEAs en BD para PV $puntoVenta")

            var alertas = 0
            val fechaLimite = LocalDate.now().minusMonths(3) // Solo alertar sobre CAEAs de últimos 3 meses

            for (caea in caeaExistentes) {
                logger.debug("Evaluando CAEA: ${caea.caea} (período ${caea.periodo}-${caea.orden})")

                // Verificar si este CAEA es reciente (últimos 3 meses)
                val fechaFinPeriodo = try {
                    val periodo = CAEAPeriodCalculator.createPeriodoFromCAEA(caea.periodo, caea.orden)
                    periodo.fechaFin
                } catch (e: Exception) {
                    logger.warn("Error al parsear período ${caea.periodo}: ${e.message}")
                    continue
                }

                // Solo procesar CAEAs recientes
                if (fechaFinPeriodo.isBefore(fechaLimite)) {
                    logger.debug("CAEA ${caea.caea} es muy antiguo (${fechaFinPeriodo}), omitiendo")
                    continue
                }

                // Verificar si este CAEA ya no puede ser informado
                val periodo = CAEAPeriodCalculator.createPeriodoFromCAEA(caea.periodo, caea.orden)
                val yaNoSePuedeInformar = periodo.yaNoSePuedeInformar()
                val yaInformado = caeaRepository.isInformado(caea.caea)

                logger.debug("CAEA ${caea.caea}: yaNoSePuedeInformar=$yaNoSePuedeInformar, yaInformado=$yaInformado")

                if (yaNoSePuedeInformar && !yaInformado) {
                    logger.warn("⚠️ ALERTA: CAEA ${caea.caea} (período ${caea.periodo}-${caea.orden}) " +
                               "ya no puede ser informado. Límite era: ${periodo.fechaLimiteInforme}")
                    alertas++
                }
            }

            if (alertas == 0) {
                logger.debug("No hay CAEAs pendientes de informar fuera de plazo")
            }

            return alertas

        } catch (e: Exception) {
            logger.error("Error al verificar CAEAs no informables: ${e.message}", e)
            0
        }
    }

    /**
     * Busca un CAEA existente para un punto de venta, período y orden específicos
     */
    private fun buscarCAEAExistente(puntoVenta: Int, periodo: String, orden: Int): com.gnico.majo.application.domain.model.CaeaCode? {
        return try {
            logger.debug("Buscando CAEA en BD: PV=$puntoVenta, período=$periodo, orden=$orden")
            val caea = caeaRepository.findByPuntoVentaPeriodoOrden(puntoVenta, periodo, orden)

            if (caea != null) {
                logger.info("✅ CAEA encontrado en BD: ${caea.caea} para PV $puntoVenta, período $periodo-$orden")
            } else {
                logger.info("❌ No se encontró CAEA en BD para PV $puntoVenta, período $periodo-$orden")
            }

            caea
        } catch (e: Exception) {
            logger.error("Error al buscar CAEA en BD para PV $puntoVenta, período $periodo, orden $orden: ${e.message}", e)
            null
        }
    }

    /**
     * Solicita un CAEA a AFIP
     */
    private suspend fun solicitarCAEA(puntoVenta: Int, periodo: String, orden: Int): Int {
        return try {
            val response = afipService.solicitarCAEA(puntoVenta, periodo, orden)

            if (response.isApproved()) {
                logger.info("✅ CAEA solicitado exitosamente: ${response.cae} (período $periodo-$orden)")
                1
            } else {
                // Verificar si el error es porque ya existe el CAEA
                val yaExiste = response.observaciones.any {
                    it.contains("15008") || it.contains("Existe un CAEA otorgado")
                }

                if (yaExiste) {
                    logger.warn("⚠️ CAEA ya existe en AFIP para período $periodo-$orden. No se requiere nueva solicitud.")
                    return 0 // No es un error, simplemente ya existe
                }

                logger.error("❌ Error al solicitar CAEA para período $periodo-$orden: ${response.observaciones}")
                0
            }
        } catch (e: Exception) {
            logger.error("❌ Excepción al solicitar CAEA para período $periodo-$orden: ${e.message}", e)
            0
        }
    }

    /**
     * Consulta un CAEA en AFIP y lo sincroniza con la BD local si existe
     */
    private suspend fun consultarYSincronizarCAEA(puntoVenta: Int, periodo: String, orden: Int): CaeaCode? {
        return try {
            logger.debug("Consultando CAEA en AFIP: PV=$puntoVenta, período=$periodo, orden=$orden")

            // Consultar CAEA en AFIP
            val caeaEnAfip = afipService.consultarCAEA(puntoVenta, periodo, orden)

            if (caeaEnAfip != null) {
                logger.info("✅ CAEA encontrado en AFIP: ${caeaEnAfip.caea}. Sincronizando con BD local...")

                try {
                    // Guardar en BD local
                    caeaRepository.save(caeaEnAfip)
                    logger.info("CAEA sincronizado exitosamente en BD local")
                    return caeaEnAfip
                } catch (e: Exception) {
                    logger.error("Error al guardar CAEA sincronizado en BD: ${e.message}", e)
                    // Retornar el CAEA aunque no se pudo guardar
                    return caeaEnAfip
                }
            } else {
                logger.debug("No se encontró CAEA en AFIP para PV $puntoVenta, período $periodo-$orden")
                return null
            }

        } catch (e: Exception) {
            logger.error("Error al consultar CAEA en AFIP: ${e.message}", e)
            null
        }
    }

    /**
     * Muestra el estado real de CAEAs en la base de datos
     */
    private suspend fun mostrarEstadoCAEAsReales() {
        try {
            val caeasTotales = mutableListOf<CaeaCode>()

            for (puntoVenta in config.puntosVentaOffline) {
                val caeas = caeaRepository.findByPuntoVenta(puntoVenta)
                caeasTotales.addAll(caeas)
            }

            logger.info("=== Estado Real CAEAs en Base de Datos ===")
            logger.info("Total CAEAs encontrados: ${caeasTotales.size}")

            if (caeasTotales.isEmpty()) {
                logger.info("No hay CAEAs en la base de datos")
                return
            }

            // Agrupar por estado de la BD
            val porEstado = caeasTotales.groupBy { it.estado }

            porEstado.forEach { (estado, caeas) ->
                logger.info("CAEAs con estado $estado: ${caeas.size}")
                caeas.forEach { caea ->
                    val vencido = if (caea.estaVencido()) "VENCIDO" else "VIGENTE"
                    val informado = if (caeaRepository.isInformado(caea.caea)) "INFORMADO" else "NO_INFORMADO"
                    logger.info("  - ${caea.caea} (${caea.periodo}-${caea.orden}) PV:${caea.puntoVenta} - $vencido, $informado")
                }
            }

        } catch (e: Exception) {
            logger.error("Error al mostrar estado real de CAEAs: ${e.message}", e)
        }
    }

    /**
     * Actualiza automáticamente los estados de CAEAs vencidos
     */
    private suspend fun actualizarEstadosVencidos() {
        try {
            logger.debug("Actualizando estados de CAEAs vencidos...")
            val caeasMarcados = caeaRepository.markExpiredCaeas()

            if (caeasMarcados > 0) {
                logger.info("✅ $caeasMarcados CAEAs marcados como VENCIDOS automáticamente")
            } else {
                logger.debug("No hay CAEAs para marcar como vencidos")
            }
        } catch (e: Exception) {
            logger.error("Error al actualizar estados de CAEAs vencidos: ${e.message}", e)
        }
    }
}
