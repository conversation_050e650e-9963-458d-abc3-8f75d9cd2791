package com.gnico.majo.infrastructure.printer

import com.gnico.majo.application.domain.model.Comprobante
import com.gnico.majo.application.domain.model.Sale
import com.gnico.majo.application.domain.model.SaleItem
import com.gnico.majo.application.domain.model.SalesReportTicket
import com.gnico.majo.application.domain.model.TipoAutorizacion
import com.gnico.majo.application.domain.model.TipoComprobanteAfip
import com.gnico.majo.infrastructure.config.PrinterConfiguration
import com.github.anastaciocintra.escpos.EscPos
import com.github.anastaciocintra.escpos.EscPosConst
import com.github.anastaciocintra.escpos.barcode.QRCode
import java.math.BigDecimal
import java.time.format.DateTimeFormatter
import java.util.Base64

/**
 * Formateador de tickets con estilos ESC/POS para impresión térmica
 * Utiliza comandos ESC/POS directos para aplicar estilos como negrita, subrayado y tamaños de fuente
 */
class StyledTicketFormatter(private val config: PrinterConfiguration) {

    companion object {
        private const val TICKET_WIDTH = 48 // Caracteres por línea en impresora térmica
        private const val SEPARATOR_LINE = "================================================"
        private const val DASH_LINE = "------------------------------------------------"
        private const val DOT_LINE = "................................................"

        private val DATE_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")
        private val DATE_ONLY_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy")

        // Comandos ESC/POS
        private const val ESC = "\u001B"
        private const val GS = "\u001D"

        // Estilos de texto
        private const val BOLD_ON = "${ESC}E\u0001"
        private const val BOLD_OFF = "${ESC}E\u0000"
        private const val UNDERLINE_ON = "${ESC}-\u0001"
        private const val UNDERLINE_OFF = "${ESC}-\u0000"

        // Alineación
        private const val ALIGN_LEFT = "${ESC}a\u0000"
        private const val ALIGN_CENTER = "${ESC}a\u0001"
        private const val ALIGN_RIGHT = "${ESC}a\u0002"

        // Tamaños de fuente
        private const val FONT_SIZE_NORMAL = "${GS}!\u0000"
        private const val FONT_SIZE_DOUBLE_HEIGHT = "${GS}!\u0001"
        private const val FONT_SIZE_DOUBLE_WIDTH = "${GS}!\u0010"
        private const val FONT_SIZE_DOUBLE_BOTH = "${GS}!\u0011"
    }
    
    /**
     * Genera un ticket fiscal con estilos ESC/POS
     */
    fun formatFiscalTicket(escpos: EscPos, comprobante: Comprobante, sale: Sale) {
        escpos.initializePrinter()

        printCompanyHeader(escpos)
        printSeparator(escpos)
        printFiscalInfo(escpos, comprobante, sale)
        printSeparator(escpos)
        printItems(escpos, sale.items, true)
        printTotals(escpos, sale, comprobante)
        printSeparator(escpos)
        printFiscalFooter(escpos)
        printFiscalCAEInfo(escpos, comprobante)
    }

    /**
     * Genera un ticket no fiscal con estilos ESC/POS
     */
    fun formatNonFiscalTicket(escpos: EscPos, sale: Sale) {
        escpos.initializePrinter()

        printNonFiscalHeader(escpos, sale)
        printSeparator(escpos)
        printItems(escpos, sale.items, false)
        printTotals(escpos, sale, null)
        printNonFiscalFooter(escpos)
    }

    
    private fun printCompanyHeader(escpos: EscPos) {
        writeWithStyle(escpos, config.companyName)
        writeWithStyle(escpos, "CUIT: ${formatCuit(config.companyCUIT)}")
        writeWithStyle(escpos, "Ing. Brutos: ${config.companyIIBB}")
        writeWithStyle(escpos, config.companyAddress)
        writeWithStyle(escpos, "Inicio de actividades: ${config.companyStart}")
        writeWithStyle(escpos, "IVA Responsable Inscripto")
    }
    
    private fun printFiscalInfo(escpos: EscPos, comprobante: Comprobante, sale: Sale) {
        // Fecha y número de comprobante en la misma línea
        val fecha = comprobante.fechaEmision.format(DATE_FORMATTER)
        val numeroComprobante = String.format("%05d-%08d", comprobante.puntoVenta, comprobante.numeroComprobante)
        printLeftRight(escpos, fecha, "Nro: $numeroComprobante")

        // Tipo de comprobante en tamaño doble, bold y centrado
        val tipoComprobanteAfip = TipoComprobanteAfip.fromString(comprobante.tipoComprobante)
        val descripcionComprobante = tipoComprobanteAfip?.descripcion ?: comprobante.tipoComprobante

        writeWithStyle(escpos, descripcionComprobante, center = true, bold = true, doubleHeight = true)

        // Solo manejamos consumidor final
        writeWithStyle(escpos, "A CONSUMIDOR FINAL", center = true, bold = true)
    }
    
    private fun printNonFiscalHeader(escpos: EscPos, sale: Sale) {
        val fecha = sale.fechaVenta.format(DATE_FORMATTER)
        printLeftRight(escpos, config.companyName, fecha)
        escpos.feed(1)
        writeWithStyle(escpos, "TICKET NO FISCAL", center = true, bold = true)
        writeWithStyle(escpos, "DOCUMENTO NO VALIDO COMO FACTURA", center = true)
    }

    
    private fun printItems(escpos: EscPos, items: List<SaleItem>, showIVA: Boolean) {
        escpos.writeLF("Cantidad x Precio Unit${if (showIVA) " (% IVA)" else ""}")
        printLeftRight(escpos, "Descripción", "Precio")
        escpos.writeLF(DASH_LINE)

        items.forEach { item ->
            // Línea 1: Cantidad x Precio = Subtotal SIN descuento
            val cantidadStr = formatDecimal(item.cantidad)
            val precioStr = formatCurrency(item.precioUnitario)
            // Calcular subtotal sin descuento (cantidad x precio unitario)
            val subtotalSinDescuento = item.cantidad.multiply(item.precioUnitario)
            val subtotalStr = formatCurrency(subtotalSinDescuento)
            escpos.writeLF("$cantidadStr x $precioStr${if (showIVA) " (${item.tipoIva.porcentaje})" else ""}")

            // Línea 2: Producto (con manejo de texto largo)
            printLeftRightWithWrapping(escpos, item.productoNombre, subtotalStr)
        }

        escpos.writeLF(DASH_LINE)
    }
    
    private fun printTotals(escpos: EscPos, sale: Sale, comprobante: Comprobante?) {
        // Mostrar subtotal y descuento si aplica
        if (sale.hasDescuento()) {
            val subtotalSinDescuento = sale.calculateSubtotalSinDescuento()
            val montoDescuento = sale.calculateMontoDescuento()

            printRightAligned(escpos, "Subtotal:", formatCurrency(subtotalSinDescuento))
            printRightAligned(escpos, "Descuento (${sale.porcentajeDescuento}%):", "-${formatCurrency(montoDescuento)}")
            escpos.writeLF(DASH_LINE)
        }

        if (comprobante != null) {
            // Totales fiscales detallados
            val taxAmounts = sale.calculateTaxAmounts()

            printRightAlignedWithStyle(escpos, "TOTAL:", formatCurrency(taxAmounts.impTotal), bold = true, doubleSize = true)

            escpos.writeLF(DASH_LINE)
            escpos.writeLF("Regimen de transparencia fiscal")
            printRightAligned(escpos, "IVA Contenido:", formatCurrency(taxAmounts.impIva))
        } else {
            // Total simple para ticket no fiscal
            printRightAlignedWithStyle(escpos, "TOTAL:", formatCurrency(sale.montoTotal), bold = true, doubleSize = true)
        }
    }
    
    private fun printFiscalFooter(escpos: EscPos) {
        if (config.companyPhone.isNotBlank()) {
            escpos.writeLF("Teléfonos: ${config.companyPhone}")
        }
        if (config.companyEmail.isNotBlank()) {
            escpos.writeLF("Email: ${config.companyEmail}")
        }
    }

    private fun printFiscalCAEInfo(escpos: EscPos, comprobante: Comprobante) {
        escpos.feed(1)

        // Imprimir QR code antes del CAE/CAEA
        printFiscalQRCode(escpos, comprobante)
        escpos.feed(1)

        // Mostrar CAE o CAEA según el tipo de autorización
        val tipoAuth = if (comprobante.tipoAutorizacion == TipoAutorizacion.CAEA) "CAEA" else "CAE"
        val authInfo = "$tipoAuth: ${comprobante.cae}"
        val vtoInfo = "Vto: ${comprobante.fechaVencimientoCae.format(DATE_ONLY_FORMATTER)}"
        printLeftRight(escpos, authInfo, vtoInfo)
        // Agregar espacio configurable al final para evitar corte prematuro
        escpos.feed(config.feedLinesBeforeCut)
    }
    
    private fun printNonFiscalFooter(escpos: EscPos) {
        escpos.feed(1)
        writeWithStyle(escpos, "GRACIAS POR SU COMPRA", center = true, bold = true)
        // Agregar espacio configurable al final para evitar corte prematuro
        escpos.feed(config.feedLinesBeforeCut)
    }
    
    private fun printSeparator(escpos: EscPos) {
        escpos.writeLF(SEPARATOR_LINE)
    }

    private fun printDotSeparator(escpos: EscPos) {
        escpos.writeLF(DOT_LINE)
    }
    
    private fun printLeftRight(escpos: EscPos, leftText: String, rightText: String) {
        val totalLength = leftText.length + rightText.length
        val padding = if (totalLength < TICKET_WIDTH) {
            " ".repeat(TICKET_WIDTH - totalLength)
        } else {
            " " // Al menos un espacio si no cabe
        }
        escpos.writeLF("$leftText$padding$rightText")
    }

    /**
     * Imprime texto izquierdo y derecho con manejo inteligente de texto largo.
     * Si el texto izquierdo es muy largo, lo divide en múltiples líneas dejando
     * espacio antes del margen derecho, y coloca el texto derecho alineado en la última línea.
     */
    private fun printLeftRightWithWrapping(escpos: EscPos, leftText: String, rightText: String) {
        val rightTextLength = rightText.length
        val maxLeftWidth = TICKET_WIDTH - rightTextLength - 2 // Reservar espacio para texto derecho + 2 espacios de margen

        if (leftText.length <= maxLeftWidth) {
            // El texto cabe en una línea, usar función normal
            printLeftRight(escpos, leftText, rightText)
            return
        }

        // El texto es muy largo, necesita wrapping
        val words = leftText.split(" ")
        val lines = mutableListOf<String>()
        var currentLine = ""

        for (word in words) {
            val testLine = if (currentLine.isEmpty()) word else "$currentLine $word"

            if (testLine.length <= maxLeftWidth) {
                currentLine = testLine
            } else {
                // La palabra no cabe, guardar línea actual y empezar nueva
                if (currentLine.isNotEmpty()) {
                    lines.add(currentLine)
                    currentLine = word
                } else {
                    // La palabra sola es más larga que el ancho disponible
                    // Dividirla por caracteres
                    if (word.length > maxLeftWidth) {
                        var remainingWord = word
                        while (remainingWord.length > maxLeftWidth) {
                            lines.add(remainingWord.substring(0, maxLeftWidth))
                            remainingWord = remainingWord.substring(maxLeftWidth)
                        }
                        currentLine = remainingWord
                    } else {
                        currentLine = word
                    }
                }
            }
        }

        // Agregar la última línea si no está vacía
        if (currentLine.isNotEmpty()) {
            lines.add(currentLine)
        }

        // Imprimir todas las líneas excepto la última
        for (i in 0 until lines.size - 1) {
            escpos.writeLF(lines[i])
        }

        // Imprimir la última línea con el texto derecho alineado
        if (lines.isNotEmpty()) {
            val lastLine = lines.last()
            printLeftRight(escpos, lastLine, rightText)
        } else {
            // Caso edge: no hay líneas (texto vacío)
            printLeftRight(escpos, "", rightText)
        }
    }
    
    private fun printRightAligned(escpos: EscPos, label: String, value: String) {
        val combined = "$label $value"
        val padding = TICKET_WIDTH - combined.length
        val aligned = " ".repeat(maxOf(0, padding)) + combined
        escpos.writeLF(aligned)
    }
    
    private fun formatCuit(cuit: String): String {
        return if (cuit.length == 11) {
            "${cuit.substring(0, 2)}-${cuit.substring(2, 10)}-${cuit.substring(10)}"
        } else {
            cuit
        }
    }
    
    private fun formatCurrency(amount: BigDecimal): String {
        return "$${String.format("%.2f", amount)}"
    }

    private fun formatDecimal(amount: BigDecimal): String {
        return String.format("%.2f", amount)
    }

    private fun formatPercentage(percentage: BigDecimal): String {
        return String.format("%.1f", percentage)
    }

    /**
     * Escribe texto con estilos ESC/POS aplicados
     */
    private fun writeWithStyle(
        escpos: EscPos,
        text: String,
        bold: Boolean = false,
        underline: Boolean = false,
        center: Boolean = false,
        doubleHeight: Boolean = false,
        doubleSize: Boolean = false
    ) {
        val styledText = buildString {
            // Aplicar alineación
            if (center) append(ALIGN_CENTER)

            // Aplicar tamaños de fuente
            when {
                doubleSize -> append(FONT_SIZE_DOUBLE_BOTH)
                doubleHeight -> append(FONT_SIZE_DOUBLE_HEIGHT)
            }

            // Aplicar estilos
            if (bold) append(BOLD_ON)
            if (underline) append(UNDERLINE_ON)

            // Agregar el texto
            append(text)

            // Resetear estilos
            if (underline) append(UNDERLINE_OFF)
            if (bold) append(BOLD_OFF)

            // Resetear tamaño de fuente
            if (doubleSize || doubleHeight) append(FONT_SIZE_NORMAL)

            // Resetear alineación
            if (center) append(ALIGN_LEFT)
        }

        escpos.writeLF(styledText)
    }

    /**
     * Imprime texto alineado a la derecha con estilos
     */
    private fun printRightAlignedWithStyle(
        escpos: EscPos,
        label: String,
        value: String,
        bold: Boolean = false,
        doubleSize: Boolean = false
    ) {
        val combined = "$label $value"
        val styledText = buildString {
            // Aplicar tamaño de fuente
            if (doubleSize) append(FONT_SIZE_DOUBLE_BOTH)

            // Aplicar negrita
            if (bold) append(BOLD_ON)

            // Calcular padding considerando el ancho efectivo con tamaño doble
            val effectiveWidth = if (doubleSize) TICKET_WIDTH / 2 else TICKET_WIDTH
            val padding = effectiveWidth - combined.length
            val aligned = " ".repeat(maxOf(0, padding)) + combined
            append(aligned)

            // Resetear estilos
            if (bold) append(BOLD_OFF)
            if (doubleSize) append(FONT_SIZE_NORMAL)
        }

        escpos.writeLF(styledText)
    }

    /**
     * Imprime el código QR fiscal con la información requerida por AFIP
     */
    private fun printFiscalQRCode(escpos: EscPos, comprobante: Comprobante) {
        try {
            val qrData = generateFiscalQRData(comprobante)
            val qrCode = QRCode()

            qrCode.setModel(QRCode.QRModel._2)
            qrCode.setSize(5)
            qrCode.setJustification(EscPosConst.Justification.Center)

            // Imprimir el QR code con configuración básica
            escpos.write(qrCode, qrData)

        } catch (e: Exception) {
            // Si hay error generando el QR, continuar sin él
            // Log del error pero no interrumpir la impresión
            escpos.writeLF("Error generando QR: ${e.message}")
        }
    }

    /**
     * Genera los datos del QR code fiscal según especificaciones AFIP
     * Formato: URL con JSON codificado en Base64
     */
    private fun generateFiscalQRData(comprobante: Comprobante): String {
        // Construir JSON con datos del comprobante según especificaciones AFIP
        val jsonData = buildString {
            append("{")
            append("\"ver\":1,")
            append("\"fecha\":\"${comprobante.fechaEmision.toLocalDate()}\",")
            append("\"cuit\":${config.companyCUIT.toLong()},")
            append("\"ptoVta\":${comprobante.puntoVenta},")

            // Mapear tipo de comprobante a código AFIP
            val tipoComprobanteAfip = TipoComprobanteAfip.fromString(comprobante.tipoComprobante)
            val codigoTipoComprobante = tipoComprobanteAfip?.codigo ?: 6 // Default a Factura B
            append("\"tipoCmp\":$codigoTipoComprobante,")

            append("\"nroCmp\":${comprobante.numeroComprobante},")
            append("\"importe\":${comprobante.impTotal.toPlainString()},")
            append("\"moneda\":\"${comprobante.monId}\",")
            append("\"ctz\":${comprobante.monCotiz.toPlainString()},")

            // Determinar tipo y número de documento del receptor si corresponde
            val (tipoDocRec, nroDocRec) = getReceptorDocumentInfo(comprobante)
            if (tipoDocRec != null && nroDocRec != null) {
                append("\"tipoDocRec\":$tipoDocRec,")
                append("\"nroDocRec\":$nroDocRec,")
            }

            // Determinar tipo de código de autorización (CAE o CAEA)
            val tipoCodAut = if (isCAEA(comprobante)) "A" else "E"
            append("\"tipoCodAut\":\"$tipoCodAut\",")
            append("\"codAut\":${comprobante.cae}")

            append("}")
        }

        // Codificar JSON en Base64
        val base64Data = Base64.getEncoder().encodeToString(jsonData.toByteArray())

        // Construir URL completa según especificaciones AFIP
        return "https://www.arca.gob.ar/fe/qr/?p=$base64Data"
    }

    /**
     * Determina si un código de autorización es CAEA (offline) o CAE (online)
     * Usa el campo tipoAutorizacion del comprobante para determinarlo correctamente
     */
    private fun isCAEA(comprobante: Comprobante): Boolean {
        return comprobante.tipoAutorizacion == TipoAutorizacion.CAEA
    }

    /**
     * Obtiene información del documento del receptor según las especificaciones AFIP
     * Retorna un par (tipoDoc, numeroDoc) o (null, null) si no aplica
     */
    private fun getReceptorDocumentInfo(comprobante: Comprobante): Pair<Int?, Long?> {
        // Solo manejamos consumidor final (sin información de cliente)
        // Para comprobantes tipo B siempre retornamos null
        return Pair(null, null)
    }

    /**
     * Genera un ticket de reporte de ventas con estilos ESC/POS
     */
    fun formatSalesReportTicket(escpos: EscPos, reportTicket: SalesReportTicket) {
        escpos.initializePrinter()

        printReportHeader(escpos, reportTicket)
        printSeparator(escpos)
        printReportSummary(escpos, reportTicket)
        printSeparator(escpos)
        printPaymentBreakdown(escpos, reportTicket)
        printSeparator(escpos)
        printReportFooter(escpos, reportTicket)
    }

    private fun printReportHeader(escpos: EscPos, reportTicket: SalesReportTicket) {
        writeWithStyle(escpos, config.companyName, center = true)
        writeWithStyle(escpos, "REPORTE DE VENTAS", center = true, bold = true)

        escpos.feed(1)

        // Mostrar período del reporte con fechas
        val periodoStr = when {
            reportTicket.periodo.esHoy() -> {
                val fecha = reportTicket.periodo.desde.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"))
                "HOY - $fecha"
            }
            reportTicket.periodo.esAyer() -> {
                val fecha = reportTicket.periodo.desde.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"))
                "AYER - $fecha"
            }
            reportTicket.periodo.esSemanaActual() -> {
                val desde = reportTicket.periodo.desde.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"))
                val hasta = reportTicket.periodo.hasta.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"))
                "ESTA SEMANA - $desde a $hasta"
            }
            reportTicket.periodo.esMesActual() -> {
                val desde = reportTicket.periodo.desde.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"))
                val hasta = reportTicket.periodo.hasta.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"))
                "ESTE MES - $desde a $hasta"
            }
            else -> {
                // Para períodos personalizados, verificar si es un solo día
                val desde = reportTicket.periodo.desde.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"))
                val hasta = reportTicket.periodo.hasta.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"))
                if (desde == hasta) {
                    "$desde"
                } else {
                    "$desde a $hasta"
                }
            }
        }
        writeWithStyle(escpos, "Período: $periodoStr", center = true, bold = true)
    }

    private fun printReportSummary(escpos: EscPos, reportTicket: SalesReportTicket) {
        writeWithStyle(escpos, "RESUMEN GENERAL", bold = true)
        printDotSeparator(escpos)

        // Ventas totales
        writeWithStyle(escpos, "Cantidad ventas: ${reportTicket.resumenGeneral.totalVentas}")
        if (reportTicket.resumenGeneral.ventasCanceladas > 0) {
            writeWithStyle(escpos, "Ventas canceladas: ${reportTicket.resumenGeneral.ventasCanceladas}")
        }
        writeWithStyle(escpos, "Cantidad facturado: ${reportTicket.resumenGeneral.ventasConComprobante}")

        escpos.feed(1)

        // Montos principales
        printRightAlignedWithStyle(escpos, "TOTAL VENTAS:", formatCurrency(reportTicket.montoTotalVentas), bold = true)
        printRightAligned(escpos, "Total facturado:", formatCurrency(reportTicket.montoTotalFacturado))
        printRightAligned(escpos, "IVA contenido:", formatCurrency(reportTicket.montoTotalIva))


    }

    private fun printPaymentBreakdown(escpos: EscPos, reportTicket: SalesReportTicket) {
        writeWithStyle(escpos, "MEDIOS DE PAGO", bold = true)
        printDotSeparator(escpos)

        // Efectivo
        if (reportTicket.ventasEfectivo.cantidadVentas > 0) {
            writeWithStyle(escpos, "EFECTIVO", bold = true)
            printRightAligned(escpos, "Cantidad:", "${reportTicket.ventasEfectivo.cantidadVentas}")
            printRightAligned(escpos, "Monto:", formatCurrency(reportTicket.ventasEfectivo.montoTotal))
            printRightAligned(escpos, "Porcentaje:", "${formatPercentage(reportTicket.ventasEfectivo.porcentajeDelTotal)}%")
            escpos.writeLF("")
        }

        // Electrónicos
        if (reportTicket.ventasElectronicas.cantidadVentas > 0) {
            writeWithStyle(escpos, "ELECTRONICO", bold = true)
            printRightAligned(escpos, "Cantidad:", "${reportTicket.ventasElectronicas.cantidadVentas}")
            printRightAligned(escpos, "Monto:", formatCurrency(reportTicket.ventasElectronicas.montoTotal))
            printRightAligned(escpos, "Porcentaje:", "${formatPercentage(reportTicket.ventasElectronicas.porcentajeDelTotal)}%")
        }
    }

    private fun printReportFooter(escpos: EscPos, reportTicket: SalesReportTicket) {
        val fechaGeneracion = reportTicket.fechaGeneracion.format(DATE_FORMATTER)
        writeWithStyle(escpos, "Fecha de impresión: $fechaGeneracion", center = true)
        // Agregar espacio configurable al final para evitar corte prematuro
        escpos.feed(config.feedLinesBeforeCut)
    }

    /**
     * Construye un campo TLV (Tag-Length-Value)
     * @deprecated Este método ya no se usa con el nuevo formato JSON del QR
     */
    private fun StringBuilder.appendTLV(tag: String, value: String) {
        val length = String.format("%02d", value.length)
        append(tag)
        append(length)
        append(value)
    }
}
