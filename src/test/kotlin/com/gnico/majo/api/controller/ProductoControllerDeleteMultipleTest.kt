package com.gnico.majo.api.controller

import com.gnico.majo.adapter.controller.rest.ProductoController
import com.gnico.majo.adapter.controller.dto.DeleteMultipleProductosRequest
import com.gnico.majo.application.port.`in`.ProductoService
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.times
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`

class ProductoControllerDeleteMultipleTest {

    private lateinit var productoService: ProductoService
    private lateinit var productoController: ProductoController

    @BeforeEach
    fun setUp() {
        productoService = mock(ProductoService::class.java)
        productoController = ProductoController(productoService)
    }

    @Test
    fun `deleteMultipleProductos should delete multiple products successfully`() {
        // Arrange
        val codigos = listOf(1001, 1002, 1003)
        val request = DeleteMultipleProductosRequest(codigos)
        `when`(productoService.deleteMultipleProductos(codigos)).thenReturn(3)

        // Act
        val response = productoController.deleteMultipleProductos(request)

        // Assert
        assertEquals(3, response.eliminados)
        assertEquals("Se eliminaron 3 producto(s) correctamente", response.mensaje)
        verify(productoService, times(1)).deleteMultipleProductos(codigos)
    }

    @Test
    fun `deleteMultipleProductos should handle empty list`() {
        // Arrange
        val request = DeleteMultipleProductosRequest(emptyList())

        // Act
        val response = productoController.deleteMultipleProductos(request)

        // Assert
        assertEquals(0, response.eliminados)
        assertEquals("No se proporcionaron códigos para eliminar", response.mensaje)
        verify(productoService, times(0)).deleteMultipleProductos(emptyList())
    }

    @Test
    fun `deleteMultipleProductos should handle no products found`() {
        // Arrange
        val codigos = listOf(9999, 9998)
        val request = DeleteMultipleProductosRequest(codigos)
        `when`(productoService.deleteMultipleProductos(codigos)).thenReturn(0)

        // Act
        val response = productoController.deleteMultipleProductos(request)

        // Assert
        assertEquals(0, response.eliminados)
        assertEquals("No se encontraron productos con los códigos proporcionados", response.mensaje)
        verify(productoService, times(1)).deleteMultipleProductos(codigos)
    }

    @Test
    fun `deleteMultipleProductos should handle partial deletion`() {
        // Arrange
        val codigos = listOf(1001, 9999, 1002)
        val request = DeleteMultipleProductosRequest(codigos)
        `when`(productoService.deleteMultipleProductos(codigos)).thenReturn(2)

        // Act
        val response = productoController.deleteMultipleProductos(request)

        // Assert
        assertEquals(2, response.eliminados)
        assertEquals("Se eliminaron 2 producto(s) correctamente", response.mensaje)
        verify(productoService, times(1)).deleteMultipleProductos(codigos)
    }
}
