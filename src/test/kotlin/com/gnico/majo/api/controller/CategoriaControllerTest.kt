package com.gnico.majo.api.controller

import com.gnico.majo.adapter.controller.rest.CategoriaController
import com.gnico.majo.adapter.controller.dto.CategoriaDto
import com.gnico.majo.application.domain.model.Categoria
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.port.`in`.CategoriaService
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.times
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any

class CategoriaControllerTest {

    private lateinit var categoriaService: CategoriaService
    private lateinit var categoriaController: CategoriaController

    @BeforeEach
    fun setUp() {
        categoriaService = mock(CategoriaService::class.java)
        categoriaController = CategoriaController(categoriaService)
    }

    @Test
    fun `getAllCategorias should return list of categories`() {
        // Arrange
        val categorias = listOf(
            createTestCategoria(1, "Bebidas", "Bebidas frías y calientes"),
            createTestCategoria(2, "Postres", "Postres y dulces")
        )
        `when`(categoriaService.getAllCategorias()).thenReturn(categorias)

        // Act
        val response = categoriaController.getAllCategorias()

        // Assert
        assertEquals(2, response.total)
        assertEquals(2, response.categorias.size)
        assertEquals("Bebidas", response.categorias[0].nombre)
        assertEquals("Postres", response.categorias[1].nombre)
        verify(categoriaService, times(1)).getAllCategorias()
    }

    @Test
    fun `getCategoriaById should return category when found`() {
        // Arrange
        val categoria = createTestCategoria(1, "Bebidas", "Bebidas frías y calientes")
        `when`(categoriaService.getCategoriaById(Id(1))).thenReturn(categoria)

        // Act
        val response = categoriaController.getCategoriaById(1)

        // Assert
        assertNotNull(response)
        assertEquals(1, response?.id)
        assertEquals("Bebidas", response?.nombre)
        assertEquals("Bebidas frías y calientes", response?.descripcion)
        assertNull(response?.color)
        verify(categoriaService, times(1)).getCategoriaById(Id(1))
    }

    @Test
    fun `getCategoriaById should return category with color when found`() {
        // Arrange
        val categoria = createTestCategoria(1, "Bebidas", "Bebidas frías y calientes", "00ff00")
        `when`(categoriaService.getCategoriaById(Id(1))).thenReturn(categoria)

        // Act
        val response = categoriaController.getCategoriaById(1)

        // Assert
        assertNotNull(response)
        assertEquals(1, response?.id)
        assertEquals("Bebidas", response?.nombre)
        assertEquals("Bebidas frías y calientes", response?.descripcion)
        assertEquals("00ff00", response?.color)
        verify(categoriaService, times(1)).getCategoriaById(Id(1))
    }

    @Test
    fun `getCategoriaById should return null when not found`() {
        // Arrange
        `when`(categoriaService.getCategoriaById(Id(999))).thenReturn(null)

        // Act
        val response = categoriaController.getCategoriaById(999)

        // Assert
        assertNull(response)
        verify(categoriaService, times(1)).getCategoriaById(Id(999))
    }

    @Test
    fun `createCategoria should call service and return created response`() {
        // Arrange
        val request = CategoriaDto(
            nombre = "Nueva Categoría",
            descripcion = "Descripción de prueba"
        )
        `when`(categoriaService.createCategoria(any())).thenReturn(Id(1))

        // Act
        val response = categoriaController.createCategoria(request)

        // Assert
        assertEquals(1, response.id)
        assertEquals("Categoría creada exitosamente", response.mensaje)
        verify(categoriaService, times(1)).createCategoria(any())
    }

    @Test
    fun `createCategoria with color should call service and return created response`() {
        // Arrange
        val request = CategoriaDto(
            nombre = "Nueva Categoría",
            descripcion = "Descripción de prueba",
            color = "ff0000"
        )
        `when`(categoriaService.createCategoria(any())).thenReturn(Id(1))

        // Act
        val response = categoriaController.createCategoria(request)

        // Assert
        assertEquals(1, response.id)
        assertEquals("Categoría creada exitosamente", response.mensaje)
        verify(categoriaService, times(1)).createCategoria(any())
    }

    @Test
    fun `updateCategoria should call service and return result`() {
        // Arrange
        val request = CategoriaDto(
            nombre = "Categoría Actualizada",
            descripcion = "Descripción actualizada"
        )
        `when`(categoriaService.updateCategoria(any())).thenReturn(true)

        // Act
        val result = categoriaController.updateCategoria(1, request)

        // Assert
        assertTrue(result)
        verify(categoriaService, times(1)).updateCategoria(any())
    }

    @Test
    fun `deleteCategoria should call service and return result`() {
        // Arrange
        `when`(categoriaService.deleteCategoria(Id(1))).thenReturn(true)

        // Act
        val result = categoriaController.deleteCategoria(1)

        // Assert
        assertTrue(result)
        verify(categoriaService, times(1)).deleteCategoria(Id(1))
    }

    // Helper method to create test categories
    private fun createTestCategoria(
        id: Int,
        nombre: String,
        descripcion: String = "Descripción de prueba",
        color: String? = null
    ): Categoria {
        return Categoria(
            id = Id(id),
            nombre = nombre,
            descripcion = descripcion,
            color = color,
            activo = true
        )
    }
}
