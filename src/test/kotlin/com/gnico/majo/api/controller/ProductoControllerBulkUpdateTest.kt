package com.gnico.majo.api.controller

import com.gnico.majo.adapter.controller.rest.ProductoController
import com.gnico.majo.adapter.controller.dto.BulkUpdateProductosRequest
import com.gnico.majo.application.port.`in`.ProductoService
import com.gnico.majo.application.domain.model.Id
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.times
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import java.math.BigDecimal

class ProductoControllerBulkUpdateTest {

    private lateinit var productoService: ProductoService
    private lateinit var productoController: ProductoController

    @BeforeEach
    fun setUp() {
        productoService = mock(ProductoService::class.java)
        productoController = ProductoController(productoService)
    }

    @Test
    fun `updateMultipleProductos should update multiple products successfully`() {
        // Arrange
        val codigos = listOf(1001, 1002, 1003)
        val request = BulkUpdateProductosRequest(
            codigos = codigos,
            categoriaId = 5,
            precioUnitario = 150.0,
            stockActual = 20
        )
        `when`(productoService.updateMultipleProductos(
            eq(codigos),
            eq(Id(5)),
            eq(BigDecimal("150.0")),
            eq(null),
            eq(20)
        )).thenReturn(3)

        // Act
        val response = productoController.updateMultipleProductos(request)

        // Assert
        assertEquals(3, response.actualizados)
        assertEquals("Se actualizaron 3 producto(s) correctamente", response.mensaje)
        verify(productoService, times(1)).updateMultipleProductos(
            eq(codigos),
            eq(Id(5)),
            eq(BigDecimal("150.0")),
            eq(null),
            eq(20)
        )
    }

    @Test
    fun `updateMultipleProductos should handle empty list`() {
        // Arrange
        val request = BulkUpdateProductosRequest(
            codigos = emptyList(),
            categoriaId = 5
        )

        // Act
        val response = productoController.updateMultipleProductos(request)

        // Assert
        assertEquals(0, response.actualizados)
        assertEquals("No se proporcionaron códigos para actualizar", response.mensaje)
        verify(productoService, times(0)).updateMultipleProductos(any(), any(), any(), any(), any())
    }

    @Test
    fun `updateMultipleProductos should handle no fields to update`() {
        // Arrange
        val request = BulkUpdateProductosRequest(
            codigos = listOf(1001, 1002)
        )

        // Act
        val response = productoController.updateMultipleProductos(request)

        // Assert
        assertEquals(0, response.actualizados)
        assertEquals("No se proporcionaron campos para actualizar", response.mensaje)
        verify(productoService, times(0)).updateMultipleProductos(any(), any(), any(), any(), any())
    }

    @Test
    fun `updateMultipleProductos should handle no products found`() {
        // Arrange
        val codigos = listOf(9999, 9998)
        val request = BulkUpdateProductosRequest(
            codigos = codigos,
            precioUnitario = 100.0
        )
        `when`(productoService.updateMultipleProductos(
            eq(codigos),
            eq(null),
            eq(BigDecimal("100.0")),
            eq(null),
            eq(null)
        )).thenReturn(0)

        // Act
        val response = productoController.updateMultipleProductos(request)

        // Assert
        assertEquals(0, response.actualizados)
        assertEquals("No se encontraron productos con los códigos proporcionados", response.mensaje)
        verify(productoService, times(1)).updateMultipleProductos(
            eq(codigos),
            eq(null),
            eq(BigDecimal("100.0")),
            eq(null),
            eq(null)
        )
    }

    @Test
    fun `updateMultipleProductos should handle partial update`() {
        // Arrange
        val codigos = listOf(1001, 9999, 1002)
        val request = BulkUpdateProductosRequest(
            codigos = codigos,
            stockActual = 50
        )
        `when`(productoService.updateMultipleProductos(
            eq(codigos),
            eq(null),
            eq(null),
            eq(null),
            eq(50)
        )).thenReturn(2)

        // Act
        val response = productoController.updateMultipleProductos(request)

        // Assert
        assertEquals(2, response.actualizados)
        assertEquals("Se actualizaron 2 producto(s) correctamente", response.mensaje)
        verify(productoService, times(1)).updateMultipleProductos(
            eq(codigos),
            eq(null),
            eq(null),
            eq(null),
            eq(50)
        )
    }

    @Test
    fun `updateMultipleProductos should handle single field update`() {
        // Arrange
        val codigos = listOf(1001)
        val request = BulkUpdateProductosRequest(
            codigos = codigos,
            unidadMedidaId = 3
        )
        `when`(productoService.updateMultipleProductos(
            eq(codigos),
            eq(null),
            eq(null),
            eq(Id(3)),
            eq(null)
        )).thenReturn(1)

        // Act
        val response = productoController.updateMultipleProductos(request)

        // Assert
        assertEquals(1, response.actualizados)
        assertEquals("Se actualizaron 1 producto(s) correctamente", response.mensaje)
        verify(productoService, times(1)).updateMultipleProductos(
            eq(codigos),
            eq(null),
            eq(null),
            eq(Id(3)),
            eq(null)
        )
    }

    @Test
    fun `updateMultipleProductos should clear categoria when categoriaId is zero`() {
        // Arrange
        val codigos = listOf(1001, 1002)
        val request = BulkUpdateProductosRequest(
            codigos = codigos,
            categoriaId = 0 // Valor especial para limpiar categoría
        )
        `when`(productoService.updateMultipleProductos(
            eq(codigos),
            eq(Id(-1)), // Valor especial interno
            eq(null),
            eq(null),
            eq(null)
        )).thenReturn(2)

        // Act
        val response = productoController.updateMultipleProductos(request)

        // Assert
        assertEquals(2, response.actualizados)
        assertEquals("Se actualizaron 2 producto(s) correctamente", response.mensaje)
        verify(productoService, times(1)).updateMultipleProductos(
            eq(codigos),
            eq(Id(-1)),
            eq(null),
            eq(null),
            eq(null)
        )
    }

    @Test
    fun `updateMultipleProductos should clear categoria when categoriaId is negative`() {
        // Arrange
        val codigos = listOf(1001)
        val request = BulkUpdateProductosRequest(
            codigos = codigos,
            categoriaId = -5 // Cualquier valor negativo limpia la categoría
        )
        `when`(productoService.updateMultipleProductos(
            eq(codigos),
            eq(Id(-1)), // Valor especial interno
            eq(null),
            eq(null),
            eq(null)
        )).thenReturn(1)

        // Act
        val response = productoController.updateMultipleProductos(request)

        // Assert
        assertEquals(1, response.actualizados)
        verify(productoService, times(1)).updateMultipleProductos(
            eq(codigos),
            eq(Id(-1)),
            eq(null),
            eq(null),
            eq(null)
        )
    }

    @Test
    fun `updateMultipleProductos should clear stockActual when stockActual is negative`() {
        // Arrange
        val codigos = listOf(1001, 1002, 1003)
        val request = BulkUpdateProductosRequest(
            codigos = codigos,
            stockActual = -1 // Valor especial para limpiar stock
        )
        `when`(productoService.updateMultipleProductos(
            eq(codigos),
            eq(null),
            eq(null),
            eq(null),
            eq(-1) // Valor especial interno
        )).thenReturn(3)

        // Act
        val response = productoController.updateMultipleProductos(request)

        // Assert
        assertEquals(3, response.actualizados)
        assertEquals("Se actualizaron 3 producto(s) correctamente", response.mensaje)
        verify(productoService, times(1)).updateMultipleProductos(
            eq(codigos),
            eq(null),
            eq(null),
            eq(null),
            eq(-1)
        )
    }

    @Test
    fun `updateMultipleProductos should clear both categoria and stock with special values`() {
        // Arrange
        val codigos = listOf(1001)
        val request = BulkUpdateProductosRequest(
            codigos = codigos,
            categoriaId = 0,    // Limpiar categoría
            stockActual = -10,  // Limpiar stock
            precioUnitario = 99.99
        )
        `when`(productoService.updateMultipleProductos(
            eq(codigos),
            eq(Id(-1)), // Valor especial para limpiar categoría
            eq(BigDecimal("99.99")),
            eq(null),
            eq(-1) // Valor especial para limpiar stock
        )).thenReturn(1)

        // Act
        val response = productoController.updateMultipleProductos(request)

        // Assert
        assertEquals(1, response.actualizados)
        verify(productoService, times(1)).updateMultipleProductos(
            eq(codigos),
            eq(Id(-1)),
            eq(BigDecimal("99.99")),
            eq(null),
            eq(-1)
        )
    }
}
