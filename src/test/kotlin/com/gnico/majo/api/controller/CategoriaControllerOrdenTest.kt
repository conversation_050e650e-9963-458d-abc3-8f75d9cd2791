package com.gnico.majo.api.controller

import com.gnico.majo.adapter.controller.rest.CategoriaController
import com.gnico.majo.adapter.controller.dto.BulkUpdateCategoriasOrdenRequest
import com.gnico.majo.adapter.controller.dto.CategoriaOrdenItem
import com.gnico.majo.application.port.`in`.CategoriaService
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.whenever
import org.mockito.kotlin.verify
import org.mockito.kotlin.times
import org.mockito.kotlin.eq
import kotlin.test.assertEquals

class CategoriaControllerOrdenTest {

    @Mock
    private lateinit var categoriaService: CategoriaService

    private lateinit var categoriaController: CategoriaController

    @BeforeEach
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        categoriaController = CategoriaController(categoriaService)
    }

    @Test
    fun `updateOrdenCategorias should update multiple categories successfully`() {
        // Arrange
        val request = BulkUpdateCategoriasOrdenRequest(
            categorias = listOf(
                CategoriaOrdenItem(id = 1, orden = 1),
                CategoriaOrdenItem(id = 2, orden = 2),
                CategoriaOrdenItem(id = 3, orden = 3)
            )
        )
        val expectedMap = mapOf(1 to 1, 2 to 2, 3 to 3)
        whenever(categoriaService.updateOrdenCategorias(eq(expectedMap))).thenReturn(3)

        // Act
        val response = categoriaController.updateOrdenCategorias(request)

        // Assert
        assertEquals(3, response.actualizadas)
        assertEquals("Se actualizó el orden de 3 categoría(s) correctamente", response.mensaje)
        verify(categoriaService, times(1)).updateOrdenCategorias(eq(expectedMap))
    }

    @Test
    fun `updateOrdenCategorias should return error when no categories provided`() {
        // Arrange
        val request = BulkUpdateCategoriasOrdenRequest(categorias = emptyList())

        // Act
        val response = categoriaController.updateOrdenCategorias(request)

        // Assert
        assertEquals(0, response.actualizadas)
        assertEquals("No se proporcionaron categorías para actualizar", response.mensaje)
        verify(categoriaService, times(0)).updateOrdenCategorias(any())
    }

    @Test
    fun `updateOrdenCategorias should return error when orden is not positive`() {
        // Arrange
        val request = BulkUpdateCategoriasOrdenRequest(
            categorias = listOf(
                CategoriaOrdenItem(id = 1, orden = 0),
                CategoriaOrdenItem(id = 2, orden = 2)
            )
        )

        // Act
        val response = categoriaController.updateOrdenCategorias(request)

        // Assert
        assertEquals(0, response.actualizadas)
        assertEquals("El orden debe ser un número positivo. Categoría ID 1 tiene orden 0", response.mensaje)
        verify(categoriaService, times(0)).updateOrdenCategorias(any())
    }

    @Test
    fun `updateOrdenCategorias should return error when orden is negative`() {
        // Arrange
        val request = BulkUpdateCategoriasOrdenRequest(
            categorias = listOf(
                CategoriaOrdenItem(id = 1, orden = 1),
                CategoriaOrdenItem(id = 2, orden = -1)
            )
        )

        // Act
        val response = categoriaController.updateOrdenCategorias(request)

        // Assert
        assertEquals(0, response.actualizadas)
        assertEquals("El orden debe ser un número positivo. Categoría ID 2 tiene orden -1", response.mensaje)
        verify(categoriaService, times(0)).updateOrdenCategorias(any())
    }

    @Test
    fun `updateOrdenCategorias should handle single category update`() {
        // Arrange
        val request = BulkUpdateCategoriasOrdenRequest(
            categorias = listOf(CategoriaOrdenItem(id = 5, orden = 10))
        )
        val expectedMap = mapOf(5 to 10)
        whenever(categoriaService.updateOrdenCategorias(eq(expectedMap))).thenReturn(1)

        // Act
        val response = categoriaController.updateOrdenCategorias(request)

        // Assert
        assertEquals(1, response.actualizadas)
        assertEquals("Se actualizó el orden de 1 categoría(s) correctamente", response.mensaje)
        verify(categoriaService, times(1)).updateOrdenCategorias(eq(expectedMap))
    }

    private fun any(): Map<Int, Int> = org.mockito.kotlin.any()
}
