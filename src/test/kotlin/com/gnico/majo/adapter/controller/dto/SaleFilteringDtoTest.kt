package com.gnico.majo.adapter.controller.dto

import kotlinx.serialization.json.Json
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNull
import kotlin.test.assertTrue

class SaleFilteringDtoTest {

    private val json = Json { ignoreUnknownKeys = true }

    @Test
    fun `SaleFilterRequest should have correct default values`() {
        // Given & When
        val request = SaleFilterRequest()

        // Then
        assertNull(request.fechaDesde)
        assertNull(request.fechaHasta)
        assertNull(request.usuarios)
        assertNull(request.comprobanteEmitido)
        assertNull(request.mediosPago)
        assertEquals(1, request.page)
        assertEquals(20, request.size)
    }

    @Test
    fun `SaleFilterRequest should serialize correctly`() {
        // Given
        val request = SaleFilterRequest(
            fechaDesde = "2024-01-01 00:00:00",
            fechaHasta = "2024-01-31 23:59:59",
            usuarios = listOf("vendedor1", "vendedor2"),
            comprobanteEmitido = false,
            mediosPago = listOf("EFECTIVO", "TARJETA_CREDITO"),
            page = 2,
            size = 50
        )

        // When
        val jsonString = json.encodeToString(SaleFilterRequest.serializer(), request)

        // Then
        assertTrue(jsonString.contains("\"fechaDesde\":\"2024-01-01 00:00:00\""))
        assertTrue(jsonString.contains("\"fechaHasta\":\"2024-01-31 23:59:59\""))
        assertTrue(jsonString.contains("\"usuarios\":[\"vendedor1\",\"vendedor2\"]"))
        assertTrue(jsonString.contains("\"comprobanteEmitido\":false"))
        assertTrue(jsonString.contains("\"mediosPago\":[\"EFECTIVO\",\"TARJETA_CREDITO\"]"))
        assertTrue(jsonString.contains("\"page\":2"))
        assertTrue(jsonString.contains("\"size\":50"))
    }

    @Test
    fun `SaleFilterRequest should deserialize correctly`() {
        // Given
        val jsonString = """
            {
                "fechaDesde": "2024-01-01 00:00:00",
                "fechaHasta": "2024-01-31 23:59:59",
                "usuarios": ["vendedor1", "vendedor2"],
                "comprobanteEmitido": true,
                "mediosPago": ["EFECTIVO", "QR"],
                "page": 3,
                "size": 25
            }
        """.trimIndent()

        // When
        val request = json.decodeFromString(SaleFilterRequest.serializer(), jsonString)

        // Then
        assertEquals("2024-01-01 00:00:00", request.fechaDesde)
        assertEquals("2024-01-31 23:59:59", request.fechaHasta)
        assertEquals(listOf("vendedor1", "vendedor2"), request.usuarios)
        assertEquals(true, request.comprobanteEmitido)
        assertEquals(listOf("EFECTIVO", "QR"), request.mediosPago)
        assertEquals(3, request.page)
        assertEquals(25, request.size)
    }

    @Test
    fun `SaleFilterRequest should handle null values correctly`() {
        // Given
        val jsonString = """
            {
                "page": 1,
                "size": 20
            }
        """.trimIndent()

        // When
        val request = json.decodeFromString(SaleFilterRequest.serializer(), jsonString)

        // Then
        assertNull(request.fechaDesde)
        assertNull(request.fechaHasta)
        assertNull(request.usuarios)
        assertNull(request.comprobanteEmitido)
        assertNull(request.mediosPago)
        assertEquals(1, request.page)
        assertEquals(20, request.size)
    }

    @Test
    fun `SalePageResponse should serialize correctly`() {
        // Given
        val saleResponse = SaleResponse(
            id = 1,
            numeroVenta = "**********",
            fechaVenta = "15/01/2024 14:30",
            usuarioUsername = "vendedor1",
            usuarioNombre = "Vendedor 1",
            montoTotal = "1,250.00",
            medioPago = "EFECTIVO",
            porcentajeDescuento = null,
            comprobanteEmitido = true,
            codigoTicketBalanza = "TKT001",
            idTicketBalanza = "ID001",
            cancelada = false,
            fechaCancelacion = null,
            usuarioCancelacion = null,
            motivoCancelacion = null,
            notaCreditoGenerada = false,
            items = listOf(
                SaleItemResponse(
                    productoNombre = "Producto Test",
                    cantidad = 2.0,
                    precioUnitario = "625.00",
                    subtotal = "1,250.00",
                    tipoIvaId = 5
                )
            )
        )

        val pageResponse = SalePageResponse(
            content = listOf(saleResponse),
            page = 1,
            size = 20,
            totalElements = 1,
            totalPages = 1,
            hasNext = false,
            hasPrevious = false
        )

        // When
        val jsonString = json.encodeToString(SalePageResponse.serializer(), pageResponse)

        // Then
        assertTrue(jsonString.contains("\"content\":["))
        assertTrue(jsonString.contains("\"numeroVenta\":\"**********\""))
        assertTrue(jsonString.contains("\"page\":1"))
        assertTrue(jsonString.contains("\"size\":20"))
        assertTrue(jsonString.contains("\"totalElements\":1"))
        assertTrue(jsonString.contains("\"totalPages\":1"))
        assertTrue(jsonString.contains("\"hasNext\":false"))
        assertTrue(jsonString.contains("\"hasPrevious\":false"))
    }

    @Test
    fun `SalePageResponse should deserialize correctly`() {
        // Given
        val jsonString = """
            {
                "content": [
                    {
                        "id": 1,
                        "numeroVenta": "**********",
                        "fechaVenta": "15/01/2024 14:30",
                        "usuarioUsername": "vendedor1",
                        "usuarioNombre": "Vendedor 1",
                        "montoTotal": "1,250.00",
                        "medioPago": "EFECTIVO",
                        "porcentajeDescuento": null,
                        "comprobanteEmitido": true,
                        "codigoTicketBalanza": "TKT001",
                        "idTicketBalanza": "ID001",
                        "cancelada": false,
                        "fechaCancelacion": null,
                        "usuarioCancelacion": null,
                        "motivoCancelacion": null,
                        "items": [
                            {
                                "productoNombre": "Producto Test",
                                "cantidad": 2.0,
                                "precioUnitario": "625.00",
                                "subtotal": "1,250.00",
                                "tipoIvaId": 5
                            }
                        ]
                    }
                ],
                "page": 2,
                "size": 10,
                "totalElements": 25,
                "totalPages": 3,
                "hasNext": true,
                "hasPrevious": true
            }
        """.trimIndent()

        // When
        val pageResponse = json.decodeFromString(SalePageResponse.serializer(), jsonString)

        // Then
        assertEquals(1, pageResponse.content.size)
        assertEquals("**********", pageResponse.content[0].numeroVenta)
        assertEquals("EFECTIVO", pageResponse.content[0].medioPago)
        assertEquals(true, pageResponse.content[0].comprobanteEmitido)
        assertEquals(2, pageResponse.page)
        assertEquals(10, pageResponse.size)
        assertEquals(25, pageResponse.totalElements)
        assertEquals(3, pageResponse.totalPages)
        assertTrue(pageResponse.hasNext)
        assertTrue(pageResponse.hasPrevious)
    }

    @Test
    fun `SalePageResponse should handle empty content correctly`() {
        // Given
        val pageResponse = SalePageResponse(
            content = emptyList(),
            page = 1,
            size = 20,
            totalElements = 0,
            totalPages = 0,
            hasNext = false,
            hasPrevious = false
        )

        // When
        val jsonString = json.encodeToString(SalePageResponse.serializer(), pageResponse)
        val deserializedResponse = json.decodeFromString(SalePageResponse.serializer(), jsonString)

        // Then
        assertEquals(0, deserializedResponse.content.size)
        assertEquals(1, deserializedResponse.page)
        assertEquals(20, deserializedResponse.size)
        assertEquals(0, deserializedResponse.totalElements)
        assertEquals(0, deserializedResponse.totalPages)
        assertFalse(deserializedResponse.hasNext)
        assertFalse(deserializedResponse.hasPrevious)
    }

    @Test
    fun `SalePageResponse pagination flags should be calculated correctly`() {
        // Test case 1: First page with more pages
        val firstPage = SalePageResponse(
            content = emptyList(),
            page = 1,
            size = 20,
            totalElements = 100,
            totalPages = 5,
            hasNext = true,
            hasPrevious = false
        )

        assertFalse(firstPage.hasPrevious)
        assertTrue(firstPage.hasNext)

        // Test case 2: Middle page
        val middlePage = SalePageResponse(
            content = emptyList(),
            page = 3,
            size = 20,
            totalElements = 100,
            totalPages = 5,
            hasNext = true,
            hasPrevious = true
        )

        assertTrue(middlePage.hasPrevious)
        assertTrue(middlePage.hasNext)

        // Test case 3: Last page
        val lastPage = SalePageResponse(
            content = emptyList(),
            page = 5,
            size = 20,
            totalElements = 100,
            totalPages = 5,
            hasNext = false,
            hasPrevious = true
        )

        assertTrue(lastPage.hasPrevious)
        assertFalse(lastPage.hasNext)

        // Test case 4: Single page
        val singlePage = SalePageResponse(
            content = emptyList(),
            page = 1,
            size = 20,
            totalElements = 10,
            totalPages = 1,
            hasNext = false,
            hasPrevious = false
        )

        assertFalse(singlePage.hasPrevious)
        assertFalse(singlePage.hasNext)
    }
}
