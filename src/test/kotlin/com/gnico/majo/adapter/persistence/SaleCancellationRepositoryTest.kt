package com.gnico.majo.adapter.persistence

import com.gnico.majo.application.port.out.SaleFilterCriteria
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*

class SaleCancellationRepositoryTest {

    @Test
    fun `should create filter criteria with incluirCanceladas false by default`() {
        // Given
        val criteria = SaleFilterCriteria(
            fechaDesde = null,
            fechaHasta = null,
            usuarios = null,
            comprobanteEmitido = null,
            mediosPago = null
            // incluirCanceladas no especificado, debe ser false por defecto
        )

        // Then
        assertFalse(criteria.incluirCanceladas, "incluirCanceladas debe ser false por defecto")
    }

    @Test
    fun `should create filter criteria with incluirCanceladas true when explicitly set`() {
        // Given
        val criteria = SaleFilterCriteria(
            fechaDesde = null,
            fechaHasta = null,
            usuarios = null,
            comprobanteEmitido = null,
            mediosPago = null,
            incluirCanceladas = true
        )

        // Then
        assertTrue(criteria.incluirCanceladas, "incluirCanceladas debe ser true cuando se especifica")
    }

    @Test
    fun `should create filter criteria with incluirCanceladas false when explicitly set`() {
        // Given
        val criteria = SaleFilterCriteria(
            fechaDesde = null,
            fechaHasta = null,
            usuarios = null,
            comprobanteEmitido = null,
            mediosPago = null,
            incluirCanceladas = false
        )

        // Then
        assertFalse(criteria.incluirCanceladas, "incluirCanceladas debe ser false cuando se especifica explícitamente")
    }

    @Test
    fun `should verify filter criteria properties are correctly set`() {
        // Given
        val usuarios = listOf("admin", "vendedor1")
        val mediosPago = listOf("EFECTIVO", "TARJETA")
        
        val criteria = SaleFilterCriteria(
            fechaDesde = null,
            fechaHasta = null,
            usuarios = usuarios,
            comprobanteEmitido = true,
            mediosPago = mediosPago,
            incluirCanceladas = true
        )

        // Then
        assertEquals(usuarios, criteria.usuarios)
        assertEquals(true, criteria.comprobanteEmitido)
        assertEquals(mediosPago, criteria.mediosPago)
        assertTrue(criteria.incluirCanceladas)
    }

    @Test
    fun `should verify all filter combinations work correctly`() {
        // Test 1: Solo incluir canceladas
        val criteria1 = SaleFilterCriteria(incluirCanceladas = true)
        assertTrue(criteria1.incluirCanceladas)
        assertNull(criteria1.usuarios)
        assertNull(criteria1.comprobanteEmitido)
        assertNull(criteria1.mediosPago)

        // Test 2: Excluir canceladas con otros filtros
        val criteria2 = SaleFilterCriteria(
            usuarios = listOf("admin"),
            comprobanteEmitido = false,
            incluirCanceladas = false
        )
        assertFalse(criteria2.incluirCanceladas)
        assertEquals(listOf("admin"), criteria2.usuarios)
        assertEquals(false, criteria2.comprobanteEmitido)

        // Test 3: Incluir canceladas con otros filtros
        val criteria3 = SaleFilterCriteria(
            mediosPago = listOf("EFECTIVO"),
            comprobanteEmitido = true,
            incluirCanceladas = true
        )
        assertTrue(criteria3.incluirCanceladas)
        assertEquals(listOf("EFECTIVO"), criteria3.mediosPago)
        assertEquals(true, criteria3.comprobanteEmitido)
    }

    @Test
    fun `should verify default behavior excludes cancelled sales`() {
        // Given - Crear criterio sin especificar incluirCanceladas
        val defaultCriteria = SaleFilterCriteria()

        // Then - Por defecto debe excluir ventas canceladas
        assertFalse(defaultCriteria.incluirCanceladas)
        assertNull(defaultCriteria.fechaDesde)
        assertNull(defaultCriteria.fechaHasta)
        assertNull(defaultCriteria.usuarios)
        assertNull(defaultCriteria.comprobanteEmitido)
        assertNull(defaultCriteria.mediosPago)
    }
}
