package com.gnico.majo.adapter.afip

import com.gnico.majo.application.domain.model.*
import com.gnico.majo.application.port.out.AfipService
import com.gnico.majo.application.port.out.CaeaRepositoryPort
import com.gnico.majo.application.port.out.ComprobanteCAEAInfo
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.mockito.Mockito.*
import java.time.LocalDate
import java.math.BigDecimal

/**
 * Test para verificar la funcionalidad de CAEA
 */
class CAEAServiceTest {

    private lateinit var mockAfipService: AfipService
    private lateinit var mockCaeaRepository: CaeaRepositoryPort

    @BeforeEach
    fun setup() {
        mockAfipService = mock(AfipService::class.java)
        mockCaeaRepository = mock(CaeaRepositoryPort::class.java)
    }

    @Test
    fun `solicitarCAEA should return successful response`() = runBlocking {
        // Arrange
        val puntoVenta = 2
        val periodo = "202507"
        val orden = 1
        
        val expectedResponse = AfipResponse.createApprovedWithCAEA(
            caea = "21234567890123",
            fechaVencimiento = LocalDate.now().plusDays(30),
            numeroComprobante = 0L,
            observaciones = listOf("CAEA obtenido exitosamente"),
            tipoOperacion = TipoOperacionAfip.CAEA_SOLICITUD
        )

        `when`(mockAfipService.solicitarCAEA(puntoVenta, periodo, orden))
            .thenReturn(expectedResponse)

        // Act
        val result = mockAfipService.solicitarCAEA(puntoVenta, periodo, orden)

        // Assert
        assertTrue(result.isApproved())
        assertEquals("21234567890123", result.cae)
        assertEquals(TipoOperacionAfip.CAEA_SOLICITUD, result.tipoOperacion)
        assertTrue(result.observaciones.contains("CAEA obtenido exitosamente"))
    }



    @Test
    fun `CAEA repository should find comprobantes by CAEA code`() {
        // Arrange
        val caeaCode = "21234567890123"
        val expectedComprobantes = listOf(
            ComprobanteCAEAInfo(
                numeroComprobante = 145L,
                tipoComprobante = "FACTURA_B",
                fechaEmision = LocalDate.now(),
                montoTotal = BigDecimal("1250.00"),
                montoNeto = BigDecimal("1033.06"),
                montoIva = BigDecimal("216.94"),
                montoTributos = BigDecimal("0.00"),
                montoTotalConceptos = BigDecimal("0.00"),
                montoExento = BigDecimal("0.00"),
                caeaUtilizado = caeaCode
            )
        )

        `when`(mockCaeaRepository.findComprobantesEmitidosConCAEA(caeaCode))
            .thenReturn(expectedComprobantes)

        // Act
        val result = mockCaeaRepository.findComprobantesEmitidosConCAEA(caeaCode)

        // Assert
        assertEquals(1, result.size)
        assertEquals(145L, result[0].numeroComprobante)
        assertEquals("FACTURA_B", result[0].tipoComprobante)
        assertEquals(caeaCode, result[0].caeaUtilizado)
    }

    @Test
    fun `CAEA repository should mark as informado correctly`() {
        // Arrange
        val caeaCode = "21234567890123"
        val tipoInforme = "SIN_MOVIMIENTO"

        // Act
        mockCaeaRepository.markAsInformado(caeaCode, tipoInforme)

        // Assert
        verify(mockCaeaRepository).markAsInformado(caeaCode, tipoInforme)
    }

    @Test
    fun `CAEA repository should check if already informado`() {
        // Arrange
        val caeaCode = "21234567890123"

        `when`(mockCaeaRepository.isInformado(caeaCode))
            .thenReturn(false)

        // Act
        val result = mockCaeaRepository.isInformado(caeaCode)

        // Assert
        assertFalse(result)
        verify(mockCaeaRepository).isInformado(caeaCode)
    }

    @Test
    fun `CAEA webservice models should validate correctly`() {
        // Test AfipCAEARequest validation
        assertDoesNotThrow {
            AfipCAEARequest(
                puntoVenta = 2,
                periodo = "202507",
                orden = 1,
                credentials = AfipCredentials("token", "sign", 20349249902L)
            )
        }

        // Test invalid periodo format
        assertThrows(IllegalArgumentException::class.java) {
            AfipCAEARequest(
                puntoVenta = 2,
                periodo = "2025", // Invalid format
                orden = 1,
                credentials = AfipCredentials("token", "sign", 20349249902L)
            )
        }

        // Test invalid orden
        assertThrows(IllegalArgumentException::class.java) {
            AfipCAEARequest(
                puntoVenta = 2,
                periodo = "202507",
                orden = 3, // Invalid orden (must be 1 or 2)
                credentials = AfipCredentials("token", "sign", 20349249902L)
            )
        }
    }

    @Test
    fun `WsfeCAEAResponse should convert to AfipResponse correctly`() {
        // Test successful response
        val successResponse = WsfeCAEAResponse(
            caea = "21234567890123",
            periodo = "202507",
            orden = 1,
            fechaDesde = "20250701",
            fechaHasta = "20250731",
            fechaTopeInforme = "20250810",
            resultado = "A",
            observaciones = listOf("CAEA aprobado")
        )

        val afipResponse = successResponse.toAfipResponse()
        assertTrue(afipResponse.isApproved())
        assertEquals("21234567890123", afipResponse.cae)

        // Test rejected response
        val rejectedResponse = WsfeCAEAResponse(
            caea = "ERROR",
            periodo = "202507",
            orden = 1,
            fechaDesde = "",
            fechaHasta = "",
            fechaTopeInforme = "",
            resultado = "R",
            observaciones = listOf("Error en solicitud")
        )

        val rejectedAfipResponse = rejectedResponse.toAfipResponse()
        assertTrue(rejectedAfipResponse.isRejected())
    }

    @Test
    fun `ComprobanteCAEAMovimiento should validate correctly`() {
        assertDoesNotThrow {
            ComprobanteCAEAMovimiento(
                tipoComprobante = TipoComprobanteAfip.FACTURA_B,
                numeroComprobante = 145L,
                fechaEmision = LocalDate.now(),
                montoTotal = BigDecimal("1250.00"),
                montoNeto = BigDecimal("1033.06"),
                montoIva = BigDecimal("216.94"),
                montoTributos = BigDecimal("0.00"),
                montoTotalConceptos = BigDecimal("0.00"),
                montoExento = BigDecimal("0.00"),
                caeaUtilizado = "21234567890123"
            )
        }

        // Test invalid numero comprobante
        assertThrows(IllegalArgumentException::class.java) {
            ComprobanteCAEAMovimiento(
                tipoComprobante = TipoComprobanteAfip.FACTURA_B,
                numeroComprobante = 0L, // Invalid
                fechaEmision = LocalDate.now(),
                montoTotal = BigDecimal("1250.00"),
                montoNeto = BigDecimal("1033.06"),
                montoIva = BigDecimal("216.94"),
                montoTributos = BigDecimal("0.00"),
                montoTotalConceptos = BigDecimal("0.00"),
                montoExento = BigDecimal("0.00"),
                caeaUtilizado = "21234567890123"
            )
        }

        // Test invalid CAEA length
        assertThrows(IllegalArgumentException::class.java) {
            ComprobanteCAEAMovimiento(
                tipoComprobante = TipoComprobanteAfip.FACTURA_B,
                numeroComprobante = 145L,
                fechaEmision = LocalDate.now(),
                montoTotal = BigDecimal("1250.00"),
                montoNeto = BigDecimal("1033.06"),
                montoIva = BigDecimal("216.94"),
                montoTributos = BigDecimal("0.00"),
                montoTotalConceptos = BigDecimal("0.00"),
                montoExento = BigDecimal("0.00"),
                caeaUtilizado = "123" // Invalid length
            )
        }
    }

    @Test
    fun `automatic CAEA inform should choose correct method based on comprobantes`() = runBlocking {
        // Test con AfipServiceAdapter mock para probar el método automático
        val mockAdapter = mock(com.gnico.majo.adapter.afip.AfipServiceAdapter::class.java)
        val caeaCode = "21234567890123"
        val puntoVenta = 2

        // Test: Sin comprobantes - debería usar FECAEASinMovimientoInformar
        val sinMovimientosResponse = AfipResponse.createApproved(
            observaciones = listOf("FECAEASinMovimientoInformar procesado correctamente"),
            TipoOperacionAfip.CAEA_INFORMATIVO
        )

        `when`(mockAdapter.informarMovimientosCAEAAutomatico(caeaCode, puntoVenta))
            .thenReturn(sinMovimientosResponse)

        val resultSinMovimientos = mockAdapter.informarMovimientosCAEAAutomatico(caeaCode, puntoVenta)
        assertTrue(resultSinMovimientos.isApproved())
        assertTrue(resultSinMovimientos.observaciones.any { it.contains("FECAEASinMovimientoInformar") })

        // Test: Con comprobantes - debería usar FECAEARegInformativo
        val conMovimientosResponse = AfipResponse.createApproved(
            observaciones = listOf("FECAEARegInformativo procesado - 5 comprobantes informados"),
            TipoOperacionAfip.CAEA_INFORMATIVO
        )

        `when`(mockAdapter.informarMovimientosCAEAAutomatico(caeaCode, puntoVenta))
            .thenReturn(conMovimientosResponse)

        val resultConMovimientos = mockAdapter.informarMovimientosCAEAAutomatico(caeaCode, puntoVenta)
        assertTrue(resultConMovimientos.isApproved())
        assertTrue(resultConMovimientos.observaciones.any { it.contains("FECAEARegInformativo") })
    }
}
