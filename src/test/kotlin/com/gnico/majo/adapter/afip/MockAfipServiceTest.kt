package com.gnico.majo.adapter.afip

import com.gnico.majo.application.domain.model.*
import com.gnico.majo.application.port.out.AfipService
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Assertions.*
import java.math.BigDecimal
import java.time.LocalDate

/**
 * Tests con mock service que no requieren conectividad real
 */
class MockAfipServiceTest {
    
    private lateinit var mockAfipService: MockAfipService
    private lateinit var testSale: Sale
    
    @BeforeEach
    fun setUp() {
        mockAfipService = MockAfipService()
        
        // Crear una venta de prueba
        val usuario = Usuario.create(
            username = "test_user",
            nombre = "Usuario Test",
            nombreDisplay = "Test User",
            activo = true
        )
        
        // Ya no manejamos clientes - solo consumidor final
        
        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto Test",
                cantidad = BigDecimal("2.0"),
                precioUnitario = BigDecimal("100.0"),
                tipoIva = TipoIva.IVA_21 // 21%
            )
        )
        
        testSale = Sale.create(
            usuario = usuario,
            items = items,
            medioPago = "EFECTIVO"
        )
    }
    
    @Test
    fun `test mock CAE success`() = runBlocking {
        mockAfipService.shouldSucceed = true
        
        val response = mockAfipService.solicitarCAE(
            sale = testSale,
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1
        )
        
        assertTrue(response.isApproved())
        assertTrue(response.isValid())
        assertEquals("AUTORIZADO", response.getEstadoDescriptivo())
        assertTrue(response.isOnlineOperation())
        assertTrue(response.cae.startsWith("MOCK"))
    }
    
    @Test
    fun `test mock CAE failure`() = runBlocking {
        mockAfipService.shouldSucceed = false
        
        val response = mockAfipService.solicitarCAE(
            sale = testSale,
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1
        )
        
        assertTrue(response.isRejected())
        assertFalse(response.isValid())
        assertEquals("RECHAZADO", response.getEstadoDescriptivo())
        assertTrue(response.observaciones.isNotEmpty())
    }
    
    @Test
    fun `test mock CAEA success`() = runBlocking {
        mockAfipService.shouldSucceed = true
        
        val response = mockAfipService.crearComprobanteConCAEA(
            sale = testSale,
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1
        )
        
        assertTrue(response.isApproved())
        assertTrue(response.isValid())
        assertEquals("AUTORIZADO_OFFLINE", response.getEstadoDescriptivo())
        assertTrue(response.isOfflineOperation())
        assertTrue(response.cae.startsWith("MOCK"))
    }
    
    @Test
    fun `test mock service availability`() = runBlocking {
        mockAfipService.isAvailable = true
        assertTrue(mockAfipService.isServiceAvailable())
        
        mockAfipService.isAvailable = false
        assertFalse(mockAfipService.isServiceAvailable())
    }
    
    @Test
    fun `test mock credentials`() = runBlocking {
        mockAfipService.shouldSucceed = true
        
        val credentials = mockAfipService.obtenerCredenciales()
        assertNotNull(credentials)
        assertTrue(credentials!!.isValid())
        assertEquals("MOCK_TOKEN", credentials.token)
        assertEquals("MOCK_SIGN", credentials.sign)
    }
    
    @Test
    fun `test mock last invoice number`() = runBlocking {
        val lastNumber = mockAfipService.getLastInvoiceNumber("FACTURA_B", 1)
        assertTrue(lastNumber > 0)
    }
}

/**
 * Mock implementation de AfipService para tests
 */
class MockAfipService : AfipService {
    
    var shouldSucceed = true
    var isAvailable = true
    
    override suspend fun solicitarCAE(
        sale: Sale,
        tipoComprobante: String,
        puntoVenta: Int,
        comprobanteAsociado: ComprobanteAsociado?
    ): AfipResponse {
        return if (shouldSucceed) {
            AfipResponse.createApprovedCAE(
                cae = "MOCK_CAE_${System.currentTimeMillis()}",
                fechaVencimiento = LocalDate.now().plusDays(10),
                numeroComprobante = System.currentTimeMillis() % 100000,
                observaciones = listOf("Mock CAE aprobado")
            )
        } else {
            AfipResponse.createRejected(
                observaciones = listOf("Mock error: Comprobante rechazado"),
                TipoOperacionAfip.CAE_ONLINE
            )
        }
    }
    
    override suspend fun crearComprobanteConCAEA(
        sale: Sale,
        tipoComprobante: String,
        puntoVenta: Int,
        comprobanteAsociado: ComprobanteAsociado?
    ): AfipResponse {
        return if (shouldSucceed) {
            AfipResponse.createApprovedCAEA(
                caea = "MOCK_CAEA_${System.currentTimeMillis()}",
                fechaVencimiento = LocalDate.now().plusDays(15),
                numeroComprobante = System.currentTimeMillis() % 100000,
                observaciones = listOf("Mock CAEA aprobado")
            )
        } else {
            AfipResponse.createRejected(
                observaciones = listOf("Mock error: CAEA rechazado"),
                TipoOperacionAfip.CAEA_OFFLINE
            )
        }
    }
    
    override suspend fun obtenerCredenciales(service: String): AfipCredentials? {
        return if (shouldSucceed) {
            AfipCredentials(
                token = "MOCK_TOKEN",
                sign = "MOCK_SIGN",
                cuit = 20349249902L
            )
        } else {
            null
        }
    }
    
    override suspend fun isServiceAvailable(): Boolean = isAvailable
    
    override suspend fun getLastInvoiceNumber(
        tipoComprobante: String,
        puntoVenta: Int
    ): Long = 12345L

    override suspend fun solicitarCAEA(puntoVenta: Int, periodo: String, orden: Int): AfipResponse {
        return if (shouldSucceed) {
            AfipResponse.createApprovedWithCAEA(
                caea = "21234567890123",
                fechaVencimiento = java.time.LocalDate.now().plusDays(30),
                numeroComprobante = 0L,
                observaciones = listOf("Mock CAEA obtenido exitosamente"),
                tipoOperacion = TipoOperacionAfip.CAEA_SOLICITUD
            )
        } else {
            AfipResponse.createRejected(
                observaciones = listOf("Mock error: CAEA rechazado"),
                TipoOperacionAfip.CAEA_SOLICITUD
            )
        }
    }

    override suspend fun informarMovimientosCAEAAutomatico(caeaCode: String, puntoVenta: Int): AfipResponse {
        return if (shouldSucceed) {
            AfipResponse.createApproved(
                observaciones = listOf("Mock CAEA informado automáticamente exitosamente"),
                TipoOperacionAfip.CAEA_INFORMATIVO
            )
        } else {
            AfipResponse.createRejected(
                observaciones = listOf("Mock error: Error al informar CAEA automáticamente"),
                TipoOperacionAfip.CAEA_INFORMATIVO
            )
        }
    }

    override suspend fun consultarCAEA(puntoVenta: Int, periodo: String, orden: Int): CaeaCode? {
        return if (shouldSucceed) {
            // Mock: simular CAEA encontrado en AFIP
            CaeaCode(
                id = null,
                caea = "21234567890123",
                puntoVenta = puntoVenta,
                periodo = periodo,
                fechaDesde = java.time.LocalDate.now(),
                fechaHasta = java.time.LocalDate.now().plusDays(15),
                orden = orden,
                estado = EstadoCaea.ACTIVO,
                ultimoNumeroFacturaB = 0L,
                ultimoNumeroNotaCreditoB = 0L,
                ultimoNumeroNotaDebitoB = 0L,
                creadoEn = java.time.LocalDateTime.now(),
                actualizadoEn = java.time.LocalDateTime.now()
            )
        } else {
            null
        }
    }
}
