package com.gnico.majo.domain

import com.gnico.majo.application.domain.model.Comprobante
import com.gnico.majo.application.domain.model.ComprobanteAsociado
import com.gnico.majo.application.domain.model.TipoAutorizacion
import com.gnico.majo.application.domain.model.Id
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

class ComprobanteAsociadoTest {

    @Test
    fun `debe crear ComprobanteAsociado correctamente`() {
        // Given
        val tipo = "FACTURA_B"
        val puntoVenta = 1
        val numeroComprobante = 123

        // When
        val comprobanteAsociado = ComprobanteAsociado.create(
            tipo = tipo,
            puntoVenta = puntoVenta,
            numeroComprobante = numeroComprobante
        )

        // Then
        assertEquals(tipo, comprobanteAsociado.tipo)
        assertEquals(puntoVenta, comprobanteAsociado.puntoVenta)
        assertEquals(numeroComprobante, comprobanteAsociado.numeroComprobante)
    }

    @Test
    fun `debe validar parametros al crear ComprobanteAsociado`() {
        // Should throw for invalid punto de venta
        assertThrows<IllegalArgumentException> {
            ComprobanteAsociado.create("FACTURA_B", 0, 123)
        }

        // Should throw for invalid numero comprobante
        assertThrows<IllegalArgumentException> {
            ComprobanteAsociado.create("FACTURA_B", 1, 0)
        }

        // Should throw for empty tipo
        assertThrows<IllegalArgumentException> {
            ComprobanteAsociado.create("", 1, 123)
        }
    }

    @Test
    fun `debe crear ComprobanteAsociado desde Comprobante`() {
        // Given
        val comprobante = createTestComprobante()

        // When
        val comprobanteAsociado = ComprobanteAsociado.fromComprobante(comprobante)

        // Then
        assertEquals(comprobante.tipoComprobante, comprobanteAsociado.tipo)
        assertEquals(comprobante.puntoVenta, comprobanteAsociado.puntoVenta)
        assertEquals(comprobante.numeroComprobante, comprobanteAsociado.numeroComprobante)
    }

    @Test
    fun `debe mapear tipos de comprobante a codigos AFIP correctamente`() {
        // Given & When & Then
        val facturaB = ComprobanteAsociado.create("FACTURA_B", 1, 123)
        assertEquals(6, facturaB.getTipoComprobanteAfipCodigo())

        val notaCreditoB = ComprobanteAsociado.create("NOTA_CREDITO_B", 1, 123)
        assertEquals(8, notaCreditoB.getTipoComprobanteAfipCodigo())

        val notaDebitoB = ComprobanteAsociado.create("NOTA_DEBITO_B", 1, 123)
        assertEquals(7, notaDebitoB.getTipoComprobanteAfipCodigo())
    }

    @Test
    fun `debe lanzar excepcion para tipo de comprobante no soportado`() {
        // Given
        val comprobanteAsociado = ComprobanteAsociado.create("TIPO_INVALIDO", 1, 123)

        // When & Then
        assertThrows<IllegalArgumentException> {
            comprobanteAsociado.getTipoComprobanteAfipCodigo()
        }
    }

    @Test
    fun `Comprobante debe verificar si requiere comprobante asociado`() {
        // Given
        val facturaB = createTestComprobante(tipoComprobante = "FACTURA_B")
        val notaCreditoB = createTestComprobante(tipoComprobante = "NOTA_CREDITO_B")
        val notaDebitoB = createTestComprobante(tipoComprobante = "NOTA_DEBITO_B")

        // When & Then
        assertTrue(!facturaB.requiereComprobanteAsociado())
        assertTrue(notaCreditoB.requiereComprobanteAsociado())
        assertTrue(notaDebitoB.requiereComprobanteAsociado())
    }

    @Test
    fun `Comprobante debe incluir comprobante asociado cuando se especifica`() {
        // Given
        val comprobanteAsociado = ComprobanteAsociado.create("FACTURA_B", 1, 123)
        
        // When
        val comprobante = createTestComprobante(
            tipoComprobante = "NOTA_CREDITO_B",
            comprobanteAsociado = comprobanteAsociado
        )

        // Then
        assertNotNull(comprobante.comprobanteAsociado)
        assertEquals("FACTURA_B", comprobante.comprobanteAsociado!!.tipo)
        assertEquals(1, comprobante.comprobanteAsociado!!.puntoVenta)
        assertEquals(123, comprobante.comprobanteAsociado!!.numeroComprobante)
    }

    private fun createTestComprobante(
        tipoComprobante: String = "FACTURA_B",
        comprobanteAsociado: ComprobanteAsociado? = null
    ): Comprobante {
        return Comprobante.fromPersistence(
            id = Id(1),
            venta = Id(1),
            tipoComprobante = tipoComprobante,
            puntoVenta = 1,
            numeroComprobante = 123,
            cae = "12345678901234",
            fechaEmision = LocalDateTime.now(),
            fechaVencimientoCae = LocalDate.now().plusDays(10),
            impTotal = BigDecimal("121.00"),
            impTotConc = BigDecimal.ZERO,
            impNeto = BigDecimal("100.00"),
            impIva = BigDecimal("21.00"),
            impTrib = BigDecimal.ZERO,
            monId = "PES",
            monCotiz = BigDecimal.ONE,
            estado = "EMITIDO",
            tipoAutorizacion = TipoAutorizacion.CAE,
            comprobanteAsociado = comprobanteAsociado,
            caeaUtilizado = null
        )
    }
}
