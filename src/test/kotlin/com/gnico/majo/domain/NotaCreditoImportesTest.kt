package com.gnico.majo.domain

import com.gnico.majo.application.domain.model.*
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

class NotaCreditoImportesTest {

    @Test
    fun `nota de credito debe usar exactamente los mismos importes que la factura original`() {
        // Given - Crear una factura original con importes específicos
        val facturaOriginal = createTestFactura(
            impTotal = BigDecimal("121.00"),
            impNeto = BigDecimal("100.00"),
            impIva = BigDecimal("21.00"),
            impTotConc = BigDecimal("0.00"),
            impTrib = BigDecimal("0.00")
        )
        
        val sale = createTestSale()
        val comprobanteAsociado = ComprobanteAsociado.fromComprobante(facturaOriginal)

        // When - Crear nota de crédito usando la factura original
        val notaCredito = Comprobante.createFromSale(
            sale = sale,
            tipoComprobante = "NOTA_CREDITO_B",
            puntoVenta = 1,
            ventaId = Id(1),
            numeroComprobante = 1001, // Número de prueba
            tipoAutorizacion = TipoAutorizacion.CAE,
            comprobanteAsociado = comprobanteAsociado,
            comprobanteOriginal = facturaOriginal
        )

        // Then - Los importes deben ser exactamente iguales
        assertEquals(facturaOriginal.impTotal, notaCredito.impTotal, "El importe total debe ser igual")
        assertEquals(facturaOriginal.impNeto, notaCredito.impNeto, "El importe neto debe ser igual")
        assertEquals(facturaOriginal.impIva, notaCredito.impIva, "El importe de IVA debe ser igual")
        assertEquals(facturaOriginal.impTotConc, notaCredito.impTotConc, "El importe total conceptos debe ser igual")
        assertEquals(facturaOriginal.impTrib, notaCredito.impTrib, "El importe tributos debe ser igual")
        
        // Verificar que tiene el comprobante asociado correcto
        assertNotNull(notaCredito.comprobanteAsociado)
        assertEquals("FACTURA_B", notaCredito.comprobanteAsociado!!.tipo)
        assertEquals(facturaOriginal.puntoVenta, notaCredito.comprobanteAsociado!!.puntoVenta)
        assertEquals(facturaOriginal.numeroComprobante, notaCredito.comprobanteAsociado!!.numeroComprobante)
    }

    @Test
    fun `factura debe calcular importes desde la venta normalmente`() {
        // Given
        val sale = createTestSale()

        // When - Crear factura SIN comprobante original (cálculo normal)
        val factura = Comprobante.createFromSale(
            sale = sale,
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1,
            ventaId = Id(1),
            numeroComprobante = 1000, // Número de prueba
            tipoAutorizacion = TipoAutorizacion.CAE,
            comprobanteAsociado = null,
            comprobanteOriginal = null
        )

        // Then - Los importes deben calcularse desde la venta
        val expectedTaxAmounts = sale.calculateTaxAmounts()
        assertEquals(expectedTaxAmounts.impTotal, factura.impTotal)
        assertEquals(expectedTaxAmounts.impNeto, factura.impNeto)
        assertEquals(expectedTaxAmounts.impIva, factura.impIva)
        assertEquals(expectedTaxAmounts.impTotConc, factura.impTotConc)
        assertEquals(expectedTaxAmounts.impTrib, factura.impTrib)
    }

    @Test
    fun `nota de credito sin comprobante original debe calcular desde venta`() {
        // Given
        val sale = createTestSale()

        // When - Crear nota de crédito SIN comprobante original (caso edge)
        val notaCredito = Comprobante.createFromSale(
            sale = sale,
            tipoComprobante = "NOTA_CREDITO_B",
            puntoVenta = 1,
            ventaId = Id(1),
            numeroComprobante = 1002, // Número de prueba
            tipoAutorizacion = TipoAutorizacion.CAE,
            comprobanteAsociado = null,
            comprobanteOriginal = null
        )

        // Then - Debe calcular desde la venta (fallback)
        val expectedTaxAmounts = sale.calculateTaxAmounts()
        assertEquals(expectedTaxAmounts.impTotal, notaCredito.impTotal)
        assertEquals(expectedTaxAmounts.impNeto, notaCredito.impNeto)
        assertEquals(expectedTaxAmounts.impIva, notaCredito.impIva)
    }

    @Test
    fun `nota de credito debe preservar importes exactos incluso con diferencias de redondeo`() {
        // Given - Factura con importes que podrían tener redondeo
        val facturaOriginal = createTestFactura(
            impTotal = BigDecimal("10000.00"),
            impNeto = BigDecimal("8264.46"),  // Estos son los valores del log
            impIva = BigDecimal("1735.54"),
            impTotConc = BigDecimal("0.00"),
            impTrib = BigDecimal("0.00")
        )
        
        val sale = createTestSale()
        val comprobanteAsociado = ComprobanteAsociado.fromComprobante(facturaOriginal)

        // When
        val notaCredito = Comprobante.createFromSale(
            sale = sale,
            tipoComprobante = "NOTA_CREDITO_B",
            puntoVenta = 1,
            ventaId = Id(1),
            numeroComprobante = 1003, // Número de prueba
            tipoAutorizacion = TipoAutorizacion.CAE,
            comprobanteAsociado = comprobanteAsociado,
            comprobanteOriginal = facturaOriginal
        )

        // Then - Debe usar exactamente los mismos valores, sin recalcular
        assertEquals(0, BigDecimal("10000.00").compareTo(notaCredito.impTotal))
        assertEquals(0, BigDecimal("8264.46").compareTo(notaCredito.impNeto))
        assertEquals(0, BigDecimal("1735.54").compareTo(notaCredito.impIva))
    }

    private fun createTestFactura(
        impTotal: BigDecimal,
        impNeto: BigDecimal,
        impIva: BigDecimal,
        impTotConc: BigDecimal,
        impTrib: BigDecimal
    ): Comprobante {
        return Comprobante.fromPersistence(
            id = Id(1),
            venta = Id(1),
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1,
            numeroComprobante = 6, // Número del log
            cae = "12345678901234",
            fechaEmision = LocalDateTime.now(),
            fechaVencimientoCae = LocalDate.now().plusDays(10),
            impTotal = impTotal,
            impTotConc = impTotConc,
            impNeto = impNeto,
            impIva = impIva,
            impTrib = impTrib,
            monId = "PES",
            monCotiz = BigDecimal.ONE,
            estado = "EMITIDO",
            tipoAutorizacion = TipoAutorizacion.CAE
        )
    }

    private fun createTestSale(): Sale {
        val usuario = Usuario("testuser", "Usuario Test", "Usuario Test", true)
        return Sale.create(
            usuario = usuario,
            items = listOf(
                SaleItem.create(
                    productoNombre = "Producto Test",
                    cantidad = BigDecimal.ONE,
                    precioUnitario = BigDecimal("121.00"),
                    tipoIva = TipoIva.IVA_21
                )
            ),
            medioPago = "EFECTIVO"
        )
    }
}
