package com.gnico.majo.domain.model

import com.gnico.majo.application.domain.model.Categoria
import com.gnico.majo.application.domain.model.Id
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

class CategoriaTest {

    @Test
    fun `should create categoria with valid color`() {
        // Arrange & Act
        val categoria = Categoria(
            id = Id(1),
            nombre = "Test Category",
            descripcion = "Test Description",
            color = "ff0000",
            activo = true
        )

        // Assert
        assertEquals("ff0000", categoria.color)
        assertEquals("Test Category", categoria.nombre)
    }

    @Test
    fun `should create categoria without color`() {
        // Arrange & Act
        val categoria = Categoria(
            id = Id(1),
            nombre = "Test Category",
            descripcion = "Test Description",
            color = null,
            activo = true
        )

        // Assert
        assertNull(categoria.color)
        assertEquals("Test Category", categoria.nombre)
    }

    @Test
    fun `should accept valid hex colors`() {
        val validColors = listOf("000000", "ffffff", "FF0000", "00ff00", "0000FF", "123abc", "ABC123")
        
        validColors.forEach { color ->
            assertDoesNotThrow {
                Categoria(
                    nombre = "Test",
                    color = color
                )
            }
        }
    }

    @Test
    fun `should set invalid hex colors to null`() {
        val invalidColors = listOf(
            "gggggg", // invalid characters
            "12345",  // too short
            "1234567", // too long
            "#ff0000", // with hash
            "ff00gg",  // invalid character
            "",        // empty
            " ff0000"  // with space
        )

        invalidColors.forEach { color ->
            val categoria = Categoria.create(
                nombre = "Test",
                color = color
            )
            assertNull(categoria.color, "Color '$color' should be set to null")
        }
    }

    @Test
    fun `create factory method should work with color`() {
        // Arrange & Act
        val categoria = Categoria.create(
            nombre = "Test Category",
            descripcion = "Test Description",
            color = "00ff00"
        )

        // Assert
        assertEquals("00ff00", categoria.color)
        assertEquals("Test Category", categoria.nombre)
        assertNotNull(categoria.creadoEn)
    }

    @Test
    fun `create factory method should work without color`() {
        // Arrange & Act
        val categoria = Categoria.create(
            nombre = "Test Category",
            descripcion = "Test Description"
        )

        // Assert
        assertNull(categoria.color)
        assertEquals("Test Category", categoria.nombre)
        assertNotNull(categoria.creadoEn)
    }

    @Test
    fun `sanitizeColor should return valid hex colors unchanged`() {
        val validColors = listOf("000000", "ffffff", "FF0000", "00ff00", "0000FF", "123abc", "ABC123")

        validColors.forEach { color ->
            assertEquals(color, Categoria.sanitizeColor(color))
        }
    }

    @Test
    fun `sanitizeColor should return null for invalid hex colors`() {
        val invalidColors = listOf(
            "gggggg", // invalid characters
            "12345",  // too short
            "1234567", // too long
            "#ff0000", // with hash
            "ff00gg",  // invalid character
            "",        // empty
            " ff0000"  // with space
        )

        invalidColors.forEach { color ->
            assertNull(Categoria.sanitizeColor(color), "Color '$color' should be sanitized to null")
        }
    }

    @Test
    fun `sanitizeColor should return null for null input`() {
        assertNull(Categoria.sanitizeColor(null))
    }

    @Test
    fun `create should handle invalid color gracefully by setting it to null`() {
        // Test that creating a categoria with invalid color doesn't throw exception
        // but sets color to null instead
        val categoria = Categoria.create(
            nombre = "Test Category",
            descripcion = "Test Description",
            color = "invalid-color"
        )

        assertNull(categoria.color, "Invalid color should be set to null")
        assertEquals("Test Category", categoria.nombre)
        assertEquals("Test Description", categoria.descripcion)
    }
}
