package com.gnico.majo.domain.model

import com.gnico.majo.application.domain.model.Sale
import com.gnico.majo.application.domain.model.SaleItem
import com.gnico.majo.application.domain.model.TipoIva
import com.gnico.majo.application.domain.model.Usuario
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlin.test.assertFalse

class SaleWithDiscountTest {

    private val testUsuario = Usuario(
        username = "test_user",
        nombre = "Test User",
        nombreDisplay = "Test User",
        activo = true
    )

    @Test
    fun `should create sale with discount and calculate correct total`() {
        // Given
        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto 1",
                cantidad = BigDecimal("2"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21,
                porcentajeDescuento = BigDecimal("10") // 10% descuento aplicado al item
            ),
            SaleItem.create(
                productoNombre = "Producto 2",
                cantidad = BigDecimal("1"),
                precioUnitario = BigDecimal("50.00"),
                tipoIva = TipoIva.IVA_21,
                porcentajeDescuento = BigDecimal("10") // 10% descuento aplicado al item
            )
        )

        // When
        val sale = Sale.create(
            usuario = testUsuario,
            items = items,
            medioPago = "EFECTIVO",
            porcentajeDescuento = BigDecimal("10")
        )

        // Then
        // Cada item tiene 10% descuento aplicado:
        // Item 1: 2 * 100 = 200, con 10% desc = 180
        // Item 2: 1 * 50 = 50, con 10% desc = 45
        // Total: 180 + 45 = 225
        assertEquals(BigDecimal("225.00"), sale.montoTotal)
        assertEquals(BigDecimal("10"), sale.porcentajeDescuento)
    }

    @Test
    fun `should create sale without discount`() {
        // Given
        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto 1",
                cantidad = BigDecimal("2"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21
            )
        )

        // When
        val sale = Sale.create(
            usuario = testUsuario,
            items = items,
            medioPago = "EFECTIVO"
        )

        // Then
        assertEquals(BigDecimal("200.00"), sale.montoTotal)
        assertEquals(null, sale.porcentajeDescuento)
    }

    @Test
    fun `should reject invalid discount percentage - negative`() {
        // Given
        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto 1",
                cantidad = BigDecimal("1"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21
            )
        )

        // When & Then
        assertThrows<IllegalArgumentException> {
            Sale.create(
                usuario = testUsuario,
                items = items,
                medioPago = "EFECTIVO",
                porcentajeDescuento = BigDecimal("-5")
            )
        }
    }

    @Test
    fun `should reject invalid discount percentage - over 100`() {
        // Given
        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto 1",
                cantidad = BigDecimal("1"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21
            )
        )

        // When & Then
        assertThrows<IllegalArgumentException> {
            Sale.create(
                usuario = testUsuario,
                items = items,
                medioPago = "EFECTIVO",
                porcentajeDescuento = BigDecimal("101")
            )
        }
    }

    @Test
    fun `should create sale item with discount and calculate correct subtotal`() {
        // Given
        val productoNombre = "Producto Test"
        val cantidad = BigDecimal("2")
        val precioUnitario = BigDecimal("100.00")
        val porcentajeDescuento = BigDecimal("20") // 20% descuento

        // When
        val saleItem = SaleItem.create(
            productoNombre = productoNombre,
            cantidad = cantidad,
            precioUnitario = precioUnitario,
            tipoIva = TipoIva.IVA_21,
            porcentajeDescuento = porcentajeDescuento
        )

        // Then
        // Subtotal base: 2 * 100 = 200
        // Con 20% descuento: 200 - 40 = 160
        assertEquals(BigDecimal("160.00"), saleItem.subtotal)

        // Verificar que los cálculos de IVA se basan en el subtotal con descuento
        val expectedDivisor = BigDecimal("1.21") // 1 + 21%
        val expectedBaseImp = BigDecimal("160.00").divide(expectedDivisor, 2, java.math.RoundingMode.HALF_UP)
        val expectedImporteIva = BigDecimal("160.00").subtract(expectedBaseImp)

        assertEquals(expectedBaseImp, saleItem.baseImp)
        assertEquals(expectedImporteIva, saleItem.importeIva)
    }

    @Test
    fun `should create sale item without discount and calculate correct subtotal`() {
        // Given
        val productoNombre = "Producto Test"
        val cantidad = BigDecimal("2")
        val precioUnitario = BigDecimal("100.00")

        // When
        val saleItem = SaleItem.create(
            productoNombre = productoNombre,
            cantidad = cantidad,
            precioUnitario = precioUnitario,
            tipoIva = TipoIva.IVA_21
        )

        // Then
        // Subtotal: 2 * 100 = 200
        assertEquals(BigDecimal("200.00"), saleItem.subtotal)

        // Verificar que los cálculos de IVA se basan en el subtotal
        val expectedDivisor = BigDecimal("1.21") // 1 + 21%
        val expectedBaseImp = BigDecimal("200.00").divide(expectedDivisor, 2, java.math.RoundingMode.HALF_UP)
        val expectedImporteIva = BigDecimal("200.00").subtract(expectedBaseImp)

        assertEquals(expectedBaseImp, saleItem.baseImp)
        assertEquals(expectedImporteIva, saleItem.importeIva)
    }

    @Test
    fun `should handle zero discount correctly`() {
        // Given
        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto 1",
                cantidad = BigDecimal("1"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21,
                porcentajeDescuento = BigDecimal.ZERO
            )
        )

        // When
        val sale = Sale.create(
            usuario = testUsuario,
            items = items,
            medioPago = "EFECTIVO",
            porcentajeDescuento = BigDecimal.ZERO
        )

        // Then
        assertEquals(BigDecimal("100.00"), sale.montoTotal)
        assertEquals(BigDecimal.ZERO, sale.porcentajeDescuento)
    }

    @Test
    fun `should handle maximum discount correctly`() {
        // Given
        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto 1",
                cantidad = BigDecimal("1"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21,
                porcentajeDescuento = BigDecimal("100") // 100% descuento aplicado al item
            )
        )

        // When
        val sale = Sale.create(
            usuario = testUsuario,
            items = items,
            medioPago = "EFECTIVO",
            porcentajeDescuento = BigDecimal("100")
        )

        // Then
        assertEquals(BigDecimal("0.00"), sale.montoTotal)
        assertEquals(BigDecimal("100"), sale.porcentajeDescuento)
    }

    @Test
    fun `should calculate discount correctly with multiple items`() {
        // Given
        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto A",
                cantidad = BigDecimal("3"),
                precioUnitario = BigDecimal("50.00"),
                tipoIva = TipoIva.IVA_21,
                porcentajeDescuento = BigDecimal("15") // 15% descuento aplicado al item
            ),
            SaleItem.create(
                productoNombre = "Producto B",
                cantidad = BigDecimal("2"),
                precioUnitario = BigDecimal("75.00"),
                tipoIva = TipoIva.IVA_10_5,
                porcentajeDescuento = BigDecimal("15") // 15% descuento aplicado al item
            )
        )

        // When
        val sale = Sale.create(
            usuario = testUsuario,
            items = items,
            medioPago = "EFECTIVO",
            porcentajeDescuento = BigDecimal("15") // 15% descuento
        )

        // Then
        // Cada item tiene 15% descuento aplicado:
        // Item A: 3 * 50 = 150, con 15% desc = 127.50
        // Item B: 2 * 75 = 150, con 15% desc = 127.50
        // Total: 127.50 + 127.50 = 255.00
        assertEquals(BigDecimal("255.00"), sale.montoTotal)
        assertEquals(BigDecimal("15"), sale.porcentajeDescuento)
    }

    @Test
    fun `should calculate subtotal sin descuento correctly`() {
        // Given
        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto 1",
                cantidad = BigDecimal("2"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21,
                porcentajeDescuento = BigDecimal("10")
            ),
            SaleItem.create(
                productoNombre = "Producto 2",
                cantidad = BigDecimal("1"),
                precioUnitario = BigDecimal("50.00"),
                tipoIva = TipoIva.IVA_21,
                porcentajeDescuento = BigDecimal("10")
            )
        )

        val sale = Sale.create(
            usuario = testUsuario,
            items = items,
            medioPago = "EFECTIVO",
            porcentajeDescuento = BigDecimal("10")
        )

        // When
        val subtotalSinDescuento = sale.calculateSubtotalSinDescuento()
        val montoDescuento = sale.calculateMontoDescuento()

        // Then
        // Subtotal sin descuento: (2 * 100) + (1 * 50) = 250
        assertEquals(BigDecimal("250.00"), subtotalSinDescuento)
        // Monto descuento: 250 - 225 = 25
        assertEquals(BigDecimal("25.00"), montoDescuento)
        // Verificar que tiene descuento
        assertTrue(sale.hasDescuento())
    }

    @Test
    fun `should handle sale without discount for new methods`() {
        // Given
        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto 1",
                cantidad = BigDecimal("2"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21
            )
        )

        val sale = Sale.create(
            usuario = testUsuario,
            items = items,
            medioPago = "EFECTIVO"
        )

        // When
        val subtotalSinDescuento = sale.calculateSubtotalSinDescuento()
        val montoDescuento = sale.calculateMontoDescuento()

        // Then
        // Sin descuento, subtotal sin descuento = monto total
        assertEquals(BigDecimal("200.00"), subtotalSinDescuento)
        assertEquals(sale.montoTotal, subtotalSinDescuento)
        // Monto descuento debe ser cero
        assertEquals(BigDecimal.ZERO, montoDescuento)
        // No debe tener descuento
        assertFalse(sale.hasDescuento())
    }
}
