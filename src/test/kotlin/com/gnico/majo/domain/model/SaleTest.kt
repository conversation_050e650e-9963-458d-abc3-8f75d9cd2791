package com.gnico.majo.domain.model

import com.gnico.majo.application.domain.model.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class SaleTest {

    @Test
    fun `should create sale with valid data`() {
        // Given
        val usuario = Usuario("user1", "Usuario Test", "Usuario Test", true)
        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto Test",
                cantidad = BigDecimal("2.0"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21
            )
        )

        // When
        val sale = Sale.create(
            usuario = usuario,
            items = items,
            medioPago = "EFECTIVO"
        )

        // Then
        assertEquals(usuario, sale.usuario)
        assertEquals(items, sale.items)
        assertEquals("EFECTIVO", sale.medioPago)
        assertFalse(sale.comprobanteEmitido)
        assertTrue(sale.numeroVenta.startsWith("V-"))
        assertEquals(0, BigDecimal("200.00").compareTo(sale.montoTotal)) // Precio con IVA incluido
    }

    @Test
    fun `should fail when creating sale with empty items`() {
        // Given
        val usuario = Usuario("user1", "Usuario Test", "Usuario Test", true)
        val items = emptyList<SaleItem>()

        // When & Then
        assertThrows<IllegalArgumentException> {
            Sale.create(
                usuario = usuario,
                items = items,
                medioPago = "EFECTIVO"
            )
        }
    }

    @Test
    fun `should fail when creating sale with invalid medio pago`() {
        // Given
        val usuario = Usuario("user1", "Usuario Test", "Usuario Test", true)
        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto Test",
                cantidad = BigDecimal("1.0"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21
            )
        )

        // When & Then
        assertThrows<IllegalArgumentException> {
            Sale.create(
                usuario = usuario,
                items = items,
                medioPago = "MEDIO_INVALIDO"
            )
        }
    }

    @Test
    fun `should validate comprobante creation correctly`() {
        // Given
        val usuario = Usuario("user1", "Usuario Test", "Usuario Test", true)
        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto Test",
                cantidad = BigDecimal("1.0"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21
            )
        )

        val sale = Sale.create(usuario, items, "EFECTIVO")

        // When & Then - Solo permitir comprobantes tipo B
        assertTrue(sale.canCreateComprobante("FACTURA_B"))
        assertTrue(sale.canCreateComprobante("NOTA_CREDITO_B"))
        assertTrue(sale.canCreateComprobante("NOTA_DEBITO_B"))
        assertFalse(sale.canCreateComprobante("FACTURA_A"))

        // Should not throw - tipos B válidos
        sale.validateComprobanteCreation("FACTURA_B")
        sale.validateComprobanteCreation("NOTA_CREDITO_B")
        sale.validateComprobanteCreation("NOTA_DEBITO_B")

        // Should throw - tipos no permitidos
        assertThrows<IllegalArgumentException> {
            sale.validateComprobanteCreation("FACTURA_A")
        }
    }

    @Test
    fun `should calculate tax amounts correctly`() {
        // Given
        val usuario = Usuario("user1", "Usuario Test", "Usuario Test", true)
        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto Test 1",
                cantidad = BigDecimal("2.0"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21
            ),
            SaleItem.create(
                productoNombre = "Producto Test 2",
                cantidad = BigDecimal("1.0"),
                precioUnitario = BigDecimal("50.00"),
                tipoIva = TipoIva.IVA_10_5
            )
        )

        val sale = Sale.create(usuario, items, "EFECTIVO")

        // When
        val taxAmounts = sale.calculateTaxAmounts()

        // Then
        // Con precios que incluyen IVA: 200 + 50 = 250 total
        // Base imponible: 200/1.21 + 50/1.105 ≈ 165.29 + 45.25 = 210.54
        // IVA: 250 - 210.54 = 39.46
        assertEquals(0, BigDecimal("210.54").compareTo(taxAmounts.impNeto.setScale(2, java.math.RoundingMode.HALF_UP)))
        assertEquals(0, BigDecimal("39.46").compareTo(taxAmounts.impIva.setScale(2, java.math.RoundingMode.HALF_UP)))
        assertEquals(0, BigDecimal("250.00").compareTo(taxAmounts.impTotal))
        assertEquals(0, BigDecimal.ZERO.compareTo(taxAmounts.impTotConc))
        assertEquals(0, BigDecimal.ZERO.compareTo(taxAmounts.impTrib))
    }
}
