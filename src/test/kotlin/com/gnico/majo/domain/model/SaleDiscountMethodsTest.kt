package com.gnico.majo.domain.model

import com.gnico.majo.application.domain.model.Sale
import com.gnico.majo.application.domain.model.SaleItem
import com.gnico.majo.application.domain.model.TipoIva
import com.gnico.majo.application.domain.model.Usuario
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlin.test.assertFalse

class SaleDiscountMethodsTest {

    private val testUsuario = Usuario(
        username = "test_user",
        nombre = "Test User",
        nombreDisplay = "Test User",
        activo = true
    )

    @Test
    fun `should calculate subtotal sin descuento correctly`() {
        // Given
        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto 1",
                cantidad = BigDecimal("2"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21,
                porcentajeDescuento = BigDecimal("10")
            ),
            SaleItem.create(
                productoNombre = "Producto 2",
                cantidad = BigDecimal("1"),
                precioUnitario = BigDecimal("50.00"),
                tipoIva = TipoIva.IVA_21,
                porcentajeDescuento = BigDecimal("10")
            )
        )

        val sale = Sale.create(
            usuario = testUsuario,
            items = items,
            medioPago = "EFECTIVO",
            porcentajeDescuento = BigDecimal("10")
        )

        // When
        val subtotalSinDescuento = sale.calculateSubtotalSinDescuento()
        val montoDescuento = sale.calculateMontoDescuento()

        // Then
        // Subtotal sin descuento: (2 * 100) + (1 * 50) = 250
        assertEquals(BigDecimal("250.00"), subtotalSinDescuento)
        // Monto descuento: 250 - 225 = 25
        assertEquals(BigDecimal("25.00"), montoDescuento)
        // Verificar que tiene descuento
        assertTrue(sale.hasDescuento())
    }

    @Test
    fun `should handle sale without discount for new methods`() {
        // Given
        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto 1",
                cantidad = BigDecimal("2"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21
            )
        )

        val sale = Sale.create(
            usuario = testUsuario,
            items = items,
            medioPago = "EFECTIVO"
        )

        // When
        val subtotalSinDescuento = sale.calculateSubtotalSinDescuento()
        val montoDescuento = sale.calculateMontoDescuento()

        // Then
        // Sin descuento, subtotal sin descuento = monto total
        assertEquals(BigDecimal("200.00"), subtotalSinDescuento)
        assertEquals(sale.montoTotal, subtotalSinDescuento)
        // Monto descuento debe ser cero
        assertEquals(BigDecimal.ZERO, montoDescuento)
        // No debe tener descuento
        assertFalse(sale.hasDescuento())
    }

    @Test
    fun `should handle zero discount correctly`() {
        // Given
        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto 1",
                cantidad = BigDecimal("1"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21,
                porcentajeDescuento = BigDecimal.ZERO
            )
        )

        val sale = Sale.create(
            usuario = testUsuario,
            items = items,
            medioPago = "EFECTIVO",
            porcentajeDescuento = BigDecimal.ZERO
        )

        // When & Then
        assertEquals(BigDecimal("100.00"), sale.montoTotal)
        assertEquals(BigDecimal("100.00"), sale.calculateSubtotalSinDescuento())
        assertEquals(BigDecimal.ZERO, sale.calculateMontoDescuento())
        assertFalse(sale.hasDescuento())
    }

    @Test
    fun `should handle maximum discount correctly`() {
        // Given
        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto 1",
                cantidad = BigDecimal("1"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21,
                porcentajeDescuento = BigDecimal("100")
            )
        )

        val sale = Sale.create(
            usuario = testUsuario,
            items = items,
            medioPago = "EFECTIVO",
            porcentajeDescuento = BigDecimal("100")
        )

        // When & Then
        assertEquals(BigDecimal("0.00"), sale.montoTotal)
        assertEquals(BigDecimal("100.00"), sale.calculateSubtotalSinDescuento())
        assertEquals(BigDecimal("100.00"), sale.calculateMontoDescuento())
        assertTrue(sale.hasDescuento())
    }
}
