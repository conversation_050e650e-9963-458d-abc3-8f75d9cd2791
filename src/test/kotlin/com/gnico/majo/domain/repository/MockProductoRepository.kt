package com.gnico.majo.domain.repository

import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Producto
import com.gnico.majo.application.port.out.ProductoRepositoryPort
import java.util.concurrent.ConcurrentHashMap

/**
 * Implementación en memoria del repositorio de productos para tests
 */
class MockProductoRepository : ProductoRepositoryPort {
    private val productos = ConcurrentHashMap<Int, Producto>()

    override fun findAll(): List<Producto> {
        return productos.values.filter { it.activo }.toList()
    }

    override fun findByCodigo(codigo: Int): Producto? {
        return productos[codigo]
    }

    override fun save(producto: Producto): Int {
        productos[producto.codigo] = producto
        return producto.codigo
    }

    override fun update(producto: Producto): Boolean {
        if (!productos.containsKey(producto.codigo)) {
            return false
        }
        productos[producto.codigo] = producto
        return true
    }

    override fun delete(codigo: Int): Boolean {
        return productos.remove(codigo) != null
    }

    override fun deleteAll(): Int {
        val count = productos.size
        productos.clear()
        return count
    }

    override fun saveAll(productos: List<Producto>): Int {
        productos.forEach { producto ->
            this.productos[producto.codigo] = producto
        }
        return productos.size
    }

    override fun deleteMultiple(codigos: List<Int>): Int {
        var deletedCount = 0
        codigos.forEach { codigo ->
            if (productos.remove(codigo) != null) {
                deletedCount++
            }
        }
        return deletedCount
    }

    override fun updateMultiple(
        codigos: List<Int>,
        categoriaId: Id?,
        precioUnitario: java.math.BigDecimal?,
        unidadMedidaId: Id?,
        stockActual: Int?
    ): Int {
        var updatedCount = 0
        codigos.forEach { codigo ->
            productos[codigo]?.let { producto ->
                // Manejar valores especiales para limpiar campos opcionales
                val newCategoriaId = when {
                    categoriaId == null -> producto.categoria
                    categoriaId.value == -1 -> null // Valor especial para limpiar
                    else -> categoriaId
                }

                val newStockActual = when {
                    stockActual == null -> producto.stockActual
                    stockActual == -1 -> null // Valor especial para limpiar
                    else -> stockActual
                }

                val updatedProducto = Producto.create(
                    codigo = producto.codigo,
                    nombre = producto.nombre,
                    descripcion = producto.descripcion,
                    unidadMedida = unidadMedidaId ?: producto.unidadMedida,
                    tipoIva = producto.tipoIva,
                    categoria = newCategoriaId,
                    precioUnitario = precioUnitario ?: producto.precioUnitario,
                    stockActual = newStockActual,
                    activo = producto.activo,
                    creadoEn = producto.creadoEn,
                    actualizadoEn = java.time.LocalDateTime.now()
                )
                productos[codigo] = updatedProducto
                updatedCount++
            }
        }
        return updatedCount
    }

    fun clear() {
        productos.clear()
    }
}
