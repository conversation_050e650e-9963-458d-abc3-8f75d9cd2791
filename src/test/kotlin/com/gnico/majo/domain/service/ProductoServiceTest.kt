package com.gnico.majo.domain.service

import com.gnico.majo.application.port.`in`.ProductoService
import com.gnico.majo.application.usecase.ProductoServiceImpl
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Producto
import com.gnico.majo.application.domain.model.TipoIva
import com.gnico.majo.domain.repository.MockProductoRepository
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal

class ProductoServiceTest {

    private lateinit var mockRepository: MockProductoRepository
    private lateinit var productoService: ProductoService

    @BeforeEach
    fun setup() {
        mockRepository = MockProductoRepository()
        productoService = ProductoServiceImpl(mockRepository)
    }

    @AfterEach
    fun tearDown() {
        mockRepository.clear()
    }

    @Test
    fun `createProducto should save a new product and return its codigo`() {
        // Arrange
        val producto = createTestProducto()

        // Act
        val codigo = productoService.createProducto(producto)

        // Assert
        assertNotNull(codigo)
        assertEquals(1234, codigo)

        val savedProducto = mockRepository.findByCodigo(codigo)
        assertNotNull(savedProducto)
        assertEquals(producto.codigo, savedProducto?.codigo)
        assertEquals(producto.nombre, savedProducto?.nombre)
    }

    @Test
    fun `createProducto should throw exception when product with same code already exists`() {
        // Arrange
        val producto1 = createTestProducto(codigo = 1001)
        productoService.createProducto(producto1)

        val producto2 = createTestProducto(codigo = 1001, nombre = "Otro Producto")

        // Act & Assert
        val exception = assertThrows<IllegalArgumentException> {
            productoService.createProducto(producto2)
        }
        assertTrue(exception.message?.contains("Ya existe un producto con el código") ?: false)
    }

    @Test
    fun `getProductoByCodigo should return product when it exists`() {
        // Arrange
        val producto = createTestProducto(codigo = 1001)
        productoService.createProducto(producto)

        // Act
        val foundProducto = productoService.getProductoByCodigo(1001)

        // Assert
        assertNotNull(foundProducto)
        assertEquals(1001, foundProducto?.codigo)
    }

    @Test
    fun `getProductoByCodigo should return null when product does not exist`() {
        // Act
        val foundProducto = productoService.getProductoByCodigo(9999)

        // Assert
        assertNull(foundProducto)
    }

    @Test
    fun `getAllProductos should return all active products`() {
        // Arrange
        val producto1 = createTestProducto(codigo = 1, nombre = "Producto 1")
        val producto2 = createTestProducto(codigo = 2, nombre = "Producto 2")
        val producto3 = createTestProducto(codigo = 3, nombre = "Producto 3")

        productoService.createProducto(producto1)
        productoService.createProducto(producto2)
        productoService.createProducto(producto3)

        // Act
        val allProductos = productoService.getAllProductos()

        // Assert
        assertEquals(3, allProductos.size)
        assertTrue(allProductos.any { it.codigo == 1 })
        assertTrue(allProductos.any { it.codigo == 2 })
        assertTrue(allProductos.any { it.codigo == 3 })
    }

    @Test
    fun `updateProducto should update an existing product`() {
        // Arrange
        val producto = createTestProducto()
        productoService.createProducto(producto)

        val updatedProducto = Producto.create(
            codigo = producto.codigo,
            nombre = "Nombre Actualizado",
            descripcion = "Descripción Actualizada",
            unidadMedida = producto.unidadMedida,
            tipoIva = producto.tipoIva,
            precioUnitario = BigDecimal("150.00"),
            stockActual = 25
        )

        // Act
        val result = productoService.updateProducto(updatedProducto)

        // Assert
        assertTrue(result)

        val retrievedProducto = productoService.getProductoByCodigo(producto.codigo)
        assertNotNull(retrievedProducto)
        assertEquals("Nombre Actualizado", retrievedProducto?.nombre)
        assertEquals("Descripción Actualizada", retrievedProducto?.descripcion)
        assertEquals(BigDecimal("150.00"), retrievedProducto?.precioUnitario)
        assertEquals(25, retrievedProducto?.stockActual)
    }

    @Test
    fun `deleteProducto should remove a product completely`() {
        // Arrange
        val producto = createTestProducto()
        val codigo = productoService.createProducto(producto)

        // Act
        val result = productoService.deleteProducto(codigo)

        // Assert
        assertTrue(result)

        // El producto debería haber sido eliminado completamente
        val deletedProducto = mockRepository.findByCodigo(codigo)
        assertNull(deletedProducto)

        // El producto no debería aparecer en la lista de productos
        val allProductos = productoService.getAllProductos()
        assertFalse(allProductos.any { it.codigo == codigo })
    }

    @Test
    fun `deleteProducto should return false when product does not exist`() {
        // Act
        val result = productoService.deleteProducto(9999)

        // Assert
        assertFalse(result)
    }

    @Test
    fun `deleteMultipleProductos should delete multiple products successfully`() {
        // Arrange
        val producto1 = createTestProducto(codigo = 1001, nombre = "Producto 1")
        val producto2 = createTestProducto(codigo = 1002, nombre = "Producto 2")
        val producto3 = createTestProducto(codigo = 1003, nombre = "Producto 3")

        productoService.createProducto(producto1)
        productoService.createProducto(producto2)
        productoService.createProducto(producto3)

        val codigos = listOf(1001, 1002, 1003)

        // Act
        val result = productoService.deleteMultipleProductos(codigos)

        // Assert
        assertEquals(3, result)

        // Verificar que los productos fueron eliminados
        assertNull(productoService.getProductoByCodigo(1001))
        assertNull(productoService.getProductoByCodigo(1002))
        assertNull(productoService.getProductoByCodigo(1003))
    }

    @Test
    fun `deleteMultipleProductos should handle empty list`() {
        // Act
        val result = productoService.deleteMultipleProductos(emptyList())

        // Assert
        assertEquals(0, result)
    }

    @Test
    fun `deleteMultipleProductos should handle non-existent products`() {
        // Act
        val result = productoService.deleteMultipleProductos(listOf(9999, 9998, 9997))

        // Assert
        assertEquals(0, result)
    }

    @Test
    fun `deleteMultipleProductos should handle partial deletion`() {
        // Arrange
        val producto1 = createTestProducto(codigo = 1001, nombre = "Producto 1")
        val producto2 = createTestProducto(codigo = 1002, nombre = "Producto 2")

        productoService.createProducto(producto1)
        productoService.createProducto(producto2)

        val codigos = listOf(1001, 9999, 1002) // 9999 no existe

        // Act
        val result = productoService.deleteMultipleProductos(codigos)

        // Assert
        assertEquals(2, result) // Solo se eliminan los que existen

        // Verificar que los productos existentes fueron eliminados
        assertNull(productoService.getProductoByCodigo(1001))
        assertNull(productoService.getProductoByCodigo(1002))
    }

    @Test
    fun `updateMultipleProductos should update multiple products successfully`() {
        // Arrange
        val producto1 = createTestProducto(codigo = 1001, nombre = "Producto 1", precioUnitario = BigDecimal("100.00"))
        val producto2 = createTestProducto(codigo = 1002, nombre = "Producto 2", precioUnitario = BigDecimal("200.00"))
        val producto3 = createTestProducto(codigo = 1003, nombre = "Producto 3", precioUnitario = BigDecimal("300.00"))

        productoService.createProducto(producto1)
        productoService.createProducto(producto2)
        productoService.createProducto(producto3)

        val codigos = listOf(1001, 1002, 1003)
        val nuevoPrecio = BigDecimal("150.00")
        val nuevoStock = 25

        // Act
        val result = productoService.updateMultipleProductos(
            codigos = codigos,
            precioUnitario = nuevoPrecio,
            stockActual = nuevoStock
        )

        // Assert
        assertEquals(3, result)

        // Verificar que los productos fueron actualizados
        val updatedProducto1 = productoService.getProductoByCodigo(1001)
        val updatedProducto2 = productoService.getProductoByCodigo(1002)
        val updatedProducto3 = productoService.getProductoByCodigo(1003)

        assertNotNull(updatedProducto1)
        assertNotNull(updatedProducto2)
        assertNotNull(updatedProducto3)

        assertEquals(nuevoPrecio, updatedProducto1?.precioUnitario)
        assertEquals(nuevoPrecio, updatedProducto2?.precioUnitario)
        assertEquals(nuevoPrecio, updatedProducto3?.precioUnitario)

        assertEquals(nuevoStock, updatedProducto1?.stockActual)
        assertEquals(nuevoStock, updatedProducto2?.stockActual)
        assertEquals(nuevoStock, updatedProducto3?.stockActual)
    }

    @Test
    fun `updateMultipleProductos should handle empty list`() {
        // Act
        val result = productoService.updateMultipleProductos(
            codigos = emptyList(),
            precioUnitario = BigDecimal("100.00")
        )

        // Assert
        assertEquals(0, result)
    }

    @Test
    fun `updateMultipleProductos should handle non-existent products`() {
        // Act
        val result = productoService.updateMultipleProductos(
            codigos = listOf(9999, 9998, 9997),
            stockActual = 50
        )

        // Assert
        assertEquals(0, result)
    }

    @Test
    fun `updateMultipleProductos should handle partial update`() {
        // Arrange
        val producto1 = createTestProducto(codigo = 1001, nombre = "Producto 1")
        val producto2 = createTestProducto(codigo = 1002, nombre = "Producto 2")

        productoService.createProducto(producto1)
        productoService.createProducto(producto2)

        val codigos = listOf(1001, 9999, 1002) // 9999 no existe
        val nuevaCategoria = com.gnico.majo.application.domain.model.Id(5)

        // Act
        val result = productoService.updateMultipleProductos(
            codigos = codigos,
            categoriaId = nuevaCategoria
        )

        // Assert
        assertEquals(2, result) // Solo se actualizan los que existen

        // Verificar que los productos existentes fueron actualizados
        val updatedProducto1 = productoService.getProductoByCodigo(1001)
        val updatedProducto2 = productoService.getProductoByCodigo(1002)

        assertNotNull(updatedProducto1)
        assertNotNull(updatedProducto2)
        assertEquals(nuevaCategoria, updatedProducto1?.categoria)
        assertEquals(nuevaCategoria, updatedProducto2?.categoria)
    }

    @Test
    fun `updateMultipleProductos should update only specified fields`() {
        // Arrange
        val originalPrecio = BigDecimal("100.00")
        val originalStock = 10
        val producto = createTestProducto(
            codigo = 1001,
            nombre = "Producto Test",
            precioUnitario = originalPrecio,
            stockActual = originalStock
        )

        productoService.createProducto(producto)

        val nuevaUnidadMedida = com.gnico.majo.application.domain.model.Id(3)

        // Act - Solo actualizar unidad de medida
        val result = productoService.updateMultipleProductos(
            codigos = listOf(1001),
            unidadMedidaId = nuevaUnidadMedida
        )

        // Assert
        assertEquals(1, result)

        val updatedProducto = productoService.getProductoByCodigo(1001)
        assertNotNull(updatedProducto)

        // Verificar que solo se actualizó la unidad de medida
        assertEquals(nuevaUnidadMedida, updatedProducto?.unidadMedida)

        // Verificar que otros campos no cambiaron
        assertEquals(originalPrecio, updatedProducto?.precioUnitario)
        assertEquals(originalStock, updatedProducto?.stockActual)
        assertEquals("Producto Test", updatedProducto?.nombre)
    }

    // Helper method to create test products
    private fun createTestProducto(
        codigo: Int = 1234,
        nombre: String = "Producto de Prueba",
        descripcion: String = "Descripción de prueba",
        unidadMedidaId: Int = 1,
        tipoIvaId: Int = 5, // 21%
        categoriaId: Int = 1,
        precioUnitario: BigDecimal = BigDecimal("100.00"),
        stockActual: Int = 10
    ): Producto {
        return Producto.create(
            codigo = codigo,
            nombre = nombre,
            descripcion = descripcion,
            unidadMedida = Id(unidadMedidaId),
            tipoIva = TipoIva.fromIdOrThrow(tipoIvaId),
            categoria = Id(categoriaId),
            precioUnitario = precioUnitario,
            stockActual = stockActual
        )
    }
}
