package com.gnico.majo.application.usecase

import com.gnico.majo.adapter.controller.dto.SaleFilterRequest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals

class SaleServiceFilteringTest {

    @Test
    fun `should validate page parameter`() {
        // Given
        val filterRequest = SaleFilterRequest(
            page = 0,
            size = 20
        )

        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            // This would normally call the service, but we'll just test the validation logic
            require(filterRequest.page >= 1) { "La página debe ser mayor o igual a 1" }
        }
        assertEquals("La página debe ser mayor o igual a 1", exception.message)
    }

    @Test
    fun `should validate size parameter - too small`() {
        // Given
        val filterRequest = SaleFilterRequest(
            page = 1,
            size = 0
        )

        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            require(filterRequest.size >= 1 && filterRequest.size <= 100) { "El tamaño de página debe estar entre 1 y 100" }
        }
        assertEquals("El tamaño de página debe estar entre 1 y 100", exception.message)
    }

    @Test
    fun `should validate size parameter - too large`() {
        // Given
        val filterRequest = SaleFilterRequest(
            page = 1,
            size = 101
        )

        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            require(filterRequest.size >= 1 && filterRequest.size <= 100) { "El tamaño de página debe estar entre 1 y 100" }
        }
        assertEquals("El tamaño de página debe estar entre 1 y 100", exception.message)
    }

    @Test
    fun `should validate empty fecha parameters`() {
        // Given
        val fechaDesde = ""

        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            require(fechaDesde.isNotBlank()) { "La fecha desde no puede estar vacía" }
        }
        assertEquals("La fecha desde no puede estar vacía", exception.message)
    }

    @Test
    fun `should validate empty username in usuarios list`() {
        // Given
        val usuarios = listOf("vendedor1", "")

        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            usuarios.forEach { username ->
                require(username.isNotBlank()) { "Los usernames no pueden estar vacíos" }
            }
        }
        assertEquals("Los usernames no pueden estar vacíos", exception.message)
    }

    @Test
    fun `should validate empty medio pago in mediosPago list`() {
        // Given
        val mediosPago = listOf("EFECTIVO", "")

        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            mediosPago.forEach { medioPago ->
                require(medioPago.isNotBlank()) { "Los medios de pago no pueden estar vacíos" }
            }
        }
        assertEquals("Los medios de pago no pueden estar vacíos", exception.message)
    }
}
