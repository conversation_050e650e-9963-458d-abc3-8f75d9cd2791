package com.gnico.majo.application.usecase

import com.gnico.majo.application.domain.model.*
import com.gnico.majo.application.port.`in`.ComprobanteService
import com.gnico.majo.application.port.out.SaleRepositoryPort
import com.gnico.majo.application.port.out.AfipService
import com.gnico.majo.application.port.out.ComprobanteNumeracionRepositoryPort
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.Mockito.*
import kotlinx.coroutines.runBlocking
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.LocalDate
import kotlin.test.assertEquals

class ComprobanteGenerationModeTest {

    private lateinit var saleRepository: SaleRepositoryPort
    private lateinit var afipService: AfipService
    private lateinit var numeracionRepository: ComprobanteNumeracionRepositoryPort
    private lateinit var comprobanteService: ComprobanteService

    @BeforeEach
    fun setUp() {
        saleRepository = mock()
        afipService = mock()
        numeracionRepository = mock()
        comprobanteService = ComprobanteServiceImpl(saleRepository, afipService, numeracionRepository)
    }

    @Test
    fun `should use ONLINE mode for FACTURA_B when mode is AUTO`() = runBlocking {
        // Given
        val ventaId = Id(1)
        val sale = createTestSale(ventaId, comprobanteEmitido = false)

        `when`(saleRepository.findSaleById(ventaId)).thenReturn(sale)
        `when`(numeracionRepository.obtenerYReservarSiguienteNumero(any(), any())).thenReturn(4)
        `when`(afipService.solicitarCAE(any(), any(), any(), any())).thenReturn(createSuccessfulAfipResponse())
        `when`(saleRepository.saveComprobante(any())).thenReturn(Id(100))
        `when`(saleRepository.saveComprobanteAttempt(any())).thenAnswer { }

        // When & Then - Should not throw exception and use online mode
        comprobanteService.generarComprobanteAuto(
            ventaId = ventaId,
            tipoComprobante = "FACTURA_B",
            modoGeneracion = "AUTO"
        )

        // Verify that CAE was requested (online mode)
        verify(afipService).solicitarCAE(any(), any(), any(), any())
        verify(afipService, never()).crearComprobanteConCAEA(any(), any(), any(), any())
    }

    @Test
    fun `should detect original comprobante mode for NOTA_CREDITO_B when mode is AUTO`() = runBlocking {
        // Given
        val ventaId = Id(1)
        val sale = createTestSale(ventaId, comprobanteEmitido = true)
        val originalComprobante = createTestComprobante(TipoAutorizacion.CAEA) // Original was offline

        `when`(saleRepository.findSaleById(ventaId)).thenReturn(sale)
        `when`(saleRepository.findComprobantesByVentaId(ventaId)).thenReturn(listOf(originalComprobante))
        `when`(numeracionRepository.obtenerYReservarSiguienteNumero(any(), any())).thenReturn(5)
        `when`(afipService.crearComprobanteConCAEA(any(), any(), any(), any())).thenReturn(createSuccessfulAfipResponse())
        `when`(saleRepository.saveComprobante(any())).thenReturn(Id(101))
        `when`(saleRepository.saveComprobanteAttempt(any())).thenAnswer { }

        // When
        comprobanteService.generarComprobanteAuto(
            ventaId = ventaId,
            tipoComprobante = "NOTA_CREDITO_B",
            modoGeneracion = "AUTO"
        )

        // Then - Should use offline mode (CAEA) because original was offline
        verify(afipService).crearComprobanteConCAEA(any(), any(), any(), any())
        verify(afipService, never()).solicitarCAE(any(), any(), any(), any())
    }

    @Test
    fun `should validate consistency when explicit mode conflicts with original comprobante`() = runBlocking {
        // Given
        val ventaId = Id(1)
        val sale = createTestSale(ventaId, comprobanteEmitido = true)
        val originalComprobante = createTestComprobante(TipoAutorizacion.CAE) // Original was online

        `when`(saleRepository.findSaleById(ventaId)).thenReturn(sale)
        `when`(saleRepository.findComprobantesByVentaId(ventaId)).thenReturn(listOf(originalComprobante))

        // When & Then - Should throw exception because requesting OFFLINE for a note when original was ONLINE
        val exception = assertThrows<IllegalArgumentException> {
            comprobanteService.generarComprobanteAuto(
                ventaId = ventaId,
                tipoComprobante = "NOTA_CREDITO_B",
                modoGeneracion = "OFFLINE"
            )
        }

        assertEquals(
            "Las notas de crédito/débito deben generarse con el mismo método que el comprobante original. " +
            "Comprobante original: ONLINE (CAE), Solicitado: OFFLINE (CAEA)",
            exception.message
        )
    }

    @Test
    fun `should allow consistent explicit mode with original comprobante`() = runBlocking {
        // Given
        val ventaId = Id(1)
        val sale = createTestSale(ventaId, comprobanteEmitido = true)
        val originalComprobante = createTestComprobante(TipoAutorizacion.CAE) // Original was online

        `when`(saleRepository.findSaleById(ventaId)).thenReturn(sale)
        `when`(saleRepository.findComprobantesByVentaId(ventaId)).thenReturn(listOf(originalComprobante))
        `when`(numeracionRepository.obtenerYReservarSiguienteNumero(any(), any())).thenReturn(6)
        `when`(afipService.solicitarCAE(any(), any(), any(), any())).thenReturn(createSuccessfulAfipResponse())
        `when`(saleRepository.saveComprobante(any())).thenReturn(Id(102))
        `when`(saleRepository.saveComprobanteAttempt(any())).thenAnswer { }

        // When - Should succeed because requesting ONLINE for a note when original was ONLINE
        comprobanteService.generarComprobanteAuto(
            ventaId = ventaId,
            tipoComprobante = "NOTA_CREDITO_B",
            modoGeneracion = "ONLINE"
        )

        // Then
        verify(afipService).solicitarCAE(any(), any(), any(), any())
    }

    @Test
    fun `should throw exception for invalid generation mode`() = runBlocking {
        // Given
        val ventaId = Id(1)
        val sale = createTestSale(ventaId, comprobanteEmitido = false)

        `when`(saleRepository.findSaleById(ventaId)).thenReturn(sale)

        // When & Then
        val exception = assertThrows<IllegalArgumentException> {
            comprobanteService.generarComprobanteAuto(
                ventaId = ventaId,
                tipoComprobante = "FACTURA_B",
                modoGeneracion = "INVALID"
            )
        }

        assertEquals(
            "Modo de generación no válido: INVALID. Valores permitidos: AUTO, ONLINE, OFFLINE",
            exception.message
        )
    }

    private fun createTestSale(ventaId: Id, comprobanteEmitido: Boolean): Sale {
        val usuario = Usuario(
            username = "testuser",
            nombre = "Test User",
            nombreDisplay = "Test User",
            activo = true
        )

        return Sale.fromPersistence(
            id = ventaId,
            numeroVenta = "V-12345678",
            usuario = usuario,
            fechaVenta = LocalDateTime.now(),
            montoTotal = BigDecimal("100.00"),
            comprobanteEmitido = comprobanteEmitido,
            medioPago = "EFECTIVO",
            porcentajeDescuento = null,
            codigoTicketBalanza = null,
            idTicketBalanza = null,
            items = emptyList()
        )
    }

    private fun createTestComprobante(tipoAutorizacion: TipoAutorizacion): Comprobante {
        return Comprobante.fromPersistence(
            id = Id(50),
            venta = Id(1),
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1,
            numeroComprobante = 123,
            cae = "12345678901234",
            fechaEmision = LocalDateTime.now(),
            fechaVencimientoCae = LocalDateTime.now().toLocalDate().plusDays(10),
            impTotal = BigDecimal("100.00"),
            impTotConc = BigDecimal.ZERO,
            impNeto = BigDecimal("82.64"),
            impIva = BigDecimal("17.36"),
            impTrib = BigDecimal.ZERO,
            monId = "PES",
            monCotiz = BigDecimal.ONE,
            estado = "APROBADO",
            tipoAutorizacion = tipoAutorizacion
        )
    }

    private fun createSuccessfulAfipResponse(): AfipResponse {
        return AfipResponse.createApprovedCAE(
            cae = "12345678901234",
            fechaVencimiento = LocalDate.now().plusDays(10),
            numeroComprobante = 123L,
            observaciones = emptyList()
        )
    }
}
