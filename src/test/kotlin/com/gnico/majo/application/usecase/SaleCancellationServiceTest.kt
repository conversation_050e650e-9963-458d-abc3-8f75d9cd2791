package com.gnico.majo.application.usecase

import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Sale
import com.gnico.majo.application.domain.model.Usuario
import com.gnico.majo.application.port.`in`.ComprobanteService
import com.gnico.majo.application.port.out.SaleRepositoryPort
import com.gnico.majo.application.port.out.UsuarioRepository
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Assertions.*
import org.mockito.Mockito.*
import kotlinx.coroutines.runBlocking
import java.math.BigDecimal
import java.time.LocalDateTime

class SaleCancellationServiceTest {

    private lateinit var saleRepository: SaleRepositoryPort
    private lateinit var usuarioRepository: UsuarioRepository
    private lateinit var comprobanteService: ComprobanteService
    private lateinit var saleCancellationService: SaleCancellationServiceImpl

    @BeforeEach
    fun setUp() {
        saleRepository = mock(SaleRepositoryPort::class.java)
        usuarioRepository = mock(UsuarioRepository::class.java)
        comprobanteService = mock(ComprobanteService::class.java)
        
        saleCancellationService = SaleCancellationServiceImpl(
            saleRepository = saleRepository,
            usuarioRepository = usuarioRepository,
            comprobanteService = comprobanteService
        )
    }

    @Test
    fun `should cancel sale without comprobante successfully`() = runBlocking {
        // Given
        val ventaId = Id(1)
        val usuarioCancelacion = "admin"
        val motivo = "Error en la venta"
        
        val usuario = Usuario(
            username = usuarioCancelacion,
            nombre = "Administrador",
            nombreDisplay = "Admin",
            activo = true
        )
        
        val sale = Sale.fromPersistence(
            id = ventaId,
            numeroVenta = "V-12345678",
            usuario = usuario,
            fechaVenta = LocalDateTime.now(),
            montoTotal = BigDecimal("100.00"),
            comprobanteEmitido = false,
            medioPago = "EFECTIVO",
            porcentajeDescuento = null,
            codigoTicketBalanza = null,
            idTicketBalanza = null,
            items = emptyList()
        )

        `when`(usuarioRepository.findByUsername(usuarioCancelacion)).thenReturn(usuario)
        `when`(saleRepository.findSaleById(ventaId)).thenReturn(sale)
        `when`(saleRepository.cancelSale(ventaId, usuarioCancelacion, motivo)).thenReturn(true)

        // When
        val result = saleCancellationService.cancelSale(ventaId, usuarioCancelacion, motivo, true)

        // Then
        assertTrue(result.success)
        assertEquals("Venta cancelada exitosamente", result.message)
        assertEquals(ventaId.value, result.ventaId)
        assertEquals("V-12345678", result.numeroVenta)
        assertFalse(result.notaCreditoGenerada)
        assertNull(result.notaCreditoId)
        assertNull(result.error)

        verify(saleRepository).cancelSale(ventaId, usuarioCancelacion, motivo)
        verify(comprobanteService, never()).generarComprobanteOnline(any(), any(), any())
    }

    @Test
    fun `should cancel sale with comprobante and generate nota credito`() = runBlocking {
        // Given
        val ventaId = Id(1)
        val usuarioCancelacion = "admin"
        val motivo = "Devolución solicitada"
        
        val usuario = Usuario(
            username = usuarioCancelacion,
            nombre = "Administrador",
            nombreDisplay = "Admin",
            activo = true
        )
        
        val sale = Sale.fromPersistence(
            id = ventaId,
            numeroVenta = "V-12345678",
            usuario = usuario,
            fechaVenta = LocalDateTime.now(),
            montoTotal = BigDecimal("100.00"),
            comprobanteEmitido = true, // Tiene comprobante emitido
            medioPago = "EFECTIVO",
            porcentajeDescuento = null,
            codigoTicketBalanza = null,
            idTicketBalanza = null,
            items = emptyList()
        )

        val notaCreditoId = Id(456)

        `when`(usuarioRepository.findByUsername(usuarioCancelacion)).thenReturn(usuario)
        `when`(saleRepository.findSaleById(ventaId)).thenReturn(sale)
        `when`(saleRepository.cancelSale(ventaId, usuarioCancelacion, motivo)).thenReturn(true)
        `when`(comprobanteService.generarComprobanteOnline(ventaId, "NOTA_CREDITO_B", null))
            .thenReturn(notaCreditoId)

        // When
        val result = saleCancellationService.cancelSale(ventaId, usuarioCancelacion, motivo, true)

        // Then
        assertTrue(result.success)
        assertTrue(result.message.contains("nota de crédito B generada"))
        assertEquals(ventaId.value, result.ventaId)
        assertEquals("V-12345678", result.numeroVenta)
        assertTrue(result.notaCreditoGenerada)
        assertEquals(notaCreditoId.value, result.notaCreditoId)
        assertNull(result.error)

        verify(saleRepository).cancelSale(ventaId, usuarioCancelacion, motivo)
        verify(comprobanteService).generarComprobanteOnline(ventaId, "NOTA_CREDITO_B", null)
    }

    @Test
    fun `should fail when user not found`() = runBlocking {
        // Given
        val ventaId = Id(1)
        val usuarioCancelacion = "nonexistent"
        val motivo = "Test"

        `when`(usuarioRepository.findByUsername(usuarioCancelacion)).thenReturn(null)

        // When
        val result = saleCancellationService.cancelSale(ventaId, usuarioCancelacion, motivo, true)

        // Then
        assertFalse(result.success)
        assertTrue(result.message.contains("Usuario '$usuarioCancelacion' no encontrado"))
        assertEquals("Usuario no encontrado", result.error)

        verify(saleRepository, never()).cancelSale(any(), any(), any())
    }

    @Test
    fun `should fail when sale not found`() = runBlocking {
        // Given
        val ventaId = Id(999)
        val usuarioCancelacion = "admin"
        val motivo = "Test"
        
        val usuario = Usuario(
            username = usuarioCancelacion,
            nombre = "Admin",
            nombreDisplay = "Admin",
            activo = true
        )

        `when`(usuarioRepository.findByUsername(usuarioCancelacion)).thenReturn(usuario)
        `when`(saleRepository.findSaleById(ventaId)).thenReturn(null)

        // When
        val result = saleCancellationService.cancelSale(ventaId, usuarioCancelacion, motivo, true)

        // Then
        assertFalse(result.success)
        assertTrue(result.message.contains("Venta ${ventaId.value} no encontrada"))
        assertEquals("Venta no encontrada", result.error)

        verify(saleRepository, never()).cancelSale(any(), any(), any())
    }

    @Test
    fun `should fail when sale already cancelled`() = runBlocking {
        // Given
        val ventaId = Id(1)
        val usuarioCancelacion = "admin"
        val motivo = "Test"
        
        val usuario = Usuario(
            username = usuarioCancelacion,
            nombre = "Admin",
            nombreDisplay = "Admin",
            activo = true
        )
        
        val cancelledSale = Sale.fromPersistence(
            id = ventaId,
            numeroVenta = "V-12345678",
            usuario = usuario,
            fechaVenta = LocalDateTime.now(),
            montoTotal = BigDecimal("100.00"),
            comprobanteEmitido = false,
            medioPago = "EFECTIVO",
            porcentajeDescuento = null,
            codigoTicketBalanza = null,
            idTicketBalanza = null,
            cancelada = true, // Ya cancelada
            fechaCancelacion = LocalDateTime.now(),
            usuarioCancelacion = "otro_usuario",
            motivoCancelacion = "Ya cancelada",
            items = emptyList()
        )

        `when`(usuarioRepository.findByUsername(usuarioCancelacion)).thenReturn(usuario)
        `when`(saleRepository.findSaleById(ventaId)).thenReturn(cancelledSale)

        // When
        val result = saleCancellationService.cancelSale(ventaId, usuarioCancelacion, motivo, true)

        // Then
        assertFalse(result.success)
        assertTrue(result.message.contains("ya está cancelada"))
        assertEquals("Venta ya cancelada", result.error)

        verify(saleRepository, never()).cancelSale(any(), any(), any())
    }
}
