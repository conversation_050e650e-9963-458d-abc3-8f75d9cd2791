package com.gnico.majo.integration

import com.gnico.majo.application.domain.model.*
import com.gnico.majo.application.port.out.SaleRepositoryPort
import com.gnico.majo.application.port.out.AfipService
import com.gnico.majo.application.port.out.ComprobanteNumeracionRepositoryPort
import com.gnico.majo.application.usecase.ComprobanteServiceImpl
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.*
import kotlinx.coroutines.runBlocking
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.LocalDate
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

/**
 * Test de integración para verificar que la numeración de comprobantes es independiente por tipo
 * según las reglas de AFIP
 */
class ComprobanteNumeracionIndependienteTest {

    private lateinit var saleRepository: SaleRepositoryPort
    private lateinit var afipService: AfipService
    private lateinit var numeracionRepository: ComprobanteNumeracionRepositoryPort
    private lateinit var comprobanteService: ComprobanteServiceImpl

    @BeforeEach
    fun setUp() {
        saleRepository = mock()
        afipService = mock()
        numeracionRepository = mock()
        comprobanteService = ComprobanteServiceImpl(saleRepository, afipService, numeracionRepository)
    }

    @Test
    fun `should allow same numero_comprobante for different tipos in same punto_venta`() = runBlocking {
        // Given - Dos ventas diferentes
        val ventaFactura = createTestSale(Id(1), comprobanteEmitido = false)
        val ventaNotaCredito = createTestSale(Id(2), comprobanteEmitido = true)
        
        // Mock para la factura (primera venta)
        `when`(saleRepository.findSaleById(Id(1))).thenReturn(ventaFactura)
        `when`(afipService.solicitarCAE(any(), eq("FACTURA_B"), any(), any())).thenReturn(
            createAfipResponse(numeroComprobante = 4L) // AFIP asigna número 4 para FACTURA_B
        )
        `when`(saleRepository.saveComprobante(any())).thenReturn(Id(100))
        `when`(saleRepository.updateComprobanteWithAfipData(any(), any(), any(), any())).thenAnswer { }
        `when`(saleRepository.saveComprobanteAttempt(any())).thenAnswer { }

        // Mock para la nota de crédito (segunda venta)
        val facturaOriginal = createTestComprobante(Id(100), "FACTURA_B", numeroComprobante = 4, TipoAutorizacion.CAE)
        `when`(saleRepository.findSaleById(Id(2))).thenReturn(ventaNotaCredito)
        `when`(saleRepository.findComprobantesByVentaId(Id(2))).thenReturn(listOf(facturaOriginal))
        `when`(afipService.solicitarCAE(any(), eq("NOTA_CREDITO_B"), any(), any())).thenReturn(
            createAfipResponse(numeroComprobante = 4L) // AFIP asigna número 4 para NOTA_CREDITO_B (numeración independiente)
        )
        `when`(saleRepository.saveComprobante(any())).thenReturn(Id(101))

        // When - Generar factura primero
        val facturaId = comprobanteService.generarComprobanteAuto(
            ventaId = Id(1),
            tipoComprobante = "FACTURA_B",
            modoGeneracion = "ONLINE"
        )

        // Then - Factura se genera exitosamente
        assertNotNull(facturaId)
        assertEquals(100, facturaId.value)

        // When - Generar nota de crédito con el mismo número pero diferente tipo
        val notaCreditoId = comprobanteService.generarComprobanteAuto(
            ventaId = Id(2),
            tipoComprobante = "NOTA_CREDITO_B",
            modoGeneracion = "ONLINE"
        )

        // Then - Nota de crédito se genera exitosamente sin conflicto de numeración
        assertNotNull(notaCreditoId)
        assertEquals(101, notaCreditoId.value)

        // Verify - Ambos comprobantes fueron guardados
        verify(saleRepository, times(2)).saveComprobante(any())
        verify(saleRepository, times(2)).updateComprobanteWithAfipData(any(), eq(4L), any(), any())
    }

    private fun createTestSale(id: Id, comprobanteEmitido: Boolean): Sale {
        val usuario = Usuario(
            username = "testuser",
            nombre = "Test User",
            nombreDisplay = "Test User",
            activo = true
        )

        return Sale.fromPersistence(
            id = id,
            numeroVenta = "V-${id.value}",
            usuario = usuario,
            fechaVenta = LocalDateTime.now(),
            montoTotal = BigDecimal("100.00"),
            comprobanteEmitido = comprobanteEmitido,
            medioPago = "EFECTIVO",
            porcentajeDescuento = null,
            codigoTicketBalanza = null,
            idTicketBalanza = null,
            items = emptyList()
        )
    }

    private fun createTestComprobante(
        id: Id,
        tipoComprobante: String,
        numeroComprobante: Int,
        tipoAutorizacion: TipoAutorizacion
    ): Comprobante {
        return Comprobante.fromPersistence(
            id = id,
            venta = Id(2),
            tipoComprobante = tipoComprobante,
            puntoVenta = 1,
            numeroComprobante = numeroComprobante,
            cae = "12345678901234",
            fechaEmision = LocalDateTime.now(),
            fechaVencimientoCae = LocalDate.now().plusDays(10),
            impTotal = BigDecimal("100.00"),
            impTotConc = BigDecimal.ZERO,
            impNeto = BigDecimal("82.64"),
            impIva = BigDecimal("17.36"),
            impTrib = BigDecimal.ZERO,
            monId = "PES",
            monCotiz = BigDecimal.ONE,
            estado = "APROBADO",
            tipoAutorizacion = tipoAutorizacion
        )
    }

    private fun createAfipResponse(numeroComprobante: Long): AfipResponse {
        return AfipResponse.createApprovedCAE(
            cae = "12345678901234",
            fechaVencimiento = LocalDate.now().plusDays(10),
            numeroComprobante = numeroComprobante,
            observaciones = emptyList()
        )
    }
}
