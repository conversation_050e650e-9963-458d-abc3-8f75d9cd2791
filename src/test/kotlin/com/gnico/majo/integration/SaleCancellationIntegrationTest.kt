package com.gnico.majo.integration

import com.gnico.majo.adapter.controller.dto.SaleFilterRequest
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Sale
import com.gnico.majo.application.domain.model.Usuario
import com.gnico.majo.application.port.out.SaleRepositoryPort
import com.gnico.majo.application.usecase.SaleServiceImpl
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Assertions.*
import org.mockito.Mockito.*
import java.math.BigDecimal
import java.time.LocalDateTime

class SaleCancellationIntegrationTest {

    private lateinit var saleRepository: SaleRepositoryPort
    private lateinit var saleService: SaleServiceImpl

    @BeforeEach
    fun setUp() {
        saleRepository = mock(SaleRepositoryPort::class.java)
        // Para este test, solo necesitamos verificar el filtro incluirCanceladas
        saleService = mock(SaleServiceImpl::class.java)
    }

    @Test
    fun `should exclude cancelled sales by default`() {
        // Given
        val filterRequest = SaleFilterRequest(
            fechaDesde = null,
            fechaHasta = null,
            usuarios = null,
            comprobanteEmitido = null,
            mediosPago = null,
            incluirCanceladas = false, // Por defecto false
            page = 1,
            size = 20
        )

        // Verificar que el filtro por defecto excluye canceladas
        assertFalse(filterRequest.incluirCanceladas)
    }

    @Test
    fun `should include cancelled sales when explicitly requested`() {
        // Given
        val filterRequest = SaleFilterRequest(
            fechaDesde = null,
            fechaHasta = null,
            usuarios = null,
            comprobanteEmitido = null,
            mediosPago = null,
            incluirCanceladas = true, // Explícitamente incluir
            page = 1,
            size = 20
        )

        // Verificar que el filtro incluye canceladas cuando se solicita
        assertTrue(filterRequest.incluirCanceladas)
    }

    @Test
    fun `should verify sale cancellation fields are properly set`() {
        // Given
        val usuario = Usuario(
            username = "admin",
            nombre = "Administrador",
            nombreDisplay = "Admin",
            activo = true
        )

        // Test: Venta normal (no cancelada)
        val normalSale = Sale.fromPersistence(
            id = Id(1),
            numeroVenta = "V-12345678",
            usuario = usuario,
            fechaVenta = LocalDateTime.now(),
            montoTotal = BigDecimal("100.00"),
            comprobanteEmitido = false,
            medioPago = "EFECTIVO",
            porcentajeDescuento = null,
            codigoTicketBalanza = null,
            idTicketBalanza = null,
            cancelada = false,
            fechaCancelacion = null,
            usuarioCancelacion = null,
            motivoCancelacion = null,
            items = emptyList()
        )

        // Test: Venta cancelada
        val cancelledSale = Sale.fromPersistence(
            id = Id(2),
            numeroVenta = "V-87654321",
            usuario = usuario,
            fechaVenta = LocalDateTime.now().minusDays(1),
            montoTotal = BigDecimal("200.00"),
            comprobanteEmitido = true,
            medioPago = "TARJETA",
            porcentajeDescuento = null,
            codigoTicketBalanza = null,
            idTicketBalanza = null,
            cancelada = true,
            fechaCancelacion = LocalDateTime.now(),
            usuarioCancelacion = "admin",
            motivoCancelacion = "Error en la venta",
            items = emptyList()
        )

        // Verificaciones para venta normal
        assertFalse(normalSale.cancelada)
        assertNull(normalSale.fechaCancelacion)
        assertNull(normalSale.usuarioCancelacion)
        assertNull(normalSale.motivoCancelacion)
        assertTrue(normalSale.canBeCancelled())

        // Verificaciones para venta cancelada
        assertTrue(cancelledSale.cancelada)
        assertNotNull(cancelledSale.fechaCancelacion)
        assertEquals("admin", cancelledSale.usuarioCancelacion)
        assertEquals("Error en la venta", cancelledSale.motivoCancelacion)
        assertFalse(cancelledSale.canBeCancelled())
    }

    @Test
    fun `should verify sale cancellation validation`() {
        // Given
        val usuario = Usuario(
            username = "admin",
            nombre = "Admin",
            nombreDisplay = "Admin",
            activo = true
        )

        val sale = Sale.fromPersistence(
            id = Id(1),
            numeroVenta = "V-12345678",
            usuario = usuario,
            fechaVenta = LocalDateTime.now(),
            montoTotal = BigDecimal("100.00"),
            comprobanteEmitido = false,
            medioPago = "EFECTIVO",
            porcentajeDescuento = null,
            codigoTicketBalanza = null,
            idTicketBalanza = null,
            items = emptyList()
        )

        // Test: Venta puede ser cancelada
        assertTrue(sale.canBeCancelled())
        assertDoesNotThrow { sale.validateCancellation() }

        // Test: Cancelar la venta
        val cancelledSale = sale.cancel("admin", "Test cancellation")
        
        // Verificar que la venta cancelada tiene los datos correctos
        assertTrue(cancelledSale.cancelada)
        assertEquals("admin", cancelledSale.usuarioCancelacion)
        assertEquals("Test cancellation", cancelledSale.motivoCancelacion)
        assertNotNull(cancelledSale.fechaCancelacion)
        
        // Test: Venta cancelada no puede ser cancelada nuevamente
        assertFalse(cancelledSale.canBeCancelled())
        assertThrows(IllegalArgumentException::class.java) {
            cancelledSale.validateCancellation()
        }
    }

    @Test
    fun `should validate cancellation parameters`() {
        // Given
        val usuario = Usuario(
            username = "admin",
            nombre = "Admin",
            nombreDisplay = "Admin",
            activo = true
        )

        val sale = Sale.fromPersistence(
            id = Id(1),
            numeroVenta = "V-12345678",
            usuario = usuario,
            fechaVenta = LocalDateTime.now(),
            montoTotal = BigDecimal("100.00"),
            comprobanteEmitido = false,
            medioPago = "EFECTIVO",
            porcentajeDescuento = null,
            codigoTicketBalanza = null,
            idTicketBalanza = null,
            items = emptyList()
        )

        // Test: Usuario vacío debe fallar
        assertThrows(IllegalArgumentException::class.java) {
            sale.cancel("", "Motivo válido")
        }

        // Test: Motivo vacío debe fallar
        assertThrows(IllegalArgumentException::class.java) {
            sale.cancel("admin", "")
        }

        // Test: Usuario y motivo en blanco deben fallar
        assertThrows(IllegalArgumentException::class.java) {
            sale.cancel("   ", "Motivo válido")
        }

        assertThrows(IllegalArgumentException::class.java) {
            sale.cancel("admin", "   ")
        }
    }
}
