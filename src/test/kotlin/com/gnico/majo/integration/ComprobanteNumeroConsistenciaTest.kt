package com.gnico.majo.integration

import com.gnico.majo.application.domain.model.*
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals
import kotlin.test.assertTrue
import java.math.BigDecimal

/**
 * Test para verificar que los números de comprobante se manejan consistentemente
 * entre la tabla comprobantes y comprobante_attempts
 */
class ComprobanteNumeroConsistenciaTest {

    @Test
    fun `comprobante debe inicializarse con numero temporal y actualizarse con numero de AFIP`() {
        // Given - Crear un comprobante inicial (antes de AFIP)
        val sale = createTestSale()
        val comprobanteInicial = Comprobante.createFromSale(
            sale = sale,
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1,
            ventaId = Id(1),
            numeroComprobante = -123456, // Número temporal negativo
            tipoAutorizacion = TipoAutorizacion.CAE
        )

        // Then - El comprobante inicial debe tener número temporal negativo (único)
        assertTrue(comprobanteInicial.numeroComprobante < 0, "El comprobante inicial debe tener número temporal negativo único")
        assertEquals("PENDING", comprobanteInicial.cae, "El CAE inicial debe ser PENDING")
        assertEquals("PENDIENTE", comprobanteInicial.estado, "El estado inicial debe ser PENDIENTE")
    }

    @Test
    fun `comprobante debe actualizarse con datos reales de AFIP`() {
        // Given - Comprobante inicial
        val sale = createTestSale()
        val comprobanteInicial = Comprobante.createFromSale(
            sale = sale,
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1,
            ventaId = Id(1),
            numeroComprobante = -123457, // Número temporal negativo
            tipoAutorizacion = TipoAutorizacion.CAE
        )

        // When - Actualizar con datos de AFIP
        val comprobanteConCAE = comprobanteInicial.withCAE(
            cae = "75282262825373",
            estado = "EMITIDO"
        )

        // Then - El comprobante debe tener los datos actualizados
        assertEquals("75282262825373", comprobanteConCAE.cae)
        assertEquals("EMITIDO", comprobanteConCAE.estado)
        // El número sigue siendo temporal negativo hasta que se actualice en la base de datos
        assertTrue(comprobanteConCAE.numeroComprobante < 0, "El número debe seguir siendo temporal hasta la actualización de BD")
    }

    @Test
    fun `ComprobanteAttempt debe contener el numero real de AFIP`() {
        // Given - Respuesta simulada de AFIP
        val afipResponse = createMockAfipResponse(
            numeroComprobante = 123L,
            cae = "75282262825373"
        )

        // When - Crear ComprobanteAttempt exitoso
        val attempt = ComprobanteAttempt.createExitoso(
            ventaId = Id(1),
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1,
            afipResponse = afipResponse,
            tiempoRespuestaMs = 1000,
            comprobanteId = Id(1)
        )

        // Then - El attempt debe tener el número real de AFIP
        assertEquals(123L, attempt.numeroComprobante, "El attempt debe tener el número real de AFIP")
        assertEquals("75282262825373", attempt.cae, "El attempt debe tener el CAE real de AFIP")
        assertEquals(EstadoIntento.EXITOSO, attempt.estado)
    }

    @Test
    fun `nota de credito debe usar numero de comprobante asociado correcto`() {
        // Given - Factura original con número específico
        val facturaOriginal = createTestFactura(numeroComprobante = 6)
        val comprobanteAsociado = ComprobanteAsociado.fromComprobante(facturaOriginal)

        // When - Crear nota de crédito
        val sale = createTestSale()
        val notaCredito = Comprobante.createFromSale(
            sale = sale,
            tipoComprobante = "NOTA_CREDITO_B",
            puntoVenta = 1,
            ventaId = Id(1),
            numeroComprobante = 3001, // Número de prueba
            tipoAutorizacion = TipoAutorizacion.CAE,
            comprobanteAsociado = comprobanteAsociado,
            comprobanteOriginal = facturaOriginal
        )

        // Then - La nota debe referenciar correctamente la factura
        assertEquals("NOTA_CREDITO_B", notaCredito.tipoComprobante)
        assertEquals(comprobanteAsociado, notaCredito.comprobanteAsociado)
        assertEquals("FACTURA_B", notaCredito.comprobanteAsociado!!.tipo)
        assertEquals(6, notaCredito.comprobanteAsociado!!.numeroComprobante)
        assertEquals(1, notaCredito.comprobanteAsociado!!.puntoVenta)
    }

    @Test
    fun `flujo completo debe mantener consistencia entre tablas`() {
        // Este test simula el flujo completo:
        // 1. Crear comprobante con número temporal
        // 2. Obtener respuesta de AFIP con número real
        // 3. Verificar que ambas tablas tengan datos consistentes

        // Given - Comprobante inicial
        val sale = createTestSale()
        val comprobanteInicial = Comprobante.createFromSale(
            sale = sale,
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1,
            ventaId = Id(1),
            numeroComprobante = -123458, // Número temporal negativo
            tipoAutorizacion = TipoAutorizacion.CAE
        )

        // When - Simular respuesta de AFIP
        val afipResponse = createMockAfipResponse(
            numeroComprobante = 123L,
            cae = "75282262825373"
        )

        val comprobanteConCAE = comprobanteInicial.withCAE(
            cae = afipResponse.cae,
            estado = afipResponse.getEstadoDescriptivo()
        )

        val attempt = ComprobanteAttempt.createExitoso(
            ventaId = Id(1),
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1,
            afipResponse = afipResponse,
            comprobanteId = Id(1)
        )

        // Then - Verificar consistencia
        assertEquals(afipResponse.cae, comprobanteConCAE.cae)
        assertEquals(afipResponse.cae, attempt.cae)
        assertEquals(afipResponse.numeroComprobante, attempt.numeroComprobante)
        assertEquals(afipResponse.getEstadoDescriptivo(), comprobanteConCAE.estado)
        
        // El comprobante en la tabla debe actualizarse con updateComprobanteWithAfipData()
        // para tener el mismo número que el attempt
    }

    private fun createTestSale(): Sale {
        val usuario = Usuario("testuser", "Usuario Test", "Usuario Test", true)
        return Sale.create(
            usuario = usuario,
            items = listOf(
                SaleItem.create(
                    productoNombre = "Producto Test",
                    cantidad = BigDecimal.ONE,
                    precioUnitario = BigDecimal("121.00"),
                    tipoIva = TipoIva.IVA_21
                )
            ),
            medioPago = "EFECTIVO"
        )
    }

    private fun createTestFactura(numeroComprobante: Int): Comprobante {
        return Comprobante.fromPersistence(
            id = Id(1),
            venta = Id(1),
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1,
            numeroComprobante = numeroComprobante,
            cae = "12345678901234",
            fechaEmision = java.time.LocalDateTime.now(),
            fechaVencimientoCae = java.time.LocalDate.now().plusDays(10),
            impTotal = BigDecimal("121.00"),
            impTotConc = BigDecimal.ZERO,
            impNeto = BigDecimal("100.00"),
            impIva = BigDecimal("21.00"),
            impTrib = BigDecimal.ZERO,
            monId = "PES",
            monCotiz = BigDecimal.ONE,
            estado = "EMITIDO",
            tipoAutorizacion = TipoAutorizacion.CAE
        )
    }

    private fun createMockAfipResponse(numeroComprobante: Long, cae: String): AfipResponse {
        return AfipResponse.createApprovedCAE(
            cae = cae,
            fechaVencimiento = java.time.LocalDate.now().plusDays(10),
            numeroComprobante = numeroComprobante,
            observaciones = emptyList()
        )
    }
}
