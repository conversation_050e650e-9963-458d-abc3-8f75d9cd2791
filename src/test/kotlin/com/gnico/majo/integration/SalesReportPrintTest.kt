package com.gnico.majo.integration

import com.gnico.majo.application.domain.model.*
import com.gnico.majo.infrastructure.printer.StyledTicketFormatter
import com.gnico.majo.infrastructure.config.PrinterConfiguration
import com.github.anastaciocintra.escpos.EscPos
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Disabled
import java.io.ByteArrayOutputStream
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * Test de integración para verificar el formato del ticket de reporte de ventas
 * Este test muestra el contenido que se enviaría a la impresora sin imprimir físicamente
 */
class SalesReportPrintTest {

    @Test
    fun `should generate correct sales report ticket format`() {
        // Crear configuración de impresora para testing
        val config = PrinterConfiguration.fromEnvironment()
        val formatter = StyledTicketFormatter(config)

        // Crear datos de prueba para el reporte
        val reportTicket = createTestSalesReportTicket()

        println("=== CONTENIDO TICKET REPORTE DE VENTAS ===")
        val output = ByteArrayOutputStream()
        val escpos = EscPos(output)
        
        formatter.formatSalesReportTicket(escpos, reportTicket)
        
        val rawOutput = output.toByteArray()
        val readableOutput = convertEscPosToReadable(rawOutput)
        
        println(readableOutput)
        println("=" * 50)
        
        // Verificar que el contenido contiene elementos esperados
        assert(readableOutput.contains("REPORTE DE VENTAS"))
        assert(readableOutput.contains("TOTAL VENTAS"))
        assert(readableOutput.contains("EFECTIVO"))
        assert(readableOutput.contains("ELECTR")) // Puede aparecer como ELECTRÓNICO o ELECTR?NICO por encoding
        assert(readableOutput.contains("Total facturado"))
        assert(readableOutput.contains("IVA contenido"))
    }

    @Test
    @Disabled("Solo ejecutar manualmente para probar con impresora física")
    fun `should print sales report to physical printer`() {
        // Este test está deshabilitado para evitar impresión accidental
        // Para probarlo, remover @Disabled y ejecutar manualmente
        
        val config = PrinterConfiguration.fromEnvironment()
        val formatter = StyledTicketFormatter(config)
        val reportTicket = createTestSalesReportTicket()

        // Aquí iría la lógica para imprimir en impresora física
        // formatter.formatSalesReportTicket(escpos, reportTicket)
        
        println("Test de impresión física deshabilitado")
    }

    private fun createTestSalesReportTicket(): SalesReportTicket {
        val periodo = ReportPeriod.today()
        
        return SalesReportTicket(
            periodo = periodo,
            fechaGeneracion = LocalDateTime.now(),
            montoTotalVentas = BigDecimal("15750.50"),
            montoTotalFacturado = BigDecimal("12300.00"),
            montoTotalIva = BigDecimal("2583.00"),
            ventasEfectivo = PaymentSummary(
                descripcion = "Efectivo",
                cantidadVentas = 8,
                montoTotal = BigDecimal("6200.25"),
                porcentajeDelTotal = BigDecimal("39.4")
            ),
            ventasElectronicas = PaymentSummary(
                descripcion = "Electrónico",
                cantidadVentas = 12,
                montoTotal = BigDecimal("9550.25"),
                porcentajeDelTotal = BigDecimal("60.6")
            ),
            resumenGeneral = TicketGeneralSummary(
                totalVentas = 20,
                ventasCanceladas = 1,
                ventasConComprobante = 15,
                ventasSinComprobante = 5
            )
        )
    }

    /**
     * Convierte comandos ESC/POS a texto legible para debugging
     */
    private fun convertEscPosToReadable(rawOutput: ByteArray): String {
        val output = StringBuilder()
        var i = 0
        
        while (i < rawOutput.size) {
            val byte = rawOutput[i].toInt() and 0xFF
            
            when (byte) {
                0x1B -> { // ESC
                    if (i + 1 < rawOutput.size) {
                        val nextByte = rawOutput[i + 1].toInt() and 0xFF
                        when (nextByte) {
                            0x61 -> { // Alineación
                                if (i + 2 < rawOutput.size) {
                                    val align = rawOutput[i + 2].toInt() and 0xFF
                                    when (align) {
                                        0 -> output.append("[ALIGN_LEFT]")
                                        1 -> output.append("[ALIGN_CENTER]")
                                        2 -> output.append("[ALIGN_RIGHT]")
                                    }
                                    i += 3
                                } else i += 2
                            }
                            0x45 -> { // Bold
                                if (i + 2 < rawOutput.size) {
                                    val bold = rawOutput[i + 2].toInt() and 0xFF
                                    output.append(if (bold == 1) "[BOLD_ON]" else "[BOLD_OFF]")
                                    i += 3
                                } else i += 2
                            }
                            else -> {
                                output.append("[ESC_${nextByte.toString(16).uppercase()}]")
                                i += 2
                            }
                        }
                    } else {
                        output.append("[ESC]")
                        i++
                    }
                }
                0x1D -> { // GS
                    if (i + 1 < rawOutput.size) {
                        val nextByte = rawOutput[i + 1].toInt() and 0xFF
                        when (nextByte) {
                            0x21 -> { // Tamaño de fuente
                                if (i + 2 < rawOutput.size) {
                                    val size = rawOutput[i + 2].toInt() and 0xFF
                                    when (size) {
                                        0x00 -> output.append("[FONT_NORMAL]")
                                        0x11 -> output.append("[FONT_DOUBLE_HEIGHT]")
                                        0x22 -> output.append("[FONT_DOUBLE_WIDTH]")
                                        0x33 -> output.append("[FONT_DOUBLE_BOTH]")
                                    }
                                    i += 3
                                } else i += 2
                            }
                            else -> {
                                output.append("[GS_${nextByte.toString(16).uppercase()}]")
                                i += 2
                            }
                        }
                    } else {
                        output.append("[GS]")
                        i++
                    }
                }
                0x0A -> { // LF
                    output.append("\n")
                    i++
                }
                0x0D -> { // CR
                    output.append("\r")
                    i++
                }
                in 32..126 -> { // Caracteres imprimibles ASCII
                    output.append(byte.toChar())
                    i++
                }
                else -> {
                    output.append("[${byte.toString(16).uppercase().padStart(2, '0')}]")
                    i++
                }
            }
        }
        
        return output.toString()
    }
}

private operator fun String.times(n: Int): String = this.repeat(n)
