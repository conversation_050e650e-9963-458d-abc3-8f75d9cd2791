package com.gnico.majo.integration

import com.gnico.majo.application.domain.model.*
import com.gnico.majo.application.usecase.ComprobanteServiceImpl
import com.gnico.majo.adapter.afip.AfipServiceAdapter
import com.gnico.majo.adapter.persistence.JooqSaleRepository
import com.gnico.majo.adapter.persistence.JooqComprobanteNumeracionRepository
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Assertions.*
import org.mockito.Mockito.*
import kotlinx.coroutines.runBlocking
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * Test para verificar que se ha solucionado el problema de duplicados CAEA
 * y que el campo caea_utilizado se asigna correctamente
 */
class CAEADuplicateFixTest {

    private lateinit var mockSaleRepository: JooqSaleRepository
    private lateinit var mockAfipService: AfipServiceAdapter
    private lateinit var mockNumeracionRepository: JooqComprobanteNumeracionRepository
    private lateinit var comprobanteService: ComprobanteServiceImpl

    @BeforeEach
    fun setup() {
        mockSaleRepository = mock(JooqSaleRepository::class.java)
        mockAfipService = mock(AfipServiceAdapter::class.java)
        mockNumeracionRepository = mock(JooqComprobanteNumeracionRepository::class.java)
        
        comprobanteService = ComprobanteServiceImpl(
            saleRepository = mockSaleRepository,
            afipService = mockAfipService,
            numeracionRepository = mockNumeracionRepository
        )
    }

    @Test
    fun `should allow multiple comprobantes with same CAEA code`() = runBlocking {
        // Given: Dos ventas diferentes que van a usar el mismo CAEA
        val venta1 = createTestSale(Id(1))
        val venta2 = createTestSale(Id(2))
        val sameCaeaCode = "21234567890123"
        
        // Mock responses con el mismo CAEA
        val afipResponse1 = AfipResponse.createApprovedCAEA(
            caea = sameCaeaCode,
            fechaVencimiento = LocalDate.now().plusDays(15),
            numeroComprobante = 1L,
            observaciones = listOf("CAEA aprobado")
        )
        
        val afipResponse2 = AfipResponse.createApprovedCAEA(
            caea = sameCaeaCode, // Mismo CAEA
            fechaVencimiento = LocalDate.now().plusDays(15),
            numeroComprobante = 2L,
            observaciones = listOf("CAEA aprobado")
        )

        // Mock repository calls
        `when`(mockSaleRepository.findSaleById(Id(1))).thenReturn(venta1)
        `when`(mockSaleRepository.findSaleById(Id(2))).thenReturn(venta2)
        `when`(mockNumeracionRepository.obtenerSiguienteNumero(2, "FACTURA_B")).thenReturn(1, 2)
        `when`(mockAfipService.crearComprobanteConCAEA(any(), any(), any(), any()))
            .thenReturn(afipResponse1, afipResponse2)
        `when`(mockSaleRepository.saveComprobante(any())).thenReturn(Id(1), Id(2))
        `when`(mockSaleRepository.updateComprobanteEmitido(any(), any())).thenReturn(true)
        `when`(mockSaleRepository.saveComprobanteAttempt(any())).thenReturn(Id(1), Id(2))

        // When: Crear dos comprobantes offline con el mismo CAEA
        val comprobante1Id = comprobanteService.generarComprobanteOffline(Id(1))
        val comprobante2Id = comprobanteService.generarComprobanteOffline(Id(2))

        // Then: Ambos comprobantes deben crearse exitosamente
        assertNotNull(comprobante1Id)
        assertNotNull(comprobante2Id)
        
        // Verificar que se guardaron ambos comprobantes
        verify(mockSaleRepository, times(2)).saveComprobante(any())
        
        // Verificar que ambos comprobantes tienen el mismo CAEA en caeaUtilizado
        verify(mockSaleRepository).saveComprobante(argThat { comprobante ->
            comprobante.cae == sameCaeaCode && 
            comprobante.caeaUtilizado == sameCaeaCode &&
            comprobante.tipoAutorizacion == TipoAutorizacion.CAEA
        })
    }

    @Test
    fun `should assign caeaUtilizado field correctly for offline comprobantes`() {
        // Given: Un comprobante creado con CAEA
        val caeaCode = "21234567890123"
        val comprobante = Comprobante.createFromSale(
            sale = createTestSale(Id(1)),
            tipoComprobante = "FACTURA_B",
            puntoVenta = 2,
            ventaId = Id(1),
            numeroComprobante = 1,
            tipoAutorizacion = TipoAutorizacion.CAEA
        ).withCAEA(
            cae = caeaCode,
            estado = "AUTORIZADO_OFFLINE",
            caeaUtilizado = caeaCode
        )

        // Then: El comprobante debe tener el CAEA asignado correctamente
        assertEquals(caeaCode, comprobante.cae)
        assertEquals(caeaCode, comprobante.caeaUtilizado)
        assertEquals(TipoAutorizacion.CAEA, comprobante.tipoAutorizacion)
        assertEquals("AUTORIZADO_OFFLINE", comprobante.estado)
    }

    @Test
    fun `should distinguish between CAE and CAEA comprobantes`() {
        // Given: Un comprobante CAE y un comprobante CAEA
        val caeCode = "12345678901234"
        val caeaCode = "21234567890123"
        
        val comprobanteCAE = Comprobante.createFromSale(
            sale = createTestSale(Id(1)),
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1,
            ventaId = Id(1),
            numeroComprobante = 1,
            tipoAutorizacion = TipoAutorizacion.CAE
        ).withCAE(
            cae = caeCode,
            estado = "AUTORIZADO"
        )
        
        val comprobanteCAEA = Comprobante.createFromSale(
            sale = createTestSale(Id(2)),
            tipoComprobante = "FACTURA_B",
            puntoVenta = 2,
            ventaId = Id(2),
            numeroComprobante = 1,
            tipoAutorizacion = TipoAutorizacion.CAEA
        ).withCAEA(
            cae = caeaCode,
            estado = "AUTORIZADO_OFFLINE",
            caeaUtilizado = caeaCode
        )

        // Then: Los comprobantes deben tener diferentes características
        // CAE
        assertEquals(caeCode, comprobanteCAE.cae)
        assertNull(comprobanteCAE.caeaUtilizado)
        assertEquals(TipoAutorizacion.CAE, comprobanteCAE.tipoAutorizacion)
        
        // CAEA
        assertEquals(caeaCode, comprobanteCAEA.cae)
        assertEquals(caeaCode, comprobanteCAEA.caeaUtilizado)
        assertEquals(TipoAutorizacion.CAEA, comprobanteCAEA.tipoAutorizacion)
    }

    private fun createTestSale(id: Id): Sale {
        val usuario = Usuario.create("testuser", "Test User", "Test User")
        val items = listOf(
            SaleItem.create(
                producto = "Producto Test",
                cantidad = BigDecimal("1.0"),
                precioUnitario = BigDecimal("100.0"),
                tipoIvaId = 5,
                subtotal = BigDecimal("121.0"),
                baseImponible = BigDecimal("100.0"),
                importeIva = BigDecimal("21.0")
            )
        )
        
        return Sale.create(
            id = id,
            numeroVenta = "TEST-${id.value}",
            vendedor = usuario,
            fechaVenta = LocalDateTime.now(),
            montoTotal = BigDecimal("121.0"),
            medioPago = "EFECTIVO",
            items = items
        )
    }
}
