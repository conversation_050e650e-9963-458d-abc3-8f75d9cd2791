package com.gnico.majo.integration

import com.gnico.majo.application.domain.model.*
import com.gnico.majo.infrastructure.config.PrinterConfiguration
import com.gnico.majo.infrastructure.printer.StyledTicketFormatter
import com.github.anastaciocintra.escpos.EscPos
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.io.ByteArrayOutputStream
import java.lang.reflect.Method
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.Base64

class QRCodeIntegrationTest {

    private lateinit var config: PrinterConfiguration
    private lateinit var formatter: StyledTicketFormatter
    private lateinit var comprobante: Comprobante
    private lateinit var sale: Sale

    @BeforeEach
    fun setUp() {
        config = PrinterConfiguration(
            companyName = "Test Company S.A.",
            companyCUIT = "20*********",
            companyIIBB = "*********",
            companyAddress = "Test Address 123",
            companyStart = "01/01/2020",
            companyPhone = "************",
            companyEmail = "<EMAIL>",
            companyWebsite = "www.test.com",
            printerIp = "*************",
            printerPort = 9100
        )
        
        formatter = StyledTicketFormatter(config)
        
        // Crear comprobante de prueba
        comprobante = Comprobante.fromPersistence(
            id = Id(1),
            venta = Id(1),
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1,
            numeroComprobante = 123,
            cae = "*********01234",
            fechaEmision = LocalDateTime.now(),
            fechaVencimientoCae = LocalDate.now().plusDays(10),
            impTotal = BigDecimal("200.00"),
            impTotConc = BigDecimal.ZERO,
            impNeto = BigDecimal("165.29"),
            impIva = BigDecimal("34.71"),
            impTrib = BigDecimal.ZERO,
            monId = "PES",
            monCotiz = BigDecimal.ONE,
            estado = "APROBADO"
        )
        
        // Crear venta de prueba
        val usuario = Usuario.create("testuser", "Test User", "Test User")
        // Ya no manejamos clientes - solo consumidor final
        
        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto Test",
                cantidad = BigDecimal("2.0"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21
            )
        )
        
        sale = Sale.create(
            usuario = usuario,
            items = items,
            medioPago = "EFECTIVO"
        )
    }
    
    @Test
    fun `should print QR code data and show content`() {
        println("=== PRUEBA DE INTEGRACIÓN QR CODE ===")
        
        // Acceder al método privado usando reflection para mostrar el contenido del QR
        val method: Method = StyledTicketFormatter::class.java.getDeclaredMethod(
            "generateFiscalQRData", 
            Comprobante::class.java
        )
        method.isAccessible = true
        
        val qrData = method.invoke(formatter, comprobante) as String

        println("QR Data generado: $qrData")
        println("QR Data length: ${qrData.length}")

        // Solo verificar que se genera contenido
        assert(qrData.isNotBlank()) { "QR Data debe generar contenido" }
        
        // Generar el ticket completo
        println("\n=== TICKET FISCAL CON QR ===")
        val outputStream = ByteArrayOutputStream()
        val escpos = EscPos(outputStream)
        
        formatter.formatFiscalTicket(escpos, comprobante, sale)
        
        val output = outputStream.toByteArray()
        println("Ticket generado exitosamente: ${output.size} bytes")
        
        // Verificar que el QR está incluido en el ticket
        val ticketContent = String(output, Charsets.ISO_8859_1)
        println("Ticket contiene datos QR: ${ticketContent.contains("QR") || output.size > 1000}")
        
        println("✅ Integración QR completada exitosamente")
    }
    
    private fun analyzeTLVContent(content: String) {
        var index = 0
        var tagNumber = 1
        
        while (index < content.length - 3) {
            try {
                val tag = content.substring(index, index + 2)
                val lengthStr = content.substring(index + 2, index + 4)
                val length = lengthStr.toInt()
                
                if (index + 4 + length <= content.length) {
                    val value = content.substring(index + 4, index + 4 + length)
                    
                    val description = when (tag) {
                        "01" -> "CUIT del emisor"
                        "02" -> "Tipo de comprobante"
                        "03" -> "Punto de venta"
                        "04" -> "Número de comprobante"
                        "05" -> "Importe total"
                        "06" -> "Fecha de emisión"
                        "07" -> "CAE"
                        "08" -> "Fecha vencimiento CAE"
                        else -> "Campo desconocido"
                    }
                    
                    println("Tag $tag ($description): $value (length: $length)")
                    index += 4 + length
                    tagNumber++
                } else {
                    break
                }
            } catch (e: Exception) {
                println("Error analizando en posición $index: ${e.message}")
                break
            }
        }
    }
}
