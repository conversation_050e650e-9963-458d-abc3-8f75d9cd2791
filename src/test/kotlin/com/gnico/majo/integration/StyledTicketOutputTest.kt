package com.gnico.majo.integration

import com.gnico.majo.application.domain.model.*
import com.gnico.majo.infrastructure.config.PrinterConfiguration
import com.gnico.majo.infrastructure.printer.StyledTicketFormatter
import com.github.anastaciocintra.escpos.EscPos
import com.github.anastaciocintra.output.PrinterOutputStream
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.LocalDate

/**
 * Test para generar archivos de salida que muestren exactamente
 * qué comandos ESC/POS se están enviando a la impresora
 */
class StyledTicketOutputTest {
    
    private lateinit var sale: Sale
    private lateinit var comprobante: Comprobante
    private lateinit var config: PrinterConfiguration
    private lateinit var formatter: StyledTicketFormatter
    
    @BeforeEach
    fun setup() {
        config = PrinterConfiguration(
            companyName = "MI EMPRESA S.A.",
            companyCUIT = "20*********",
            companyIIBB = "*********",
            companyAddress = "Av. Corrientes 1234, CABA",
            companyStart = "01/01/2020",
            companyPhone = "011-1234-5678",
            companyEmail = "<EMAIL>",
            companyWebsite = "www.miempresa.com",
            printerIp = "*************",
            printerPort = 9100
        )
        
        formatter = StyledTicketFormatter(config)
        
        // Crear datos de prueba realistas
        val usuario = Usuario.create("vendedor1", "Juan Pérez", "Juan P.")
        // Ya no manejamos clientes - solo consumidor final
        
        val items = listOf(
            SaleItem.create(
                productoNombre = "Coca Cola 500ml",
                cantidad = BigDecimal("2.00"),
                precioUnitario = BigDecimal("150.00"),
                tipoIva = TipoIva.IVA_21
            ),
            SaleItem.create(
                productoNombre = "Pan Lactal Integral x 500g",
                cantidad = BigDecimal("1.00"),
                precioUnitario = BigDecimal("320.50"),
                tipoIva = TipoIva.IVA_21
            ),
            SaleItem.create(
                productoNombre = "Leche Descremada 1L",
                cantidad = BigDecimal("3.00"),
                precioUnitario = BigDecimal("180.00"),
                tipoIva = TipoIva.IVA_21
            )
        )
        
        sale = Sale.create(
            usuario = usuario,
            items = items,
            medioPago = "EFECTIVO"
        )
        
        comprobante = Comprobante.fromPersistence(
            id = Id(1),
            venta = Id(1),
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1,
            numeroComprobante = 456,
            cae = "98765432109876",
            fechaEmision = LocalDateTime.now(),
            fechaVencimientoCae = LocalDate.now().plusDays(10),
            impTotal = BigDecimal("1160.50"),
            impTotConc = BigDecimal.ZERO,
            impNeto = BigDecimal("959.09"),
            impIva = BigDecimal("201.41"),
            impTrib = BigDecimal.ZERO,
            monId = "PES",
            monCotiz = BigDecimal.ONE,
            estado = "APROBADO"
        )
    }
    
    @Test
    fun `generate styled ticket output to console`() {
        println("=== SIMULACIÓN DE TICKET FISCAL CON ESTILOS ===")
        
        val outputStream = ByteArrayOutputStream()
        val escpos = EscPos(outputStream)
        
        // Generar el ticket
        formatter.formatFiscalTicket(escpos, comprobante, sale)
        
        // Convertir los bytes a string legible
        val rawOutput = outputStream.toByteArray()
        val readableOutput = convertEscPosToReadable(rawOutput)
        
        println(readableOutput)
        println("===============================================")
        
        // También mostrar ticket no fiscal
        println("=== SIMULACIÓN DE TICKET NO FISCAL CON ESTILOS ===")
        
        val outputStream2 = ByteArrayOutputStream()
        val escpos2 = EscPos(outputStream2)
        
        formatter.formatNonFiscalTicket(escpos2, sale)
        
        val rawOutput2 = outputStream2.toByteArray()
        val readableOutput2 = convertEscPosToReadable(rawOutput2)
        
        println(readableOutput2)
        println("==================================================")
        
        println("✅ Simulación completada - revisar la salida arriba")
    }
    
    @Test
    @Disabled("Habilitar para generar archivos de salida")
    fun `generate styled ticket files for analysis`() {
        val outputDir = File("build/ticket-output")
        outputDir.mkdirs()
        
        // Generar ticket fiscal
        generateTicketFile(
            outputDir, 
            "ticket-fiscal-styled.txt", 
            "TICKET FISCAL CON ESTILOS"
        ) { escpos ->
            formatter.formatFiscalTicket(escpos, comprobante, sale)
        }
        
        // Generar ticket no fiscal
        generateTicketFile(
            outputDir, 
            "ticket-no-fiscal-styled.txt", 
            "TICKET NO FISCAL CON ESTILOS"
        ) { escpos ->
            formatter.formatNonFiscalTicket(escpos, sale)
        }
        
        // Generar ticket para consumidor final (todas las ventas son así ahora)
        val saleConsumidorFinal = Sale.create(
            usuario = sale.usuario,
            items = sale.items,
            medioPago = "TARJETA_DEBITO"
        )
        
        generateTicketFile(
            outputDir, 
            "ticket-consumidor-final-styled.txt", 
            "TICKET CONSUMIDOR FINAL CON ESTILOS"
        ) { escpos ->
            formatter.formatFiscalTicket(escpos, comprobante, saleConsumidorFinal)
        }
        
        println("✅ Archivos generados en: ${outputDir.absolutePath}")
        println("📁 Revisar los archivos .txt para ver los comandos ESC/POS")
    }
    
    private fun generateTicketFile(
        outputDir: File, 
        fileName: String, 
        title: String,
        ticketGenerator: (EscPos) -> Unit
    ) {
        val outputStream = ByteArrayOutputStream()
        val escpos = EscPos(outputStream)
        
        ticketGenerator(escpos)
        
        val rawOutput = outputStream.toByteArray()
        val readableOutput = convertEscPosToReadable(rawOutput)
        
        val file = File(outputDir, fileName)
        file.writeText("=== $title ===\n\n$readableOutput")
        
        println("📄 Generado: $fileName")
    }
    
    private fun convertEscPosToReadable(bytes: ByteArray): String {
        return String(bytes, Charsets.UTF_8)
            .replace("\u001B", "[ESC]")
            .replace("\u001D", "[GS]")
            .replace("\u0001", "[ON]")
            .replace("\u0000", "[OFF]")
            .replace("\u001Ba\u0000", "[ALIGN_LEFT]")
            .replace("\u001Ba\u0001", "[ALIGN_CENTER]")
            .replace("\u001Ba\u0002", "[ALIGN_RIGHT]")
            .replace("\u001BE\u0001", "[BOLD_ON]")
            .replace("\u001BE\u0000", "[BOLD_OFF]")
            .replace("\u001D!\u0000", "[FONT_NORMAL]")
            .replace("\u001D!\u0001", "[FONT_DOUBLE_HEIGHT]")
            .replace("\u001D!\u0010", "[FONT_DOUBLE_WIDTH]")
            .replace("\u001D!\u0011", "[FONT_DOUBLE_BOTH]")
            .replace("\u001B-\u0001", "[UNDERLINE_ON]")
            .replace("\u001B-\u0000", "[UNDERLINE_OFF]")
    }
}
