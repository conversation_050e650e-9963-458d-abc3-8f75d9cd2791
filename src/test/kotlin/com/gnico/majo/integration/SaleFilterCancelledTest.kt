package com.gnico.majo.integration

import com.gnico.majo.adapter.controller.dto.SaleFilterRequest
import com.gnico.majo.application.port.out.SaleFilterCriteria
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*

class SaleFilterCancelledTest {

    @Test
    fun `should exclude cancelled sales by default in GET request`() {
        // Simular una consulta GET sin incluirCanceladas
        // GET /api/sales?page=1&size=20
        
        val filterRequest = SaleFilterRequest(
            fechaDesde = null,
            fechaHasta = null,
            usuarios = null,
            comprobanteEmitido = null,
            mediosPago = null,
            // incluirCanceladas no especificado, debe ser false por defecto
            page = 1,
            size = 20
        )

        // Verificar que por defecto excluye canceladas
        assertFalse(filterRequest.incluirCanceladas, "Por defecto debe excluir ventas canceladas")
        
        // Simular la conversión a SaleFilterCriteria
        val criteria = SaleFilterCriteria(
            fechaDesde = null,
            fechaHasta = null,
            usuarios = filterRequest.usuarios,
            comprobanteEmitido = filterRequest.comprobanteEmitido,
            mediosPago = filterRequest.mediosPago,
            incluirCanceladas = filterRequest.incluirCanceladas
        )
        
        // Verificar que el criterio también excluye canceladas
        assertFalse(criteria.incluirCanceladas, "El criterio debe excluir ventas canceladas por defecto")
    }

    @Test
    fun `should include cancelled sales when explicitly requested`() {
        // Simular una consulta GET con incluirCanceladas=true
        // GET /api/sales?incluirCanceladas=true&page=1&size=20
        
        val filterRequest = SaleFilterRequest(
            fechaDesde = null,
            fechaHasta = null,
            usuarios = null,
            comprobanteEmitido = null,
            mediosPago = null,
            incluirCanceladas = true, // Explícitamente incluir
            page = 1,
            size = 20
        )

        // Verificar que incluye canceladas cuando se solicita
        assertTrue(filterRequest.incluirCanceladas, "Debe incluir ventas canceladas cuando se solicita")
        
        // Simular la conversión a SaleFilterCriteria
        val criteria = SaleFilterCriteria(
            fechaDesde = null,
            fechaHasta = null,
            usuarios = filterRequest.usuarios,
            comprobanteEmitido = filterRequest.comprobanteEmitido,
            mediosPago = filterRequest.mediosPago,
            incluirCanceladas = filterRequest.incluirCanceladas
        )
        
        // Verificar que el criterio incluye canceladas
        assertTrue(criteria.incluirCanceladas, "El criterio debe incluir ventas canceladas cuando se solicita")
    }

    @Test
    fun `should verify default behavior explanation`() {
        println("=== EXPLICACIÓN DEL COMPORTAMIENTO ===")
        println()
        println("🔍 PROBLEMA IDENTIFICADO:")
        println("Si las ventas aparecen como canceladas en la base de datos pero NO en la API,")
        println("es porque el filtro por defecto está EXCLUYENDO las ventas canceladas.")
        println()
        println("📋 SOLUCIÓN:")
        println("Para ver las ventas canceladas, debes usar:")
        println("GET /api/sales?incluirCanceladas=true")
        println()
        println("🔧 COMPORTAMIENTO ACTUAL:")
        println("- GET /api/sales                    → Excluye ventas canceladas (por defecto)")
        println("- GET /api/sales?incluirCanceladas=false → Excluye ventas canceladas (explícito)")
        println("- GET /api/sales?incluirCanceladas=true  → Incluye ventas canceladas")
        println()
        println("📊 ENDPOINTS ESPECÍFICOS:")
        println("- GET /api/sales/cancelled          → Solo ventas canceladas")
        println("- GET /api/sales/cancellation-stats → Estadísticas de cancelaciones")
        println()
        
        // Test que demuestra el comportamiento
        val defaultRequest = SaleFilterRequest()
        val includeRequest = SaleFilterRequest(incluirCanceladas = true)
        
        assertFalse(defaultRequest.incluirCanceladas, "Por defecto excluye canceladas")
        assertTrue(includeRequest.incluirCanceladas, "Con parámetro incluye canceladas")
        
        println("✅ VERIFICACIÓN EXITOSA: El comportamiento es correcto por diseño.")
        println("   Las ventas canceladas se excluyen por defecto para reportes normales.")
        println("   Para auditorías completas, usar incluirCanceladas=true")
    }

    @Test
    fun `should demonstrate query parameter parsing`() {
        // Simular el parsing de query parameters como en SaleRoutes.kt
        
        // Caso 1: Sin parámetro incluirCanceladas
        val incluirCanceladas1: Boolean? = null // No especificado en query
        val finalValue1 = incluirCanceladas1 ?: false // Valor por defecto
        assertFalse(finalValue1, "Sin parámetro debe ser false")
        
        // Caso 2: Con parámetro incluirCanceladas=true
        val incluirCanceladas2: Boolean? = true // Especificado como true
        val finalValue2 = incluirCanceladas2 ?: false
        assertTrue(finalValue2, "Con parámetro true debe ser true")
        
        // Caso 3: Con parámetro incluirCanceladas=false
        val incluirCanceladas3: Boolean? = false // Especificado como false
        val finalValue3 = incluirCanceladas3 ?: false
        assertFalse(finalValue3, "Con parámetro false debe ser false")
        
        println("🔍 PARSING DE PARÁMETROS:")
        println("- Sin parámetro: $finalValue1")
        println("- Con true: $finalValue2") 
        println("- Con false: $finalValue3")
    }

    @Test
    fun `should verify repository filter logic`() {
        // Simular la lógica del repositorio
        
        // Caso 1: incluirCanceladas = false (por defecto)
        val criteria1 = SaleFilterCriteria(incluirCanceladas = false)
        val shouldAddFilter1 = !criteria1.incluirCanceladas
        assertTrue(shouldAddFilter1, "Debe agregar filtro CANCELADA = false")
        
        // Caso 2: incluirCanceladas = true
        val criteria2 = SaleFilterCriteria(incluirCanceladas = true)
        val shouldAddFilter2 = !criteria2.incluirCanceladas
        assertFalse(shouldAddFilter2, "NO debe agregar filtro, incluye todas")
        
        println("🗄️ LÓGICA DE REPOSITORIO:")
        println("- incluirCanceladas=false → Agrega WHERE cancelada = false")
        println("- incluirCanceladas=true  → No agrega filtro (incluye todas)")
    }
}
