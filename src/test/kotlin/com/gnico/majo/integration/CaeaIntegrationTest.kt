package com.gnico.majo.integration

import com.gnico.majo.application.domain.model.CaeaCode
import com.gnico.majo.application.domain.model.EstadoCaea
import com.gnico.majo.application.port.out.CaeaRepositoryPort
import com.gnico.majo.application.port.out.AfipService
import com.gnico.majo.application.domain.model.Sale
import com.gnico.majo.application.domain.model.SaleItem
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.TipoOperacionAfip
import com.gnico.majo.application.domain.model.Usuario
import com.gnico.majo.application.domain.model.TipoIva
import com.gnico.majo.infrastructure.config.ComprobanteConfigurationService
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import kotlinx.coroutines.runBlocking
import java.time.LocalDate
import java.time.LocalDateTime
import java.math.BigDecimal
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import kotlin.test.assertFalse

/**
 * Test de funcionalidad CAEA
 * Verifica que los modelos de dominio y lógica de negocio funcionen correctamente
 */
class CaeaIntegrationTest {

    @BeforeEach
    fun setup() {
        // Setup básico para tests
    }
    
    @Test
    fun `should create and validate CAEA codes`() {
        println("=== TEST: Crear y validar códigos CAEA ===")

        // Crear CAEA de prueba
        val caea = CaeaCode.create(
            caea = "21234567890123",
            puntoVenta = ComprobanteConfigurationService.getDefaultPuntoVentaOffline(),
            periodo = "202507",
            fechaDesde = LocalDate.of(2025, 7, 1),
            fechaHasta = LocalDate.of(2025, 7, 31),
            orden = 1
        )

        // Verificar propiedades del CAEA
        assertEquals("21234567890123", caea.caea)
        assertEquals(2, caea.puntoVenta)
        assertEquals("202507", caea.periodo)
        assertEquals(1, caea.orden)
        assertEquals(EstadoCaea.ACTIVO, caea.estado)

        // Verificar validez
        assertTrue(caea.isValid(LocalDate.of(2025, 7, 15)))
        assertFalse(caea.isExpired(LocalDate.of(2025, 7, 15)))

        println("✓ CAEA creado y validado correctamente")
        println("  - CAEA: ${caea.caea}")
        println("  - Punto de venta: ${caea.puntoVenta}")
        println("  - Período: ${caea.periodo}")
        println("  - Estado: ${caea.estado}")
    }
    
    @Test
    fun `should handle comprobante number logic`() {
        println("=== TEST: Lógica de números de comprobantes ===")

        // Crear CAEA de prueba
        val caea = CaeaCode.create(
            caea = "21234567890124",
            puntoVenta = ComprobanteConfigurationService.getDefaultPuntoVentaOffline(),
            periodo = "202507",
            fechaDesde = LocalDate.of(2025, 7, 1),
            fechaHasta = LocalDate.of(2025, 7, 31),
            orden = 1
        )

        // Verificar números iniciales
        assertEquals(0L, caea.getLastNumberForType("FACTURA_B"))
        assertEquals(0L, caea.getLastNumberForType("NOTA_CREDITO_B"))
        assertEquals(0L, caea.getLastNumberForType("NOTA_DEBITO_B"))

        // Verificar siguiente número
        assertEquals(1L, caea.getNextNumberForType("FACTURA_B"))
        assertEquals(1L, caea.getNextNumberForType("NOTA_CREDITO_B"))

        // Simular actualización de números
        val caeaActualizado = caea.withUpdatedNumber("FACTURA_B", 5L)
        assertEquals(5L, caeaActualizado.getLastNumberForType("FACTURA_B"))
        assertEquals(6L, caeaActualizado.getNextNumberForType("FACTURA_B"))

        println("✓ Lógica de números de comprobantes funciona correctamente")
        println("  - Números iniciales: 0")
        println("  - Siguiente número: 1")
        println("  - Actualización: 5 -> siguiente 6")
    }
    
    @Test
    fun `should handle CAEA expiration logic`() {
        println("=== TEST: Lógica de vencimiento de CAEAs ===")

        // Crear CAEA vencido
        val caeaVencido = CaeaCode.create(
            caea = "21234567890125",
            puntoVenta = ComprobanteConfigurationService.getDefaultPuntoVentaOffline(),
            periodo = "202506",
            fechaDesde = LocalDate.of(2025, 6, 1),
            fechaHasta = LocalDate.of(2025, 6, 30),
            orden = 1
        )

        // Verificar que está activo inicialmente
        assertEquals(EstadoCaea.ACTIVO, caeaVencido.estado)

        // Verificar validez en diferentes fechas
        assertTrue(caeaVencido.isValid(LocalDate.of(2025, 6, 15))) // Dentro del período
        assertFalse(caeaVencido.isValid(LocalDate.of(2025, 7, 15))) // Fuera del período
        assertTrue(caeaVencido.isExpired(LocalDate.of(2025, 7, 15))) // Vencido

        // Marcar como vencido
        val caeaMaracadoVencido = caeaVencido.markAsExpired()
        assertEquals(EstadoCaea.VENCIDO, caeaMaracadoVencido.estado)
        assertFalse(caeaMaracadoVencido.isValid(LocalDate.of(2025, 6, 15))) // Ya no válido aunque esté en período

        // Marcar como agotado
        val caeaAgotado = caeaVencido.markAsExhausted()
        assertEquals(EstadoCaea.AGOTADO, caeaAgotado.estado)

        println("✓ Lógica de vencimiento funciona correctamente")
        println("  - Válido en período: ✓")
        println("  - Inválido fuera de período: ✓")
        println("  - Marcado como vencido: ✓")
        println("  - Marcado como agotado: ✓")
    }
    
    @Test
    fun `should test CAEA configuration`() {
        println("=== TEST: Configuración CAEA ===")

        // Verificar configuración de punto de venta offline
        val puntoVentaOffline = ComprobanteConfigurationService.getDefaultPuntoVentaOffline()
        val puntoVentaOnline = ComprobanteConfigurationService.getDefaultPuntoVenta()

        // Los puntos de venta deben ser diferentes
        assertTrue(puntoVentaOffline != puntoVentaOnline)
        assertEquals(2, puntoVentaOffline) // Configurado en .env
        assertEquals(1, puntoVentaOnline)  // Configurado en .env

        println("✓ Configuración CAEA correcta")
        println("  - Punto de venta online: $puntoVentaOnline")
        println("  - Punto de venta offline: $puntoVentaOffline")
        println("  - Puntos de venta independientes: ✓")
    }
    
    @Test
    fun `should validate CAEA business rules`() {
        println("=== TEST: Validar reglas de negocio CAEA ===")
        
        // Test validación de formato CAEA
        try {
            CaeaCode.create(
                caea = "123", // Muy corto
                puntoVenta = 2,
                periodo = "202507",
                fechaDesde = LocalDate.of(2025, 7, 1),
                fechaHasta = LocalDate.of(2025, 7, 31),
                orden = 1
            )
            assert(false) { "Debería fallar con CAEA inválido" }
        } catch (e: IllegalArgumentException) {
            println("✓ Validación de formato CAEA funciona")
        }
        
        // Test validación de orden
        try {
            CaeaCode.create(
                caea = "21234567890123",
                puntoVenta = 2,
                periodo = "202507",
                fechaDesde = LocalDate.of(2025, 7, 1),
                fechaHasta = LocalDate.of(2025, 7, 31),
                orden = 3 // Inválido
            )
            assert(false) { "Debería fallar con orden inválido" }
        } catch (e: IllegalArgumentException) {
            println("✓ Validación de orden funciona")
        }
        
        // Test validación de fechas
        try {
            CaeaCode.create(
                caea = "21234567890123",
                puntoVenta = 2,
                periodo = "202507",
                fechaDesde = LocalDate.of(2025, 7, 31),
                fechaHasta = LocalDate.of(2025, 7, 1), // Fecha hasta menor
                orden = 1
            )
            assert(false) { "Debería fallar con fechas inválidas" }
        } catch (e: IllegalArgumentException) {
            println("✓ Validación de fechas funciona")
        }
        
        println("✓ Todas las validaciones de reglas de negocio funcionan correctamente")
    }
}
