package com.gnico.majo.integration

import com.gnico.majo.adapter.printer.EscPosPrinterAdapter
import com.gnico.majo.adapter.printer.PrinterConnectionException
import com.gnico.majo.application.domain.model.*
import com.gnico.majo.infrastructure.config.PrinterConfiguration
import com.gnico.majo.infrastructure.printer.StyledTicketFormatter
import com.github.anastaciocintra.escpos.EscPos
import java.io.ByteArrayOutputStream
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.LocalDate

/**
 * Clase utilitaria para probar la impresión de tickets con estilos
 * Ejecutar como aplicación independiente para probar diferentes escenarios
 */
object PrinterTestRunner {
    
    @JvmStatic
    fun main(args: Array<String>) {
        println("🖨️  PROBADOR DE TICKETS CON ESTILOS ESC/POS")
        println("==========================================")
        
        val testData = createTestData()
        
        if (args.isNotEmpty() && args[0] == "--physical") {
            testWithPhysicalPrinter(testData)
        } else {
            testWithSimulation(testData)
        }
    }
    
    private fun testWithPhysicalPrinter(testData: TestData) {
        println("🖨️  MODO: Impresión física")
        println("⚠️  Asegúrate de tener una impresora conectada y configurada en .env")
        println()
        
        val printerAdapter = EscPosPrinterAdapter()
        
        try {
            println("1️⃣  Imprimiendo ticket fiscal...")
            printerAdapter.printTicket(testData.comprobante, testData.saleWithClient)
            println("✅ Ticket fiscal impreso")
            Thread.sleep(2000)
            
            println("2️⃣  Imprimiendo ticket no fiscal...")
            printerAdapter.printTicket(null, testData.saleWithClient)
            println("✅ Ticket no fiscal impreso")
            Thread.sleep(2000)
            
            println("3️⃣  Imprimiendo ticket consumidor final...")
            printerAdapter.printTicket(testData.comprobante, testData.saleWithoutClient)
            println("✅ Ticket consumidor final impreso")
            
            println()
            println("🎉 ¡Impresión completada!")
            println("📋 Verificar en los tickets físicos:")
            println("   • Nombre de empresa en tamaño doble y centrado")
            println("   • Información fiscal en negrita")
            println("   • 'IVA Responsable Inscripto' subrayado")
            println("   • Tipo de comprobante en negrita y tamaño doble")
            println("   • 'A CONSUMIDOR FINAL' en negrita cuando no hay cliente")
            println("   • Totales en negrita")
            println("   • Separadores con líneas punteadas")
            
        } catch (e: PrinterConnectionException) {
            println("❌ Error de conexión: ${e.message}")
            println("💡 Verificar:")
            println("   • PRINTER_IP y PRINTER_PORT en .env")
            println("   • Impresora encendida y conectada")
            println("   • Red accesible")
        } catch (e: Exception) {
            println("❌ Error inesperado: ${e.message}")
            e.printStackTrace()
        }
    }
    
    private fun testWithSimulation(testData: TestData) {
        println("🖥️  MODO: Simulación (sin impresora física)")
        println("📄 Mostrando comandos ESC/POS que se enviarían a la impresora")
        println()
        
        val config = PrinterConfiguration.fromEnvironment()
        val formatter = StyledTicketFormatter(config)
        
        // Test 1: Ticket fiscal
        println("1️⃣  === TICKET FISCAL CON CLIENTE ===")
        simulateTicket { escpos ->
            formatter.formatFiscalTicket(escpos, testData.comprobante, testData.saleWithClient)
        }
        
        // Test 2: Ticket no fiscal
        println("2️⃣  === TICKET NO FISCAL ===")
        simulateTicket { escpos ->
            formatter.formatNonFiscalTicket(escpos, testData.saleWithClient)
        }
        
        // Test 3: Ticket consumidor final
        println("3️⃣  === TICKET FISCAL CONSUMIDOR FINAL ===")
        simulateTicket { escpos ->
            formatter.formatFiscalTicket(escpos, testData.comprobante, testData.saleWithoutClient)
        }
        
        println("✅ Simulación completada")
        println("💡 Para probar con impresora física, ejecutar con: --physical")
    }
    
    private fun simulateTicket(ticketGenerator: (EscPos) -> Unit) {
        val outputStream = ByteArrayOutputStream()
        val escpos = EscPos(outputStream)
        
        ticketGenerator(escpos)
        
        val rawOutput = outputStream.toByteArray()
        val readableOutput = convertEscPosToReadable(rawOutput)
        
        println(readableOutput)
        println("=" * 50)
        println()
    }
    
    private fun convertEscPosToReadable(bytes: ByteArray): String {
        return String(bytes, Charsets.UTF_8)
            .replace("\u001B", "⚡[ESC]")
            .replace("\u001D", "🔧[GS]")
            .replace("\u0001", "[ON]")
            .replace("\u0000", "[OFF]")
            .replace("⚡[ESC]a[OFF]", "📍[ALIGN_LEFT]")
            .replace("⚡[ESC]a[ON]", "📍[ALIGN_CENTER]")
            .replace("⚡[ESC]a\u0002", "📍[ALIGN_RIGHT]")
            .replace("⚡[ESC]E[ON]", "💪[BOLD_ON]")
            .replace("⚡[ESC]E[OFF]", "💪[BOLD_OFF]")
            .replace("🔧[GS]![OFF]", "📏[FONT_NORMAL]")
            .replace("🔧[GS]![ON]", "📏[FONT_DOUBLE_HEIGHT]")
            .replace("🔧[GS]!\u0010", "📏[FONT_DOUBLE_WIDTH]")
            .replace("🔧[GS]!\u0011", "📏[FONT_DOUBLE_BOTH]")
            .replace("⚡[ESC]-[ON]", "📑[UNDERLINE_ON]")
            .replace("⚡[ESC]-[OFF]", "📑[UNDERLINE_OFF]")
    }
    
    private fun createTestData(): TestData {
        val usuario = Usuario.create("vendedor1", "María González", "María G.")
        // Ya no manejamos clientes - solo consumidor final
        
        val items = listOf(
            SaleItem.create(
                productoNombre = "Coca Cola 600ml",
                cantidad = BigDecimal("2.00"),
                precioUnitario = BigDecimal("180.00"),
                tipoIva = TipoIva.IVA_21
            ),
            SaleItem.create(
                productoNombre = "Pan Francés x 4 unidades",
                cantidad = BigDecimal("1.00"),
                precioUnitario = BigDecimal("250.00"),
                tipoIva = TipoIva.IVA_21
            ),
            SaleItem.create(
                productoNombre = "Leche Entera La Serenísima 1L",
                cantidad = BigDecimal("2.00"),
                precioUnitario = BigDecimal("195.50"),
                tipoIva = TipoIva.IVA_21
            )
        )
        
        val saleWithClient = Sale.create(
            usuario = usuario,
            items = items,
            medioPago = "EFECTIVO"
        )

        val saleWithoutClient = Sale.create(
            usuario = usuario,
            items = items,
            medioPago = "TARJETA_CREDITO"
        )
        
        val comprobante = Comprobante.fromPersistence(
            id = Id(1),
            venta = Id(1),
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1,
            numeroComprobante = 789,
            cae = "12345678901234",
            fechaEmision = LocalDateTime.now(),
            fechaVencimientoCae = LocalDate.now().plusDays(10),
            impTotal = BigDecimal("821.00"),
            impTotConc = BigDecimal.ZERO,
            impNeto = BigDecimal("678.51"),
            impIva = BigDecimal("142.49"),
            impTrib = BigDecimal.ZERO,
            monId = "PES",
            monCotiz = BigDecimal.ONE,
            estado = "APROBADO",
            tipoAutorizacion = TipoAutorizacion.CAE
        )
        
        return TestData(saleWithClient, saleWithoutClient, comprobante)
    }
    
    private data class TestData(
        val saleWithClient: Sale,
        val saleWithoutClient: Sale,
        val comprobante: Comprobante
    )
    
    private operator fun String.times(n: Int): String = this.repeat(n)
}
