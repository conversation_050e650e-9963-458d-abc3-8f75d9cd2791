package com.gnico.majo.integration

import com.gnico.majo.application.domain.model.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue
import java.math.BigDecimal

class ComprobanteAsociadoIntegrationTest {

    @Test
    fun `AfipCAERequest debe validar comprobante asociado para notas de credito`() {
        // Given
        val sale = createTestSale()
        val credentials = createTestCredentials()
        val comprobanteAsociado = ComprobanteAsociado.create("FACTURA_B", 1, 123)

        // When - Crear request para nota de crédito CON comprobante asociado
        val request = AfipCAERequest(
            sale = sale,
            tipoComprobante = TipoComprobanteAfip.NOTA_CREDITO_B,
            puntoVenta = 1,
            credentials = credentials,
            comprobanteAsociado = comprobanteAsociado
        )

        // Then
        assertNotNull(request.comprobanteAsociado)
        assertEquals("FACTURA_B", request.comprobanteAsociado!!.tipo)
        assertEquals(1, request.comprobanteAsociado!!.puntoVenta)
    }

    @Test
    fun `AfipCAERequest debe fallar si nota de credito no tiene comprobante asociado`() {
        // Given
        val sale = createTestSale()
        val credentials = createTestCredentials()

        // When & Then - Crear request para nota de crédito SIN comprobante asociado
        assertThrows<IllegalArgumentException> {
            AfipCAERequest(
                sale = sale,
                tipoComprobante = TipoComprobanteAfip.NOTA_CREDITO_B,
                puntoVenta = 1,
                credentials = credentials,
                comprobanteAsociado = null
            )
        }
    }

    @Test
    fun `AfipCAERequest debe fallar si punto de venta no coincide con comprobante asociado`() {
        // Given
        val sale = createTestSale()
        val credentials = createTestCredentials()
        val comprobanteAsociado = ComprobanteAsociado.create("FACTURA_B", 2, 123) // PV diferente

        // When & Then
        assertThrows<IllegalArgumentException> {
            AfipCAERequest(
                sale = sale,
                tipoComprobante = TipoComprobanteAfip.NOTA_CREDITO_B,
                puntoVenta = 1, // PV diferente al del comprobante asociado
                credentials = credentials,
                comprobanteAsociado = comprobanteAsociado
            )
        }
    }

    @Test
    fun `AfipCAERequest debe permitir facturas sin comprobante asociado`() {
        // Given
        val sale = createTestSale()
        val credentials = createTestCredentials()

        // When
        val request = AfipCAERequest(
            sale = sale,
            tipoComprobante = TipoComprobanteAfip.FACTURA_B,
            puntoVenta = 1,
            credentials = credentials,
            comprobanteAsociado = null
        )

        // Then
        assertNull(request.comprobanteAsociado)
    }

    @Test
    fun `ComprobanteAsociado debe mapear correctamente tipos a codigos AFIP`() {
        // Given & When & Then
        val facturaB = ComprobanteAsociado.create("FACTURA_B", 1, 123)
        assertEquals(6, facturaB.getTipoComprobanteAfipCodigo())

        val notaCreditoB = ComprobanteAsociado.create("NOTA_CREDITO_B", 1, 124)
        assertEquals(8, notaCreditoB.getTipoComprobanteAfipCodigo())

        val notaDebitoB = ComprobanteAsociado.create("NOTA_DEBITO_B", 1, 125)
        assertEquals(7, notaDebitoB.getTipoComprobanteAfipCodigo())
    }

    @Test
    fun `Comprobante debe incluir comprobante asociado en factory method`() {
        // Given
        val sale = createTestSale()
        val comprobanteAsociado = ComprobanteAsociado.create("FACTURA_B", 1, 123)

        // When
        val comprobante = Comprobante.createFromSale(
            sale = sale,
            tipoComprobante = "NOTA_CREDITO_B",
            puntoVenta = 1,
            ventaId = Id(1),
            numeroComprobante = 2001, // Número de prueba
            tipoAutorizacion = TipoAutorizacion.CAE,
            comprobanteAsociado = comprobanteAsociado
        )

        // Then
        assertNotNull(comprobante.comprobanteAsociado)
        assertEquals("FACTURA_B", comprobante.comprobanteAsociado!!.tipo)
        assertEquals(1, comprobante.comprobanteAsociado!!.puntoVenta)
        assertEquals(123, comprobante.comprobanteAsociado!!.numeroComprobante)
        assertTrue(comprobante.requiereComprobanteAsociado())
    }

    @Test
    fun `Comprobante debe mantener comprobante asociado en metodo withCAE`() {
        // Given
        val sale = createTestSale()
        val comprobanteAsociado = ComprobanteAsociado.create("FACTURA_B", 1, 123)
        val comprobante = Comprobante.createFromSale(
            sale = sale,
            tipoComprobante = "NOTA_CREDITO_B",
            puntoVenta = 1,
            ventaId = Id(1),
            numeroComprobante = 2002, // Número de prueba
            tipoAutorizacion = TipoAutorizacion.CAE,
            comprobanteAsociado = comprobanteAsociado
        )

        // When
        val comprobanteConCAE = comprobante.withCAE("12345678901234", "EMITIDO")

        // Then
        assertNotNull(comprobanteConCAE.comprobanteAsociado)
        assertEquals("FACTURA_B", comprobanteConCAE.comprobanteAsociado!!.tipo)
        assertEquals("12345678901234", comprobanteConCAE.cae)
        assertEquals("EMITIDO", comprobanteConCAE.estado)
    }

    private fun createTestSale(): Sale {
        val usuario = Usuario("testuser", "Usuario Test", "Usuario Test", true)
        return Sale.create(
            usuario = usuario,
            items = listOf(
                SaleItem.create(
                    productoNombre = "Producto Test",
                    cantidad = BigDecimal.ONE,
                    precioUnitario = BigDecimal("121.00"), // Precio con IVA incluido
                    tipoIva = TipoIva.IVA_21
                )
            ),
            medioPago = "EFECTIVO"
        )
    }

    private fun createTestCredentials(): AfipCredentials {
        return AfipCredentials(
            token = "test-token",
            sign = "test-sign",
            cuit = 20123456789L
        )
    }
}
