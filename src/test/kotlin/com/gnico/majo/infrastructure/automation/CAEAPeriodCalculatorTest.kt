package com.gnico.majo.infrastructure.automation

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import java.time.LocalDate

/**
 * Tests para CAEAPeriodCalculator
 */
class CAEAPeriodCalculatorTest {

    @Test
    fun `getPeriodoActual should return correct period for first half of month`() {
        // Arrange
        val fecha = LocalDate.of(2025, 7, 10) // 10 de julio
        
        // Act
        val periodo = CAEAPeriodCalculator.getPeriodoActual(fecha)
        
        // Assert
        assertEquals("202507", periodo.periodo)
        assertEquals(1, periodo.orden)
        assertEquals(LocalDate.of(2025, 7, 1), periodo.fechaInicio)
        assertEquals(LocalDate.of(2025, 7, 15), periodo.fechaFin)
        assertEquals(LocalDate.of(2025, 7, 23), periodo.fechaLimiteInforme) // 15 + 8 días
    }

    @Test
    fun `getPeriodoActual should return correct period for second half of month`() {
        // Arrange
        val fecha = LocalDate.of(2025, 7, 20) // 20 de julio
        
        // Act
        val periodo = CAEAPeriodCalculator.getPeriodoActual(fecha)
        
        // Assert
        assertEquals("202507", periodo.periodo)
        assertEquals(2, periodo.orden)
        assertEquals(LocalDate.of(2025, 7, 16), periodo.fechaInicio)
        assertEquals(LocalDate.of(2025, 7, 31), periodo.fechaFin)
        assertEquals(LocalDate.of(2025, 8, 8), periodo.fechaLimiteInforme) // 31 + 8 días
    }

    @Test
    fun `getProximoPeriodo should return correct next period from first half`() {
        // Arrange
        val fecha = LocalDate.of(2025, 7, 10) // Estamos en orden 1
        
        // Act
        val proximoPeriodo = CAEAPeriodCalculator.getProximoPeriodo(fecha)
        
        // Assert
        assertEquals("202507", proximoPeriodo.periodo)
        assertEquals(2, proximoPeriodo.orden)
        assertEquals(LocalDate.of(2025, 7, 16), proximoPeriodo.fechaInicio)
        assertEquals(LocalDate.of(2025, 7, 31), proximoPeriodo.fechaFin)
    }

    @Test
    fun `getProximoPeriodo should return correct next period from second half`() {
        // Arrange
        val fecha = LocalDate.of(2025, 7, 20) // Estamos en orden 2
        
        // Act
        val proximoPeriodo = CAEAPeriodCalculator.getProximoPeriodo(fecha)
        
        // Assert
        assertEquals("202508", proximoPeriodo.periodo)
        assertEquals(1, proximoPeriodo.orden)
        assertEquals(LocalDate.of(2025, 8, 1), proximoPeriodo.fechaInicio)
        assertEquals(LocalDate.of(2025, 8, 15), proximoPeriodo.fechaFin)
    }

    @Test
    fun `puedeSolicitarProximoPeriodo should return true within 4 days before`() {
        // Arrange - 4 días antes del 16 de julio (próximo período orden 2)
        val fecha = LocalDate.of(2025, 7, 12)
        
        // Act
        val puedeSolicitar = CAEAPeriodCalculator.puedeSolicitarProximoPeriodo(fecha)
        
        // Assert
        assertTrue(puedeSolicitar)
    }

    @Test
    fun `puedeSolicitarProximoPeriodo should return false more than 4 days before`() {
        // Arrange - 5 días antes del 16 de julio
        val fecha = LocalDate.of(2025, 7, 11)
        
        // Act
        val puedeSolicitar = CAEAPeriodCalculator.puedeSolicitarProximoPeriodo(fecha)
        
        // Assert
        assertFalse(puedeSolicitar)
    }

    @Test
    fun `getPeriodosVencidosParaInformar should return periods within 8 days`() {
        // Arrange - 5 días después del fin del período 1 de julio (15 + 5 = 20 julio)
        val fecha = LocalDate.of(2025, 7, 20)
        
        // Act
        val periodosVencidos = CAEAPeriodCalculator.getPeriodosVencidosParaInformar(fecha)
        
        // Assert
        assertTrue(periodosVencidos.any { 
            it.periodo == "202507" && it.orden == 1 
        })
    }

    @Test
    fun `getPeriodosVencidosSinInformar should return periods past 8 days`() {
        // Arrange - 10 días después del fin del período 1 de julio (15 + 10 = 25 julio)
        val fecha = LocalDate.of(2025, 7, 25)
        
        // Act
        val periodosNoInformables = CAEAPeriodCalculator.getPeriodosVencidosSinInformar(fecha)
        
        // Assert
        assertTrue(periodosNoInformables.any { 
            it.periodo == "202507" && it.orden == 1 
        })
    }

    @Test
    fun `periodo states should be consistent`() {
        // Arrange
        val fechaDentro = LocalDate.of(2025, 7, 10)
        val fechaVencido = LocalDate.of(2025, 7, 20)
        val fechaPasadoLimite = LocalDate.of(2025, 7, 25)
        
        val periodo = CAEAPeriodCalculator.getPeriodoActual(LocalDate.of(2025, 7, 10))
        
        // Act & Assert
        assertTrue(periodo.estaVigente(fechaDentro))
        assertFalse(periodo.estaVencido(fechaDentro))
        assertFalse(periodo.puedeInformarse(fechaDentro))
        assertFalse(periodo.yaNoSePuedeInformar(fechaDentro))
        
        assertFalse(periodo.estaVigente(fechaVencido))
        assertTrue(periodo.estaVencido(fechaVencido))
        assertTrue(periodo.puedeInformarse(fechaVencido))
        assertFalse(periodo.yaNoSePuedeInformar(fechaVencido))
        
        assertFalse(periodo.estaVigente(fechaPasadoLimite))
        assertTrue(periodo.estaVencido(fechaPasadoLimite))
        assertFalse(periodo.puedeInformarse(fechaPasadoLimite))
        assertTrue(periodo.yaNoSePuedeInformar(fechaPasadoLimite))
    }

    @Test
    fun `toPeriodoString should format correctly`() {
        // Arrange
        val yearMonth = java.time.YearMonth.of(2025, 7)
        
        // Act
        val periodoString = CAEAPeriodCalculator.toPeriodoString(yearMonth)
        
        // Assert
        assertEquals("202507", periodoString)
    }

    @Test
    fun `getEstadoPeriodos should return detailed status`() {
        // Arrange
        val fecha = LocalDate.of(2025, 7, 12)
        
        // Act
        val estado = CAEAPeriodCalculator.getEstadoPeriodos(fecha)
        
        // Assert
        assertTrue(estado.contains("202507-1"))
        assertTrue(estado.contains("202507-2"))
        assertTrue(estado.contains("Puede solicitar próximo: true"))
    }

    @Test
    fun `edge case - last day of month should be order 2`() {
        // Arrange
        val fecha = LocalDate.of(2025, 2, 28) // Último día de febrero
        
        // Act
        val periodo = CAEAPeriodCalculator.getPeriodoActual(fecha)
        
        // Assert
        assertEquals("202502", periodo.periodo)
        assertEquals(2, periodo.orden)
        assertEquals(LocalDate.of(2025, 2, 16), periodo.fechaInicio)
        assertEquals(LocalDate.of(2025, 2, 28), periodo.fechaFin)
    }

    @Test
    fun `edge case - day 15 should be order 1`() {
        // Arrange
        val fecha = LocalDate.of(2025, 7, 15) // Día 15
        
        // Act
        val periodo = CAEAPeriodCalculator.getPeriodoActual(fecha)
        
        // Assert
        assertEquals("202507", periodo.periodo)
        assertEquals(1, periodo.orden)
    }

    @Test
    fun `edge case - day 16 should be order 2`() {
        // Arrange
        val fecha = LocalDate.of(2025, 7, 16) // Día 16

        // Act
        val periodo = CAEAPeriodCalculator.getPeriodoActual(fecha)

        // Assert
        assertEquals("202507", periodo.periodo)
        assertEquals(2, periodo.orden)
    }

    @Test
    fun `createPeriodoFromCAEA should create correct period`() {
        // Arrange
        val periodoString = "202507"
        val orden = 1

        // Act
        val periodo = CAEAPeriodCalculator.createPeriodoFromCAEA(periodoString, orden)

        // Assert
        assertEquals("202507", periodo.periodo)
        assertEquals(1, periodo.orden)
        assertEquals(LocalDate.of(2025, 7, 1), periodo.fechaInicio)
        assertEquals(LocalDate.of(2025, 7, 15), periodo.fechaFin)
        assertEquals(LocalDate.of(2025, 7, 23), periodo.fechaLimiteInforme)
    }

    @Test
    fun `scenario for July 28 2025 should be correct`() {
        // Arrange - Fecha actual del problema reportado
        val fecha = LocalDate.of(2025, 7, 28)

        // Act
        val periodoActual = CAEAPeriodCalculator.getPeriodoActual(fecha)
        val proximoPeriodo = CAEAPeriodCalculator.getProximoPeriodo(fecha)
        val puedeSolicitarProximo = CAEAPeriodCalculator.puedeSolicitarProximoPeriodo(fecha)

        // Assert
        assertEquals("202507", periodoActual.periodo)
        assertEquals(2, periodoActual.orden) // Estamos en orden 2 (16-31 julio)

        assertEquals("202508", proximoPeriodo.periodo)
        assertEquals(1, proximoPeriodo.orden) // Próximo es orden 1 agosto

        assertTrue(puedeSolicitarProximo) // Podemos solicitar agosto orden 1 (faltan 4 días)
    }
}
