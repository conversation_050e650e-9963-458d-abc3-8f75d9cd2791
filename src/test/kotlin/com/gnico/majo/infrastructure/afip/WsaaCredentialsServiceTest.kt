package com.gnico.majo.infrastructure.afip

import com.gnico.majo.application.domain.model.WsaaCredentials
import com.gnico.majo.application.domain.model.AfipCredentials
import com.gnico.majo.application.domain.model.EstadoCredencial
import com.gnico.majo.application.port.out.WsaaCredentialsRepository
import com.gnico.majo.infrastructure.afip.webservices.WsaaClientImpl
import com.gnico.majo.infrastructure.afip.webservices.AfipConfiguration
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Assertions.*
import org.mockito.kotlin.*
import java.time.LocalDateTime

class WsaaCredentialsServiceTest {
    
    private lateinit var credentialsRepository: WsaaCredentialsRepository
    private lateinit var wsaaClient: WsaaClientImpl
    private lateinit var configuration: AfipConfiguration
    private lateinit var wsaaCredentialsService: WsaaCredentialsService
    
    @BeforeEach
    fun setUp() {
        credentialsRepository = mock()
        wsaaClient = mock()
        configuration = AfipConfiguration(
            cuit = 20349249902L,
            certificatePath = "test.p12",
            certificatePassword = "test",
            isProduction = false
        )
        
        wsaaCredentialsService = WsaaCredentialsService(
            credentialsRepository,
            wsaaClient,
            configuration
        )
    }
    
    @Test
    fun `test WsaaCredentials domain model creation`() {
        val credentials = WsaaCredentials.create(
            servicio = "wsfe",
            token = "test_token",
            sign = "test_sign",
            fechaExpiracion = LocalDateTime.now().plusHours(12)
        )
        
        assertTrue(credentials.isValid())
        assertFalse(credentials.isExpired())
        assertFalse(credentials.isNearExpiry())
        assertEquals(EstadoCredencial.ACTIVO, credentials.estado)
        assertEquals("wsfe", credentials.servicio)
    }
    
    @Test
    fun `test WsaaCredentials expiry logic`() {
        // Test con fechas fijas para evitar problemas de timing
        val baseTime = LocalDateTime.of(2024, 1, 1, 12, 0, 0)

        val expiredCredentials = WsaaCredentials.create(
            servicio = "wsfe",
            token = "test_token",
            sign = "test_sign",
            fechaGeneracion = baseTime.minusHours(2),
            fechaExpiracion = baseTime.minusHours(1)
        )

        // Como usamos fechas del pasado, estas credenciales están expiradas
        assertTrue(expiredCredentials.isExpired())

        val nearExpiryCredentials = WsaaCredentials.create(
            servicio = "wsfe",
            token = "test_token",
            sign = "test_sign",
            fechaGeneracion = baseTime,
            fechaExpiracion = baseTime.plusMinutes(30)
        )

        // Estas credenciales están próximas a vencer
        assertTrue(nearExpiryCredentials.isNearExpiry())
    }
    
    @Test
    fun `test WsaaCredentials to AfipCredentials conversion`() {
        val wsaaCredentials = WsaaCredentials.create(
            servicio = "wsfe",
            token = "test_token",
            sign = "test_sign",
            fechaExpiracion = LocalDateTime.now().plusHours(12)
        )
        
        val afipCredentials = wsaaCredentials.toAfipCredentials(20349249902L)
        
        assertEquals("test_token", afipCredentials.token)
        assertEquals("test_sign", afipCredentials.sign)
        assertEquals(20349249902L, afipCredentials.cuit)
        assertTrue(afipCredentials.isValid())
    }
    
    @Test
    fun `test get valid credentials when none exist`() {
        // Arrange
        whenever(credentialsRepository.findActiveCredentials("wsfe")).thenReturn(null)
        whenever(wsaaClient.login("wsfe")).thenReturn(
            AfipCredentials("new_token", "new_sign", 20349249902L)
        )
        whenever(credentialsRepository.save(any())).thenReturn(com.gnico.majo.application.domain.model.Id(1))
        
        // Act
        val result = wsaaCredentialsService.getValidCredentials("wsfe")
        
        // Assert
        assertNotNull(result)
        assertEquals("new_token", result!!.token)
        assertEquals("new_sign", result.sign)
        
        verify(credentialsRepository).findActiveCredentials("wsfe")
        verify(wsaaClient).login("wsfe")
        verify(credentialsRepository).save(any())
    }
    
    @Test
    fun `test get valid credentials when expired exist`() {
        // Arrange
        val baseTime = LocalDateTime.of(2024, 1, 1, 12, 0, 0)
        val expiredCredentials = WsaaCredentials.create(
            servicio = "wsfe",
            token = "old_token",
            sign = "old_sign",
            fechaGeneracion = baseTime.minusHours(2),
            fechaExpiracion = baseTime.minusHours(1)
        )

        whenever(credentialsRepository.findActiveCredentials("wsfe")).thenReturn(expiredCredentials)
        whenever(wsaaClient.login("wsfe")).thenReturn(
            AfipCredentials("new_token", "new_sign", 20349249902L)
        )
        whenever(credentialsRepository.update(any())).thenReturn(true)
        whenever(credentialsRepository.save(any())).thenReturn(com.gnico.majo.application.domain.model.Id(2))

        // Act
        val result = wsaaCredentialsService.getValidCredentials("wsfe")

        // Assert
        assertNotNull(result)
        assertEquals("new_token", result!!.token)

        verify(credentialsRepository).update(any()) // Marca como expiradas
        verify(credentialsRepository).save(any()) // Guarda nuevas
    }
    
    @Test
    fun `test get valid credentials when near expiry`() {
        // Arrange
        val nearExpiryCredentials = WsaaCredentials.create(
            servicio = "wsfe",
            token = "old_token",
            sign = "old_sign",
            fechaExpiracion = LocalDateTime.now().plusMinutes(30)
        )
        
        whenever(credentialsRepository.findActiveCredentials("wsfe")).thenReturn(nearExpiryCredentials)
        whenever(wsaaClient.login("wsfe")).thenReturn(
            AfipCredentials("new_token", "new_sign", 20349249902L)
        )
        whenever(credentialsRepository.save(any())).thenReturn(com.gnico.majo.application.domain.model.Id(3))
        
        // Act
        val result = wsaaCredentialsService.getValidCredentials("wsfe")
        
        // Assert
        assertNotNull(result)
        assertEquals("new_token", result!!.token)
        
        verify(wsaaClient).login("wsfe") // Renueva credenciales
        verify(credentialsRepository).save(any())
    }
    
    @Test
    fun `test get valid credentials when valid exist`() {
        // Arrange
        val validCredentials = WsaaCredentials.create(
            servicio = "wsfe",
            token = "valid_token",
            sign = "valid_sign",
            fechaExpiracion = LocalDateTime.now().plusHours(6)
        )
        
        whenever(credentialsRepository.findActiveCredentials("wsfe")).thenReturn(validCredentials)
        
        // Act
        val result = wsaaCredentialsService.getValidCredentials("wsfe")
        
        // Assert
        assertNotNull(result)
        assertEquals("valid_token", result!!.token)
        assertEquals("valid_sign", result.sign)
        
        verify(credentialsRepository).findActiveCredentials("wsfe")
        verify(wsaaClient, never()).login(any()) // No debe solicitar nuevas
        verify(credentialsRepository, never()).save(any())
    }
    
    @Test
    fun `test has valid credentials`() {
        // Arrange
        val validCredentials = WsaaCredentials.create(
            servicio = "wsfe",
            token = "valid_token",
            sign = "valid_sign",
            fechaExpiracion = LocalDateTime.now().plusHours(6)
        )
        
        whenever(credentialsRepository.findActiveCredentials("wsfe")).thenReturn(validCredentials)
        
        // Act
        val hasValid = wsaaCredentialsService.hasValidCredentials("wsfe")
        
        // Assert
        assertTrue(hasValid)
        verify(credentialsRepository).findActiveCredentials("wsfe")
    }
    
    @Test
    fun `test cleanup expired credentials`() {
        // Arrange
        val baseTime = LocalDateTime.of(2024, 1, 1, 12, 0, 0)
        val expiredCredentials = listOf(
            WsaaCredentials.create(
                servicio = "wsfe",
                token = "expired1",
                sign = "sign1",
                fechaGeneracion = baseTime.minusHours(3),
                fechaExpiracion = baseTime.minusHours(2)
            )
        )

        whenever(credentialsRepository.findExpired()).thenReturn(expiredCredentials)
        whenever(credentialsRepository.update(any())).thenReturn(true)
        whenever(credentialsRepository.deleteExpiredOlderThan(7)).thenReturn(5)

        // Act
        val deleted = wsaaCredentialsService.cleanupExpiredCredentials(7)

        // Assert
        assertEquals(5, deleted)
        verify(credentialsRepository).findExpired()
        verify(credentialsRepository).update(any())
        verify(credentialsRepository).deleteExpiredOlderThan(7)
    }
    
    @Test
    fun `test EstadoCredencial enum`() {
        assertEquals("ACTIVO", EstadoCredencial.ACTIVO.value)
        assertEquals("EXPIRADO", EstadoCredencial.EXPIRADO.value)
        
        assertEquals(EstadoCredencial.ACTIVO, EstadoCredencial.fromString("ACTIVO"))
        assertEquals(EstadoCredencial.EXPIRADO, EstadoCredencial.fromString("expirado"))
        
        assertThrows(IllegalArgumentException::class.java) {
            EstadoCredencial.fromString("INVALID")
        }
    }
}
