package com.gnico.majo.infrastructure.printer

import com.gnico.majo.application.domain.model.*
import com.gnico.majo.infrastructure.config.PrinterConfiguration
import com.github.anastaciocintra.escpos.EscPos
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.io.ByteArrayOutputStream
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.LocalDate

/**
 * Test específico para verificar el formato de tickets con descuentos
 * Sin usar impresora física - solo simulación
 */
class DiscountTicketFormatterTest {

    private lateinit var config: PrinterConfiguration
    private lateinit var formatter: StyledTicketFormatter

    @BeforeEach
    fun setUp() {
        config = PrinterConfiguration(
            companyName = "Test Company",
            companyCUIT = "20*********",
            companyIIBB = "*********",
            companyAddress = "Test Address 123",
            companyStart = "01/01/2020",
            companyPhone = "************",
            companyEmail = "<EMAIL>",
            companyWebsite = "www.test.com",
            printerIp = "*************",
            printerPort = 9100
        )
        
        formatter = StyledTicketFormatter(config)
    }

    @Test
    fun `should format ticket with discount correctly`() {
        println("=== TEST: Ticket con descuento ===")
        
        // Given - Venta con descuento del 10%
        val saleWithDiscount = createSaleWithDiscount()
        val comprobante = createTestComprobante()
        
        // When - Generar ticket fiscal con descuento
        val outputStream = ByteArrayOutputStream()
        val escpos = EscPos(outputStream)
        formatter.formatFiscalTicket(escpos, comprobante, saleWithDiscount)
        
        // Then - Verificar contenido
        val ticketContent = convertToReadableContent(outputStream.toByteArray())
        
        println("Contenido del ticket:")
        println(ticketContent)
        println()
        
        // Verificaciones
        assert(ticketContent.contains("Subtotal:")) { "Debe mostrar subtotal" }
        assert(ticketContent.contains("Descuento (10%):")) { "Debe mostrar descuento con porcentaje" }
        assert(ticketContent.contains("-$25.00")) { "Debe mostrar monto del descuento" }
        assert(ticketContent.contains("$250.00")) { "Debe mostrar subtotal sin descuento" }
        assert(ticketContent.contains("$225.00")) { "Debe mostrar total final" }
        
        println("✅ Ticket con descuento formateado correctamente")
    }

    @Test
    fun `should format ticket without discount correctly`() {
        println("=== TEST: Ticket sin descuento ===")
        
        // Given - Venta sin descuento
        val saleWithoutDiscount = createSaleWithoutDiscount()
        val comprobante = createTestComprobante()
        
        // When - Generar ticket fiscal sin descuento
        val outputStream = ByteArrayOutputStream()
        val escpos = EscPos(outputStream)
        formatter.formatFiscalTicket(escpos, comprobante, saleWithoutDiscount)
        
        // Then - Verificar contenido
        val ticketContent = convertToReadableContent(outputStream.toByteArray())
        
        println("Contenido del ticket:")
        println(ticketContent)
        println()
        
        // Verificaciones - NO debe mostrar líneas de descuento
        assert(!ticketContent.contains("Subtotal:")) { "No debe mostrar subtotal cuando no hay descuento" }
        assert(!ticketContent.contains("Descuento")) { "No debe mostrar línea de descuento" }
        assert(ticketContent.contains("$200.00")) { "Debe mostrar total directo" }
        
        println("✅ Ticket sin descuento formateado correctamente")
    }

    @Test
    fun `should format non-fiscal ticket with discount correctly`() {
        println("=== TEST: Ticket no fiscal con descuento ===")
        
        // Given - Venta con descuento del 15%
        val saleWithDiscount = createSaleWithLargeDiscount()
        
        // When - Generar ticket no fiscal con descuento
        val outputStream = ByteArrayOutputStream()
        val escpos = EscPos(outputStream)
        formatter.formatNonFiscalTicket(escpos, saleWithDiscount)
        
        // Then - Verificar contenido
        val ticketContent = convertToReadableContent(outputStream.toByteArray())
        
        println("Contenido del ticket:")
        println(ticketContent)
        println()
        
        // Verificaciones
        assert(ticketContent.contains("Subtotal:")) { "Debe mostrar subtotal" }
        assert(ticketContent.contains("Descuento (15%):")) { "Debe mostrar descuento con porcentaje" }
        assert(ticketContent.contains("-$45.00")) { "Debe mostrar monto del descuento" }
        assert(ticketContent.contains("$255.00")) { "Debe mostrar total final" }
        
        println("✅ Ticket no fiscal con descuento formateado correctamente")
    }

    @Test
    fun `should test discount calculation methods`() {
        println("=== TEST: Métodos de cálculo de descuento ===")
        
        val saleWithDiscount = createSaleWithDiscount()
        
        // Verificar métodos de cálculo
        val subtotalSinDescuento = saleWithDiscount.calculateSubtotalSinDescuento()
        val montoDescuento = saleWithDiscount.calculateMontoDescuento()
        val hasDescuento = saleWithDiscount.hasDescuento()
        
        println("Subtotal sin descuento: $subtotalSinDescuento")
        println("Monto descuento: $montoDescuento")
        println("Tiene descuento: $hasDescuento")
        println("Monto total: ${saleWithDiscount.montoTotal}")
        println()
        
        // Verificaciones
        assert(subtotalSinDescuento == BigDecimal("250.00")) { "Subtotal sin descuento incorrecto" }
        assert(montoDescuento == BigDecimal("25.00")) { "Monto descuento incorrecto" }
        assert(hasDescuento) { "Debe detectar que tiene descuento" }
        assert(saleWithDiscount.montoTotal == BigDecimal("225.00")) { "Total incorrecto" }
        
        println("✅ Métodos de cálculo funcionan correctamente")
    }

    private fun createSaleWithDiscount(): Sale {
        val usuario = Usuario("test_user", "Test User", "Test User", true)
        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto A",
                cantidad = BigDecimal("2"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21,
                porcentajeDescuento = BigDecimal("10") // 10% descuento
            ),
            SaleItem.create(
                productoNombre = "Producto B",
                cantidad = BigDecimal("1"),
                precioUnitario = BigDecimal("50.00"),
                tipoIva = TipoIva.IVA_21,
                porcentajeDescuento = BigDecimal("10") // 10% descuento
            )
        )
        
        return Sale.create(
            usuario = usuario,
            items = items,
            medioPago = "EFECTIVO",
            porcentajeDescuento = BigDecimal("10")
        )
    }

    private fun createSaleWithoutDiscount(): Sale {
        val usuario = Usuario("test_user", "Test User", "Test User", true)
        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto A",
                cantidad = BigDecimal("2"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21
            )
        )
        
        return Sale.create(
            usuario = usuario,
            items = items,
            medioPago = "EFECTIVO"
        )
    }

    private fun createSaleWithLargeDiscount(): Sale {
        val usuario = Usuario("test_user", "Test User", "Test User", true)
        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto Premium",
                cantidad = BigDecimal("1"),
                precioUnitario = BigDecimal("300.00"),
                tipoIva = TipoIva.IVA_21,
                porcentajeDescuento = BigDecimal("15") // 15% descuento
            )
        )
        
        return Sale.create(
            usuario = usuario,
            items = items,
            medioPago = "TARJETA_CREDITO",
            porcentajeDescuento = BigDecimal("15")
        )
    }

    private fun createTestComprobante(): Comprobante {
        return Comprobante.fromPersistence(
            id = Id(1),
            venta = Id(1),
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1,
            numeroComprobante = 123,
            cae = "74*********012",
            fechaEmision = LocalDateTime.now(),
            fechaVencimientoCae = LocalDate.now().plusDays(10),
            impTotal = BigDecimal("225.00"),
            impTotConc = BigDecimal.ZERO,
            impNeto = BigDecimal("185.95"),
            impIva = BigDecimal("39.05"),
            impTrib = BigDecimal.ZERO,
            monId = "PES",
            monCotiz = BigDecimal.ONE,
            estado = "EMITIDO"
        )
    }

    private fun convertToReadableContent(rawOutput: ByteArray): String {
        // Convertir bytes ESC/POS a texto legible para verificación
        val content = String(rawOutput, Charsets.ISO_8859_1)
        
        // Filtrar caracteres de control ESC/POS y mantener solo texto legible
        return content.replace(Regex("[\u0000-\u001F\u007F-\u009F]"), "")
            .replace(Regex("\\s+"), " ")
            .trim()
    }
}
