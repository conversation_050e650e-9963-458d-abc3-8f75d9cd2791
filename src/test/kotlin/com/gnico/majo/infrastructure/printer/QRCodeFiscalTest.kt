package com.gnico.majo.infrastructure.printer

import com.gnico.majo.application.domain.model.*
import com.gnico.majo.infrastructure.config.PrinterConfiguration
import com.github.anastaciocintra.escpos.EscPos
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.io.ByteArrayOutputStream
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.Base64

class QRCodeFiscalTest {

    private lateinit var config: PrinterConfiguration
    private lateinit var formatter: StyledTicketFormatter
    private lateinit var comprobante: Comprobante
    private lateinit var sale: Sale

    @BeforeEach
    fun setUp() {
        config = PrinterConfiguration(
            companyName = "Test Company S.A.",
            companyCUIT = "20*********",
            companyIIBB = "*********",
            companyAddress = "Test Address 123",
            companyStart = "01/01/2020",
            companyPhone = "************",
            companyEmail = "<EMAIL>",
            companyWebsite = "www.test.com",
            printerIp = "*************",
            printerPort = 9100
        )
        
        formatter = StyledTicketFormatter(config)
        
        // Crear comprobante de prueba
        comprobante = Comprobante.fromPersistence(
            id = Id(1),
            venta = Id(1),
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1,
            numeroComprobante = 123,
            cae = "*********01234",
            fechaEmision = LocalDateTime.now(),
            fechaVencimientoCae = LocalDate.now().plusDays(10),
            impTotal = BigDecimal("200.00"),
            impTotConc = BigDecimal.ZERO,
            impNeto = BigDecimal("165.29"),
            impIva = BigDecimal("34.71"),
            impTrib = BigDecimal.ZERO,
            monId = "PES",
            monCotiz = BigDecimal.ONE,
            estado = "APROBADO"
        )
        
        // Crear venta de prueba
        val usuario = Usuario.create("testuser", "Test User", "Test User")
        // Ya no manejamos clientes - solo consumidor final
        
        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto Test",
                cantidad = BigDecimal("2.0"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21
            )
        )

        sale = Sale.create(
            usuario = usuario,
            items = items,
            medioPago = "EFECTIVO"
        )
    }
    
    @Test
    fun `should generate QR code data in correct JSON format`() {
        val outputStream = ByteArrayOutputStream()
        val escpos = EscPos(outputStream)

        // Generar el ticket fiscal con QR
        formatter.formatFiscalTicket(escpos, comprobante, sale)

        // Verificar que se generó contenido
        val output = outputStream.toByteArray()
        assert(output.isNotEmpty()) { "El ticket debe generar contenido" }

        println("✅ Ticket fiscal con QR generado exitosamente")
        println("Tamaño del output: ${output.size} bytes")
    }
    
    @Test
    fun `should generate valid JSON QR data according to AFIP specifications`() {
        // Acceder al método privado usando reflection para testing
        val method = StyledTicketFormatter::class.java.getDeclaredMethod(
            "generateFiscalQRData",
            Comprobante::class.java
        )
        method.isAccessible = true

        val qrData = method.invoke(formatter, comprobante) as String

        // Verificar que es una URL válida
        assert(qrData.startsWith("https://www.arca.gob.ar/fe/qr/?p=")) {
            "QR debe ser una URL de AFIP válida"
        }

        // Extraer y decodificar la parte Base64
        val base64Part = qrData.substringAfter("?p=")

        try {
            val decoded = Base64.getDecoder().decode(base64Part)
            assert(decoded.isNotEmpty()) { "Los datos decodificados no deben estar vacíos" }

            val jsonString = String(decoded)
            println("QR URL: $qrData")
            println("JSON decodificado: $jsonString")

            // Solo verificar que es JSON válido básico
            assert(jsonString.contains("\"ver\":1")) { "Debe contener versión 1" }
            assert(jsonString.contains("\"fecha\":")) { "Debe contener fecha" }

            println("✅ QR Data JSON generado correctamente según especificaciones AFIP")

        } catch (e: Exception) {
            throw AssertionError("Error procesando QR data: ${e.message}")
        }
    }
    
    @Test
    fun `should handle QR generation errors gracefully`() {
        // Crear un comprobante con datos inválidos para forzar error
        val comprobanteInvalido = Comprobante.fromPersistence(
            id = Id(1),
            venta = Id(1),
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1,
            numeroComprobante = 123,
            cae = "", // CAE vacío para forzar error
            fechaEmision = LocalDateTime.now(),
            fechaVencimientoCae = LocalDate.now().plusDays(10),
            impTotal = BigDecimal("200.00"),
            impTotConc = BigDecimal.ZERO,
            impNeto = BigDecimal("165.29"),
            impIva = BigDecimal("34.71"),
            impTrib = BigDecimal.ZERO,
            monId = "PES",
            monCotiz = BigDecimal.ONE,
            estado = "APROBADO"
        )
        
        val outputStream = ByteArrayOutputStream()
        val escpos = EscPos(outputStream)
        
        // Debe continuar sin error aunque el QR falle
        formatter.formatFiscalTicket(escpos, comprobanteInvalido, sale)
        
        val output = outputStream.toByteArray()
        assert(output.isNotEmpty()) { "El ticket debe generarse aunque falle el QR" }
        
        println("✅ Manejo de errores en QR funciona correctamente")
    }
}
