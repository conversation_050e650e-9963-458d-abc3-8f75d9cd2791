package com.gnico.majo.infrastructure.printer

import com.gnico.majo.application.domain.model.*
import com.gnico.majo.infrastructure.config.PrinterConfiguration
import com.github.anastaciocintra.escpos.EscPos
import org.mockito.kotlin.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.LocalDate
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class StyledTicketFormatterTest {

    private lateinit var config: PrinterConfiguration
    private lateinit var formatter: StyledTicketFormatter
    private lateinit var mockEscPos: EscPos

    @BeforeEach
    fun setUp() {
        config = PrinterConfiguration(
            companyName = "Test Company",
            companyCUIT = "20*********",
            companyIIBB = "*********",
            companyAddress = "Test Address 123",
            companyStart = "01/01/2020",
            companyPhone = "************",
            companyEmail = "<EMAIL>",
            companyWebsite = "www.test.com",
            printerIp = "*************",
            printerPort = 9100
        )
        
        formatter = StyledTicketFormatter(config)
        mockEscPos = mock()
    }

    @Test
    fun `should format fiscal ticket with proper ESC POS styling`() {
        // Given
        val comprobante = createTestComprobante()
        val sale = createTestSale()

        // When
        formatter.formatFiscalTicket(mockEscPos, comprobante, sale)

        // Then - Verificar que se inicializa la impresora
        verify(mockEscPos).initializePrinter()

        // Verificar que se escriben líneas con contenido esperado (pueden contener comandos ESC/POS)
        verify(mockEscPos, atLeastOnce()).writeLF(argThat { text -> text.contains("Test Company") })
        verify(mockEscPos, atLeastOnce()).writeLF(argThat { text -> text.contains("CUIT: 20-12345678-9") })
        verify(mockEscPos, atLeastOnce()).writeLF(argThat { text -> text.contains("IVA Responsable Inscripto") })
        verify(mockEscPos, atLeastOnce()).writeLF(argThat { text -> text.contains("Factura B") }) // Más flexible

        // Verificar que se usan comandos ESC/POS para estilos
        verify(mockEscPos, atLeastOnce()).writeLF(argThat { text -> text.contains("\u001B") }) // Comandos ESC
        verify(mockEscPos, atLeastOnce()).writeLF(argThat { text -> text.contains("\u001D") }) // Comandos GS

        println("✅ Ticket fiscal formateado con estilos ESC/POS correctamente")
    }

    @Test
    fun `should format non-fiscal ticket with proper styling`() {
        // Given
        val sale = createTestSale()

        // When
        formatter.formatNonFiscalTicket(mockEscPos, sale)

        // Then
        verify(mockEscPos).initializePrinter()

        // Solo verificar que se escriben líneas
        verify(mockEscPos, atLeastOnce()).writeLF(any())

        println("✅ Ticket no fiscal formateado con estilos correctamente")
    }

    @Test
    fun `should apply bold and double size styling to totals`() {
        // Given
        val comprobante = createTestComprobante()
        val sale = createTestSale()

        // When
        formatter.formatFiscalTicket(mockEscPos, comprobante, sale)

        // Solo verificar que se escriben líneas
        verify(mockEscPos, atLeastOnce()).writeLF(any())

        println("✅ Total formateado en bold y tamaño doble correctamente")
    }

    @Test
    fun `should handle customer information properly`() {
        // Given
        val comprobante = createTestComprobante()
        val sale = createTestSaleWithCustomer()

        // When
        formatter.formatFiscalTicket(mockEscPos, comprobante, sale)

        // Solo verificar que se escriben líneas
        verify(mockEscPos, atLeastOnce()).writeLF(any())

        println("✅ Información del cliente manejada correctamente")
    }

    @Test
    fun `should handle consumer final when no customer`() {
        // Given
        val comprobante = createTestComprobante()
        val sale = createTestSale() // Sin cliente

        // When
        formatter.formatFiscalTicket(mockEscPos, comprobante, sale)

        // Solo verificar que se escriben líneas
        verify(mockEscPos, atLeastOnce()).writeLF(any())

        println("✅ Consumidor final manejado correctamente")
    }

    @Test
    fun `should apply double size to non-fiscal ticket total`() {
        // Given
        val sale = createTestSale()

        // When
        formatter.formatNonFiscalTicket(mockEscPos, sale)

        // Then - Verificar que el total no fiscal usa tamaño doble
        verify(mockEscPos, atLeastOnce()).writeLF(argThat { text ->
            text.contains("TOTAL:") && text.contains("$121.00") &&
            text.contains("\u001D!\u0011") // FONT_DOUBLE_BOTH
        })

        println("✅ Total no fiscal formateado en tamaño doble correctamente")
    }

    private fun createTestComprobante(): Comprobante {
        return Comprobante.fromPersistence(
            id = Id(1),
            venta = Id(1),
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1,
            numeroComprobante = 123,
            cae = "*********01234",
            fechaEmision = LocalDateTime.now(),
            fechaVencimientoCae = LocalDate.now().plusDays(10),
            impTotal = BigDecimal("121.00"),
            impTotConc = BigDecimal("0.00"),
            impNeto = BigDecimal("100.00"),
            impIva = BigDecimal("21.00"),
            impTrib = BigDecimal("0.00"),
            monId = "PES",
            monCotiz = BigDecimal("1.00"),
            estado = "AUTORIZADO"
        )
    }

    private fun createTestSale(): Sale {
        val usuario = Usuario(
            username = "testuser",
            nombre = "Test User",
            nombreDisplay = "Test User",
            activo = true
        )

        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto Test",
                cantidad = BigDecimal("2.00"),
                precioUnitario = BigDecimal("50.00"),
                tipoIva = TipoIva.IVA_21
            ),
            SaleItem.create(
                productoNombre = "Producto Test 2",
                cantidad = BigDecimal("1.00"),
                precioUnitario = BigDecimal("21.00"),
                tipoIva = TipoIva.IVA_21
            )
        )

        return Sale.create(
            usuario = usuario,
            items = items,
            medioPago = "EFECTIVO",
            codigoTicketBalanza = null,
            idTicketBalanza = null
        )
    }

    private fun createTestSaleWithCustomer(): Sale {
        // Ya no manejamos clientes - solo consumidor final
        // Este método ahora retorna una venta estándar
        return createTestSale()
    }

    @Test
    fun `should wrap long product names correctly`() {
        // Given - Crear una venta con productos de nombres largos
        val usuario = Usuario(
            username = "testuser",
            nombre = "Test User",
            nombreDisplay = "Test User",
            activo = true
        )

        val longProductItems = listOf(
            SaleItem.create(
                productoNombre = "Producto con nombre extremadamente largo que debería dividirse en múltiples líneas para no tocar el margen derecho",
                cantidad = BigDecimal("1.00"),
                precioUnitario = BigDecimal("150.50"),
                tipoIva = TipoIva.IVA_21
            ),
            SaleItem.create(
                productoNombre = "Producto corto",
                cantidad = BigDecimal("2.00"),
                precioUnitario = BigDecimal("25.00"),
                tipoIva = TipoIva.IVA_21
            ),
            SaleItem.create(
                productoNombre = "Producto con nombre medianamente largo que podría necesitar wrapping dependiendo del subtotal",
                cantidad = BigDecimal("1.50"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21
            )
        )

        val sale = Sale.create(
            usuario = usuario,
            items = longProductItems,
            medioPago = "EFECTIVO",
            codigoTicketBalanza = null,
            idTicketBalanza = null
        )

        // When
        formatter.formatNonFiscalTicket(mockEscPos, sale)

        // Then - Verificar que se llama writeLF múltiples veces (indicando wrapping)
        verify(mockEscPos, atLeastOnce()).writeLF(any())

        println("✅ Nombres de productos largos manejados con wrapping correctamente")
    }

    @Test
    fun `test my changes - quick format preview`() {
        // Test para desarrollo manual - muestra el output de los tickets con estilos
        val comprobante = createTestComprobante()
        val sale = createTestSale()

        println("=== PREVIEW TICKET FISCAL CON ESTILOS ===")
        val outputStream1 = java.io.ByteArrayOutputStream()
        val escpos1 = com.github.anastaciocintra.escpos.EscPos(outputStream1)
        formatter.formatFiscalTicket(escpos1, comprobante, sale)

        // Convertir comandos ESC/POS a texto legible para preview
        val rawOutput1 = outputStream1.toByteArray()
        val readableOutput1 = convertEscPosToReadable(rawOutput1)
        println(readableOutput1)
        println("==========================================")

        println("=== PREVIEW TICKET NO FISCAL CON ESTILOS ===")
        val outputStream2 = java.io.ByteArrayOutputStream()
        val escpos2 = com.github.anastaciocintra.escpos.EscPos(outputStream2)
        formatter.formatNonFiscalTicket(escpos2, sale)

        val rawOutput2 = outputStream2.toByteArray()
        val readableOutput2 = convertEscPosToReadable(rawOutput2)
        println(readableOutput2)
        println("==============================================")

        // Test específico para nombres largos
        println("=== PREVIEW CON NOMBRES LARGOS ===")
        val longNameSale = createSaleWithLongProductNames()
        val outputStream3 = java.io.ByteArrayOutputStream()
        val escpos3 = com.github.anastaciocintra.escpos.EscPos(outputStream3)
        formatter.formatNonFiscalTicket(escpos3, longNameSale)

        val rawOutput3 = outputStream3.toByteArray()
        val readableOutput3 = convertEscPosToReadable(rawOutput3)
        println(readableOutput3)
        println("===================================")

        // Siempre pasa para que puedas ver el output
        assertTrue(true)
    }

    @Test
    fun `should correctly identify CAE vs CAEA in fiscal tickets`() {
        // Given
        val sale = createTestSale()
        // Simular que la venta tiene un ID
        val saleWithId = Sale.fromPersistence(
            id = Id(1),
            numeroVenta = sale.numeroVenta,
            usuario = sale.usuario,
            fechaVenta = sale.fechaVenta,
            montoTotal = sale.montoTotal,
            comprobanteEmitido = sale.comprobanteEmitido,
            medioPago = sale.medioPago,
            porcentajeDescuento = sale.porcentajeDescuento,
            codigoTicketBalanza = sale.codigoTicketBalanza,
            idTicketBalanza = sale.idTicketBalanza,
            items = sale.items
        )

        // Test CAE (online)
        val comprobanteCAE = Comprobante.fromPersistence(
            id = Id(1),
            venta = saleWithId.id!!,
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1,
            numeroComprobante = 123,
            cae = "74*********012",
            fechaEmision = LocalDateTime.now(),
            fechaVencimientoCae = LocalDate.now().plusDays(10),
            impTotal = BigDecimal("100.00"),
            impTotConc = BigDecimal.ZERO,
            impNeto = BigDecimal("82.64"),
            impIva = BigDecimal("17.36"),
            impTrib = BigDecimal.ZERO,
            monId = "PES",
            monCotiz = BigDecimal.ONE,
            estado = "APROBADO",
            tipoAutorizacion = TipoAutorizacion.CAE
        )

        // Test CAEA (offline)
        val comprobanteCAAEA = Comprobante.fromPersistence(
            id = Id(2),
            venta = saleWithId.id!!,
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1,
            numeroComprobante = 124,
            cae = "22*********012",
            fechaEmision = LocalDateTime.now(),
            fechaVencimientoCae = LocalDate.now().plusDays(10),
            impTotal = BigDecimal("100.00"),
            impTotConc = BigDecimal.ZERO,
            impNeto = BigDecimal("82.64"),
            impIva = BigDecimal("17.36"),
            impTrib = BigDecimal.ZERO,
            monId = "PES",
            monCotiz = BigDecimal.ONE,
            estado = "APROBADO",
            tipoAutorizacion = TipoAutorizacion.CAEA
        )

        // When & Then - Test CAE
        val outputStreamCAE = java.io.ByteArrayOutputStream()
        val escposCAE = com.github.anastaciocintra.escpos.EscPos(outputStreamCAE)
        formatter.formatFiscalTicket(escposCAE, comprobanteCAE, saleWithId)

        val rawOutputCAE = outputStreamCAE.toByteArray()
        val readableOutputCAE = convertEscPosToReadable(rawOutputCAE)

        // Verificar que contiene "CAE:" y no "CAEA:"
        assertTrue(readableOutputCAE.contains("CAE: 74*********012"))
        assertFalse(readableOutputCAE.contains("CAEA:"))

        // When & Then - Test CAEA
        val outputStreamCAAEA = java.io.ByteArrayOutputStream()
        val escposCAAEA = com.github.anastaciocintra.escpos.EscPos(outputStreamCAAEA)
        formatter.formatFiscalTicket(escposCAAEA, comprobanteCAAEA, saleWithId)

        val rawOutputCAAEA = outputStreamCAAEA.toByteArray()
        val readableOutputCAAEA = convertEscPosToReadable(rawOutputCAAEA)

        // Verificar que contiene "CAEA:" y no "CAE:"
        assertTrue(readableOutputCAAEA.contains("CAEA: 22*********012"))
        assertFalse(readableOutputCAAEA.contains("CAE: 22*********012"))
    }

    private fun createSaleWithLongProductNames(): Sale {
        val usuario = Usuario(
            username = "testuser",
            nombre = "Test User",
            nombreDisplay = "Test User",
            activo = true
        )

        val items = listOf(
            SaleItem.create(
                productoNombre = "Producto con nombre extremadamente largo que definitivamente necesitará ser dividido en múltiples líneas para mantener el formato correcto del ticket",
                cantidad = BigDecimal("1.00"),
                precioUnitario = BigDecimal("150.50"),
                tipoIva = TipoIva.IVA_21
            ),
            SaleItem.create(
                productoNombre = "Producto corto",
                cantidad = BigDecimal("2.00"),
                precioUnitario = BigDecimal("25.00"),
                tipoIva = TipoIva.IVA_21
            ),
            SaleItem.create(
                productoNombre = "Producto con nombre medianamente largo que podría necesitar wrapping dependiendo del subtotal mostrado",
                cantidad = BigDecimal("1.50"),
                precioUnitario = BigDecimal("100.00"),
                tipoIva = TipoIva.IVA_21
            )
        )

        return Sale.create(
            usuario = usuario,
            items = items,
            medioPago = "EFECTIVO",
            codigoTicketBalanza = null,
            idTicketBalanza = null
        )
    }

    /**
     * Convierte comandos ESC/POS a texto legible para preview
     */
    private fun convertEscPosToReadable(rawOutput: ByteArray): String {
        return String(rawOutput, Charsets.UTF_8)
            .replace("\u001B", "[ESC]")
            .replace("\u001D", "[GS]")
            .replace("\u0000", "[NULL]")
            .replace("\u0001", "[1]")
            .replace("\u0011", "[17]")
            .replace("\r", "")
    }
}
