package com.gnico.majo.infrastructure.printer

import com.gnico.majo.application.domain.model.*
import com.gnico.majo.infrastructure.config.PrinterConfiguration
import com.github.anastaciocintra.escpos.EscPos
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.io.ByteArrayOutputStream
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.LocalDate

/**
 * Test para mostrar por consola el formato de tickets con descuento
 * Sin usar impresora física - solo simulación visual
 */
class DiscountTicketConsoleTest {

    private lateinit var config: PrinterConfiguration
    private lateinit var formatter: StyledTicketFormatter

    @BeforeEach
    fun setUp() {
        config = PrinterConfiguration(
            companyName = "MAJO DISTRIBUIDORA",
            companyCUIT = "20349249902",
            companyIIBB = "*********",
            companyAddress = "Av. Principal 1234, Ciudad",
            companyStart = "15/03/2020",
            companyPhone = "011-4567-8900",
            companyEmail = "<EMAIL>",
            companyWebsite = "www.majodistribuidora.com",
            printerIp = "*************",
            printerPort = 9100
        )
        
        formatter = StyledTicketFormatter(config)
    }

    @Test
    fun `mostrar ticket fiscal con descuento 15 por ciento`() {
        println("\n" + "=".repeat(60))
        println("🎫 DEMOSTRACIÓN: TICKET FISCAL CON DESCUENTO 15%")
        println("=".repeat(60))
        
        // Crear venta con descuento del 15%
        val saleWithDiscount = createSaleWithDiscount15()
        val comprobante = createTestComprobante()
        
        // Mostrar datos de la venta
        println("\n📊 DATOS DE LA VENTA:")
        println("- Subtotal sin descuento: $${saleWithDiscount.calculateSubtotalSinDescuento()}")
        println("- Descuento aplicado (15%): -$${saleWithDiscount.calculateMontoDescuento()}")
        println("- Total final: $${saleWithDiscount.montoTotal}")
        println("- Tiene descuento: ${saleWithDiscount.hasDescuento()}")
        
        // Generar y mostrar ticket
        println("\n🎫 CONTENIDO DEL TICKET FISCAL:")
        println("-".repeat(50))
        val ticketContent = simulateAndShowTicket { escpos ->
            formatter.formatFiscalTicket(escpos, comprobante, saleWithDiscount)
        }
        println("-".repeat(50))
        
        // Verificaciones
        println("\n✅ VERIFICACIONES:")
        println("- Contiene 'Subtotal': ${ticketContent.contains("Subtotal")}")
        println("- Contiene 'Descuento (15%)': ${ticketContent.contains("Descuento (15%)")}")
        println("- Contiene monto descuento: ${ticketContent.contains("67.50")}")
        println("- Contiene total final: ${ticketContent.contains("382.50")}")
        
        println("\n✅ Test completado exitosamente!")
    }

    @Test
    fun `mostrar ticket no fiscal con descuento 20 por ciento`() {
        println("\n" + "=".repeat(60))
        println("🎫 DEMOSTRACIÓN: TICKET NO FISCAL CON DESCUENTO 20%")
        println("=".repeat(60))
        
        // Crear venta con descuento del 20%
        val saleWithDiscount = createSaleWithDiscount20()
        
        // Mostrar datos de la venta
        println("\n📊 DATOS DE LA VENTA:")
        println("- Subtotal sin descuento: $${saleWithDiscount.calculateSubtotalSinDescuento()}")
        println("- Descuento aplicado (20%): -$${saleWithDiscount.calculateMontoDescuento()}")
        println("- Total final: $${saleWithDiscount.montoTotal}")
        println("- Tiene descuento: ${saleWithDiscount.hasDescuento()}")
        
        // Generar y mostrar ticket
        println("\n🎫 CONTENIDO DEL TICKET NO FISCAL:")
        println("-".repeat(50))
        val ticketContent = simulateAndShowTicket { escpos ->
            formatter.formatNonFiscalTicket(escpos, saleWithDiscount)
        }
        println("-".repeat(50))
        
        // Verificaciones
        println("\n✅ VERIFICACIONES:")
        println("- Contiene 'Subtotal': ${ticketContent.contains("Subtotal")}")
        println("- Contiene 'Descuento (20%)': ${ticketContent.contains("Descuento (20%)")}")
        println("- Contiene monto descuento: ${ticketContent.contains("120.00")}")
        println("- Contiene total final: ${ticketContent.contains("480.00")}")
        
        println("\n✅ Test completado exitosamente!")
    }

    @Test
    fun `comparar ticket con y sin descuento`() {
        println("\n" + "=".repeat(60))
        println("🔄 COMPARACIÓN: TICKET CON VS SIN DESCUENTO")
        println("=".repeat(60))
        
        val comprobante = createTestComprobante()
        
        // Venta SIN descuento
        val saleWithoutDiscount = createSaleWithoutDiscount()
        println("\n1️⃣ TICKET SIN DESCUENTO:")
        println("-".repeat(40))
        val ticketWithoutDiscount = simulateAndShowTicket { escpos ->
            formatter.formatFiscalTicket(escpos, comprobante, saleWithoutDiscount)
        }
        
        // Venta CON descuento
        val saleWithDiscount = createSaleWithDiscount15()
        println("\n2️⃣ TICKET CON DESCUENTO 15%:")
        println("-".repeat(40))
        val ticketWithDiscount = simulateAndShowTicket { escpos ->
            formatter.formatFiscalTicket(escpos, comprobante, saleWithDiscount)
        }
        
        // Comparación
        println("\n📋 COMPARACIÓN:")
        println("- Ticket sin descuento contiene 'Subtotal': ${ticketWithoutDiscount.contains("Subtotal")}")
        println("- Ticket con descuento contiene 'Subtotal': ${ticketWithDiscount.contains("Subtotal")}")
        println("- Ticket sin descuento contiene 'Descuento': ${ticketWithoutDiscount.contains("Descuento")}")
        println("- Ticket con descuento contiene 'Descuento': ${ticketWithDiscount.contains("Descuento")}")
        
        println("\n✅ Comparación completada!")
    }

    private fun simulateAndShowTicket(ticketGenerator: (EscPos) -> Unit): String {
        val outputStream = ByteArrayOutputStream()
        val escpos = EscPos(outputStream)
        
        try {
            ticketGenerator(escpos)
            
            val rawOutput = outputStream.toByteArray()
            val readableOutput = convertEscPosToReadable(rawOutput)
            
            // Mostrar el contenido del ticket de forma legible
            println(readableOutput)
            
            return readableOutput
            
        } catch (e: Exception) {
            println("❌ Error al generar ticket: ${e.message}")
            e.printStackTrace()
            return ""
        }
    }

    private fun convertEscPosToReadable(rawOutput: ByteArray): String {
        // Convertir bytes ESC/POS a texto legible para mostrar en consola
        val content = String(rawOutput, Charsets.ISO_8859_1)
        
        // Filtrar caracteres de control ESC/POS y mantener solo texto legible
        return content
            .replace(Regex("[\u0000-\u001F\u007F-\u009F]"), "") // Remover caracteres de control
            .replace(Regex("\\s+"), " ") // Normalizar espacios
            .trim()
            .split(" ")
            .filter { it.isNotBlank() }
            .joinToString(" ")
    }

    private fun createSaleWithDiscount15(): Sale {
        val usuario = Usuario("vendedor1", "Juan Pérez", "Juan Pérez", true)
        val items = listOf(
            SaleItem.create(
                productoNombre = "Aceite Girasol 900ml",
                cantidad = BigDecimal("3"),
                precioUnitario = BigDecimal("120.00"),
                tipoIva = TipoIva.IVA_21,
                porcentajeDescuento = BigDecimal("15") // 15% descuento
            ),
            SaleItem.create(
                productoNombre = "Arroz Largo Fino 1kg",
                cantidad = BigDecimal("2"),
                precioUnitario = BigDecimal("95.00"),
                tipoIva = TipoIva.IVA_21,
                porcentajeDescuento = BigDecimal("15") // 15% descuento
            )
        )
        
        return Sale.create(
            usuario = usuario,
            items = items,
            medioPago = "EFECTIVO",
            porcentajeDescuento = BigDecimal("15")
        )
    }

    private fun createSaleWithDiscount20(): Sale {
        val usuario = Usuario("vendedor2", "María García", "María García", true)
        val items = listOf(
            SaleItem.create(
                productoNombre = "Detergente Líquido 3L",
                cantidad = BigDecimal("2"),
                precioUnitario = BigDecimal("180.00"),
                tipoIva = TipoIva.IVA_21,
                porcentajeDescuento = BigDecimal("20") // 20% descuento
            ),
            SaleItem.create(
                productoNombre = "Papel Higiénico x12",
                cantidad = BigDecimal("1"),
                precioUnitario = BigDecimal("240.00"),
                tipoIva = TipoIva.IVA_21,
                porcentajeDescuento = BigDecimal("20") // 20% descuento
            )
        )
        
        return Sale.create(
            usuario = usuario,
            items = items,
            medioPago = "TARJETA_DEBITO",
            porcentajeDescuento = BigDecimal("20")
        )
    }

    private fun createSaleWithoutDiscount(): Sale {
        val usuario = Usuario("vendedor1", "Juan Pérez", "Juan Pérez", true)
        val items = listOf(
            SaleItem.create(
                productoNombre = "Leche Entera 1L",
                cantidad = BigDecimal("2"),
                precioUnitario = BigDecimal("150.00"),
                tipoIva = TipoIva.IVA_21
            )
        )
        
        return Sale.create(
            usuario = usuario,
            items = items,
            medioPago = "EFECTIVO"
        )
    }

    private fun createTestComprobante(): Comprobante {
        return Comprobante.fromPersistence(
            id = Id(1),
            venta = Id(1),
            tipoComprobante = "FACTURA_B",
            puntoVenta = 1,
            numeroComprobante = 456,
            cae = "74123456789012",
            fechaEmision = LocalDateTime.now(),
            fechaVencimientoCae = LocalDate.now().plusDays(10),
            impTotal = BigDecimal("382.50"),
            impTotConc = BigDecimal.ZERO,
            impNeto = BigDecimal("316.12"),
            impIva = BigDecimal("66.38"),
            impTrib = BigDecimal.ZERO,
            monId = "PES",
            monCotiz = BigDecimal.ONE,
            estado = "EMITIDO",
            caeaUtilizado = null
        )
    }
}
