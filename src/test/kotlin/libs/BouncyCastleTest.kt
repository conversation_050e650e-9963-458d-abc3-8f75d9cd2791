package libs

import org.bouncycastle.asn1.x500.X500Name
import org.bouncycastle.cert.X509v3CertificateBuilder
import org.bouncycastle.cert.jcajce.JcaX509CertificateConverter
import org.bouncycastle.cert.jcajce.JcaX509v3CertificateBuilder
import org.bouncycastle.jce.provider.BouncyCastleProvider
import org.bouncycastle.operator.jcajce.JcaContentSignerBuilder
import org.junit.jupiter.api.Test
import java.math.BigInteger
import java.security.*
import java.security.cert.X509Certificate
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

class BouncyCastleTest {

    init {
        // Register the Bouncy Castle provider if not already registered
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(BouncyCastleProvider())
        }
    }

    @Test
    fun `should generate a self-signed certificate`() {
        val keyPairGenerator = KeyPairGenerator.getInstance("RSA", "BC")
        keyPairGenerator.initialize(2048)
        val keyPair = keyPairGenerator.generateKeyPair()

        val subject = X500Name("CN=Test Certificate")
        val serialNumber = BigInteger.valueOf(System.currentTimeMillis())
        val notBefore = Date(System.currentTimeMillis() - 1000L * 60)
        val notAfter = Date(System.currentTimeMillis() + 365 * 24 * 60 * 60 * 1000L) // 1 year

        val certBuilder: X509v3CertificateBuilder = JcaX509v3CertificateBuilder(
            subject, serialNumber, notBefore, notAfter, subject, keyPair.public
        )

        val contentSigner = JcaContentSignerBuilder("SHA256WithRSAEncryption")
            .setProvider("BC")
            .build(keyPair.private)

        val certificate: X509Certificate = JcaX509CertificateConverter()
            .setProvider("BC")
            .getCertificate(certBuilder.build(contentSigner))

        // Validate the certificate
        certificate.checkValidity()
        certificate.verify(keyPair.public)

        // Assertions to confirm everything worked
        assertNotNull(certificate)
        assertEquals("CN=Test Certificate", certificate.subjectX500Principal.name)
        println("✅ Bouncy Castle is working. Certificate generated successfully.")
    }
}