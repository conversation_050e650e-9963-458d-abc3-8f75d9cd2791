# Solución: Repositorio de Numeración de Comprobantes

## Problema Original

Al generar notas de crédito, el sistema fallaba con errores de numeración:
```
ERROR: llave duplicada viola restricción de unicidad "unique_comprobante_por_tipo"
Detail: Ya existe la llave (punto_venta, numero_comprobante, tipo_comprobante)=(1, 0, NOTA_CREDITO_B).
```

### Causas Identificadas:
1. **Números temporales**: Se usaban números negativos temporales que causaban conflictos
2. **Consultas a AFIP**: Cada generación requería consultar a AFIP para obtener el último número
3. **Dependencia de conectividad**: Sin conexión a AFIP, no se podían generar comprobantes
4. **Conflictos de concurrencia**: <PERSON><PERSON><PERSON><PERSON> requests simultáneos podían obtener el mismo número

## Solución Implementada

### 1. **Tabla de Numeración Local**

Creada tabla `comprobante_numeracion` que trackea el último número por punto de venta y tipo:

```sql
CREATE TABLE comprobante_numeracion (
    id SERIAL PRIMARY KEY,
    punto_venta INTEGER NOT NULL,
    tipo_comprobante VARCHAR(20) NOT NULL,
    ultimo_numero INTEGER NOT NULL DEFAULT 0,
    creado_en TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    actualizado_en TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT unique_numeracion_por_tipo UNIQUE (punto_venta, tipo_comprobante)
);
```

### 2. **Repositorio de Numeración**

Implementado `ComprobanteNumeracionRepositoryPort` con operaciones atómicas:

- `obtenerYReservarSiguienteNumero()`: Obtiene y reserva el siguiente número (con lock)
- `sincronizarConAfip()`: Sincroniza con AFIP cuando hay diferencias
- `actualizarUltimoNumero()`: Actualiza solo si el nuevo número es mayor

### 3. **Flujo Mejorado**

#### Antes (Problemático):
```
1. Crear comprobante con número temporal (-123456)
2. Guardar en BD con número temporal
3. Consultar AFIP para obtener número real
4. Actualizar BD con número real de AFIP
❌ Conflictos si múltiples requests usan el mismo número temporal
```

#### Ahora (Correcto):
```
1. Obtener siguiente número del repositorio local (atómico)
2. Crear comprobante con número real
3. Enviar a AFIP con número conocido
4. Guardar en BD con número correcto desde el inicio
✅ Sin conflictos, sin números temporales
```

## Beneficios Logrados

### ✅ **Eliminación de Problemas**:
1. **Sin números temporales**: Se usan números reales desde el inicio
2. **Sin consultas a AFIP**: La numeración se maneja localmente
3. **Sin conflictos de concurrencia**: Operaciones atómicas con locks
4. **Sin dependencia de conectividad**: Funciona offline

### ✅ **Mejoras de Performance**:
1. **Menos llamadas a AFIP**: Solo para autorización, no para numeración
2. **Operaciones más rápidas**: Sin consultas adicionales de numeración
3. **Mejor concurrencia**: Locks específicos por punto de venta y tipo

### ✅ **Mayor Confiabilidad**:
1. **Consistencia garantizada**: Numeración local sincronizada con AFIP
2. **Recuperación automática**: Sincronización cuando hay diferencias
3. **Validación de consistencia**: Verifica que AFIP devuelva el número esperado

## Arquitectura de la Solución

### Componentes Nuevos:

1. **`ComprobanteNumeracion`** (Modelo de Dominio)
   - Representa el tracking de numeración
   - Métodos: `siguienteNumero()`, `conNuevoNumero()`

2. **`ComprobanteNumeracionRepositoryPort`** (Puerto)
   - Interfaz para operaciones de numeración
   - Operaciones atómicas y sincronización

3. **`JooqComprobanteNumeracionRepository`** (Adaptador)
   - Implementación JOOQ con transacciones
   - Locks pesimistas para concurrencia

### Integración:

- **`ComprobanteServiceImpl`**: Usa el repositorio de numeración antes de llamar a AFIP
- **Koin Configuration**: Inyección de dependencias configurada
- **Migración V26**: Tabla creada e inicializada con datos existentes

## Flujo de Generación Actualizado

```kotlin
// 1. Obtener número del repositorio (atómico)
val numeroComprobante = numeracionRepository.obtenerYReservarSiguienteNumero(
    puntoVenta, tipoComprobante
)

// 2. Crear comprobante con número real
val comprobante = Comprobante.createFromSale(
    sale = sale,
    numeroComprobante = numeroComprobante, // Número real, no temporal
    // ... otros parámetros
)

// 3. Enviar a AFIP (ya conocemos el número)
val afipResponse = afipService.solicitarCAE(...)

// 4. Validar consistencia
if (afipResponse.numeroComprobante != numeroComprobante) {
    // Sincronizar si hay diferencia
    numeracionRepository.sincronizarConAfip(...)
}

// 5. Guardar comprobante (ya con número correcto)
saleRepository.saveComprobante(comprobante)
```

## Casos de Uso Soportados

### ✅ **Generación Normal**:
- Facturas, notas de crédito y débito
- Online (CAE) y offline (CAEA)
- Múltiples puntos de venta

### ✅ **Concurrencia**:
- Múltiples requests simultáneos
- Locks por punto de venta y tipo
- Sin conflictos de numeración

### ✅ **Recuperación**:
- Sincronización automática con AFIP
- Detección de inconsistencias
- Corrección de numeración local

## Estado Final

**✅ PROBLEMA COMPLETAMENTE RESUELTO**

- ❌ Números temporales eliminados
- ❌ Conflictos de numeración eliminados  
- ❌ Dependencia de AFIP para numeración eliminada
- ✅ Numeración local confiable implementada
- ✅ Sincronización automática con AFIP
- ✅ Operaciones atómicas para concurrencia
- ✅ Performance mejorada significativamente

**El sistema ahora puede generar comprobantes de forma confiable, rápida y sin conflictos de numeración.**
