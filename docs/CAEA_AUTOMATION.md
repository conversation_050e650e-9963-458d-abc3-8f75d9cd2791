# Automatización CAEA - Documentación

## Resumen

La automatización CAEA se ejecuta automáticamente al iniciar la aplicación como última etapa del startup. El sistema verifica el estado de los CAEAs y realiza solicitudes o informes automáticamente según las reglas de AFIP.

## Reglas de Negocio CAEA

### Períodos de Validez
- **Orden 1**: Del 1 al 15 de cada mes (inclusive)
- **Orden 2**: Del 16 al último día del mes (inclusive)

### Solicitud de CAEA
- **Período actual**: Se puede solicitar cualquier día dentro del período actual
- **Período siguiente**: Se puede solicitar hasta **4 días antes** del inicio del próximo período

### Información de Movimientos
- **Plazo**: Debe informarse dentro de los **8 días corridos** posteriores al vencimiento del período
- **Método automático**: El sistema decide automáticamente si usar `FECAEASinMovimientoInformar` o `FECAEARegInformativo`

## Configuración

### Variables de Entorno

```bash
# Habilitar/deshabilitar automatización
CAEA_AUTOMATION_ENABLED=true

# Puntos de venta offline que usan CAEA (separados por coma)
CAEA_PUNTOS_VENTA_OFFLINE=2,3

# Solicitar ambos órdenes cuando sea posible (deshabilitado por defecto)
CAEA_SOLICITAR_AMBOS_ORDENES=false

# Informar CAEAs vencidos automáticamente
CAEA_INFORMAR_AUTOMATICAMENTE=true

# Log detallado de operaciones
CAEA_LOG_DETALLADO=true
```

### Configuración por Defecto

Si no se especifican variables de entorno, se usan estos valores:

```kotlin
CAEAAutomationConfig(
    enabled = true,
    puntosVentaOffline = listOf(2),
    solicitarAmbosOrdenes = false,  // Deshabilitado por defecto para evitar solicitudes innecesarias
    informarAutomaticamente = true,
    logDetallado = true
)
```

## Flujo de Automatización

### Al Iniciar la Aplicación

```
1. Aplicación inicia
2. Servicios se configuran
3. Base de datos se conecta
4. → CAEAStartupService.ejecutarAutomatizacion()
   ├── Para cada punto de venta offline:
   │   ├── Verificar CAEA período actual
   │   ├── Verificar CAEA próximo período
   │   ├── Informar CAEAs vencidos
   │   └── Alertar CAEAs no informables
   └── Log resumen de operaciones
5. Aplicación lista para requests
```

### Lógica de Decisión

#### 1. CAEA Período Actual
```
¿Existe CAEA para período actual?
├── SÍ → No hacer nada
└── NO → Solicitar CAEA (sin restricciones)
```

#### 2. CAEA Próximo Período
```
¿Faltan ≤ 4 días para el próximo período?
├── NO → No hacer nada
└── SÍ → ¿Existe CAEA para próximo período?
    ├── SÍ → No hacer nada
    └── NO → Solicitar CAEA anticipado
        └── Si solicitarAmbosOrdenes=true y es orden 1
            → Solicitar también orden 2
```

#### 3. Informes Pendientes
```
Para cada CAEA existente en la base de datos:
├── ¿Vencido hace ≤ 8 días?
│   ├── SÍ → ¿Ya informado?
│   │   ├── NO → Informar automáticamente
│   │   └── SÍ → No hacer nada
│   └── NO → ¿Ya informado?
│       ├── NO → ⚠️ ALERTA: Ya no se puede informar
│       └── SÍ → No hacer nada

Nota: Solo verifica CAEAs que realmente existen, no períodos teóricos.
```

## Ejemplos de Ejecución

### Escenario 1: Inicio de Mes
```
Fecha: 1 de Agosto 2025
Período actual: 202508-1 (01/08 al 15/08)
Próximo período: 202508-2 (16/08 al 31/08)

Acciones:
✅ Solicitar CAEA para 202508-1 (si no existe)
❌ No solicitar 202508-2 (faltan más de 4 días)
✅ Informar CAEA 202507-2 si venció hace ≤ 8 días
```

### Escenario 2: Mitad de Mes
```
Fecha: 12 de Agosto 2025
Período actual: 202508-1 (01/08 al 15/08)
Próximo período: 202508-2 (16/08 al 31/08)

Acciones:
✅ Solicitar CAEA para 202508-1 (si no existe)
✅ Solicitar CAEA para 202508-2 (faltan 4 días)
✅ Solicitar CAEA para 202509-1 (si solicitarAmbosOrdenes=true)
```

### Escenario 3: Final de Mes
```
Fecha: 25 de Agosto 2025
Período actual: 202508-2 (16/08 al 31/08)
Próximo período: 202509-1 (01/09 al 15/09)

Acciones:
✅ Solicitar CAEA para 202508-2 (si no existe)
❌ No solicitar 202509-1 (faltan más de 4 días)
✅ Informar CAEA 202508-1 si venció hace ≤ 8 días
```

## Logs de Ejemplo

### Startup Exitoso
```
INFO  - === Iniciando Automatización CAEA ===
INFO  - === Estado Períodos CAEA para 2025-08-12 ===
INFO  - Período Actual: 202508-1 (2025-08-01 al 2025-08-15)
INFO  - Próximo Período: 202508-2 (2025-08-16 al 2025-08-31)
INFO  - Puede solicitar próximo: true
INFO  - Procesando automatización para punto de venta 2
INFO  - Verificando CAEA período actual: 202508-1 para PV 2
INFO  - ✅ CAEA solicitado exitosamente: 21234567890123 (período 202508-1)
INFO  - Verificando CAEA próximo período: 202508-2 para PV 2
INFO  - ✅ CAEA solicitado exitosamente: 21234567890124 (período 202508-2)
INFO  - === Automatización CAEA completada: 2 operaciones realizadas ===
```

### Con Informes Pendientes
```
INFO  - Informando CAEA vencido: 21234567890120 (período 202507-2)
INFO  - CAEA 21234567890120 informado exitosamente
WARN  - ⚠️ ALERTA: CAEA 21234567890115 (período 202507-1) ya no puede ser informado. Límite era: 2025-07-23
```

## Monitoreo y Troubleshooting

### Verificar Estado
Los logs muestran el estado detallado de períodos al iniciar si `CAEA_LOG_DETALLADO=true`.

### Deshabilitar Temporalmente
```bash
CAEA_AUTOMATION_ENABLED=false
```

### Solo Informar (No Solicitar)
```bash
CAEA_AUTOMATION_ENABLED=true
CAEA_PUNTOS_VENTA_OFFLINE=""  # Lista vacía
CAEA_INFORMAR_AUTOMATICAMENTE=true
```

### Errores Comunes
- **Error de credenciales**: Verificar configuración AFIP
- **CAEA ya existe**: Normal, el sistema lo detecta y continúa
- **Error de red**: Se registra pero no detiene la aplicación

## Beneficios

1. **Compliance automático**: Nunca se olvida solicitar o informar CAEAs
2. **Reducción de errores**: Elimina intervención manual
3. **Visibilidad**: Logs claros del estado y acciones
4. **Flexibilidad**: Configurable por variables de entorno
5. **Robustez**: Manejo de errores sin afectar el startup

## Limitaciones

- Solo se ejecuta al iniciar la aplicación
- No hay scheduler recurrente (por diseño)
- Requiere reinicio para procesar nuevos períodos
- Depende de conectividad con AFIP al startup
