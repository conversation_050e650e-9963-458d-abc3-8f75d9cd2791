# Implementación de Impresión Térmica con Estilos ESC/POS

## Resumen

Se ha implementado la funcionalidad completa de impresión de comprobantes fiscales y no fiscales usando la librería `escpos-coffee` con soporte completo para estilos ESC/POS profesionales.

## Características Implementadas

### ✅ Impresión Fiscal con Estilos
- Tickets con datos fiscales completos (CAE, CUIT, etc.)
- **Estilos ESC/POS**: negrita, subrayado, diferentes tamaños de fuente
- QR codes fiscales según especificaciones AFIP
- Formato oficial para comprobantes tipo B (consumidor final)
- Información detallada de IVA por tipo (usando enum TipoIva)

### ✅ Impresión No Fiscal con Estilos
- Tickets genéricos sin validez fiscal con formato profesional
- Aplicación de estilos para mejor legibilidad
- Información básica de la venta con formato mejorado

### ✅ Conectividad Ethernet Robusta
- Conexión TCP/IP a impresora térmica
- Configuración flexible de IP y puerto
- Manejo robusto de errores de conexión con timeouts
- Recuperación automática de errores de comunicación

### ✅ Formateo Profesional con ESC/POS
- Comandos ESC/POS nativos para estilos
- Alineación automática (izquierda, derecha, centrado)
- Tamaños de fuente: normal, doble altura, doble tamaño
- Timing optimizado para evitar cortes prematuros
- Separadores visuales y espaciado apropiado

## Archivos Implementados

### 1. **EscPosPrinterAdapter.kt**
- Implementación real de impresión usando escpos-coffee
- Manejo de conexiones TCP/IP con timeouts configurables
- Gestión robusta de errores específicos de impresión
- Integración con StyledTicketFormatter

### 2. **PrinterConfiguration.kt**
- Configuración centralizada de impresora y empresa
- Carga automática desde variables de entorno
- Validación de configuración
- **Nuevos parámetros**: cutDelayMs, feedLinesBeforeCut, enabled

### 3. **StyledTicketFormatter.kt** (Reemplaza TicketFormatter)
- Formateo profesional con comandos ESC/POS nativos
- Estilos: negrita, subrayado, tamaños de fuente
- QR codes fiscales según especificaciones AFIP
- Manejo de diferentes tipos de IVA (enum TipoIva)
- Timing optimizado para evitar cortes prematuros

### 4. **Tests de Integración**
- PrinterIntegrationTest.kt - Tests con impresora física
- StyledTicketFormatterTest.kt - Tests de formateo
- PrinterCutTimingTest.kt - Tests de timing de corte
- Validación de contenido y formato

## Configuración Requerida

### Variables de Entorno (.env)

```env
# Impresora
PRINTER_IP=*************
PRINTER_PORT=9100

# Información de la Empresa
COMPANY_NAME=Mi Empresa S.A.
COMPANY_ADDRESS=Av. Corrientes 1234, CABA
COMPANY_PHONE=011-4567-8900
COMPANY_EMAIL=<EMAIL>
COMPANY_WEBSITE=www.miempresa.com.ar

# AFIP (ya existente)
AFIP_CUIT=20349249902
```

### Dependencia Gradle

```kotlin
// ESC/POS Printer
implementation("com.github.anastaciocintra:escpos-coffee:4.1.0")
```

## Uso

### Impresión Automática
La impresión se integra automáticamente con el flujo de ventas existente:

```kotlin
// Al crear una venta con impresión
saleService.createSale(
    clienteId = 1,
    vendedor = "usuario1",
    itemsRequest = items,
    imprimirTicket = true,        // Imprime ticket genérico
    facturaOnline = true          // + imprime ticket fiscal si se genera comprobante
)
```

### Impresión Manual
También se puede imprimir manualmente usando el servicio de impresión:

```kotlin
// Ticket genérico
printService.imprimirTicket(ventaId = Id(123))

// Ticket fiscal
printService.imprimirTicket(ventaId = Id(123), comprobanteId = Id(456))
```

### API Endpoints
```http
# Ticket de venta (no fiscal)
POST /api/print/ticket-venta/123

# Ticket de comprobante (fiscal)
POST /api/print/ticket-comprobante/456
```

## Formato de Tickets

### Ticket Fiscal
```
           Mi Empresa S.A.
        CUIT: 20-34924990-2
      Av. Corrientes 1234, CABA
        Tel: 011-4567-8900
       <EMAIL>

================================================
      *** COMPROBANTE FISCAL ***

              FACTURA_B
        Nro: 0001-00000123

Fecha: 28/06/2025 14:30:15
CAE: 12345678901234
Vto CAE: 08/07/2025
================================================
Venta: V-abc12345
Fecha: 28/06/2025 14:30:15
Vendedor: Juan Pérez
Medio Pago: EFECTIVO

Cliente: Cliente Test S.A.
CUIT: 20-98765432-1

DETALLE:
------------------------------------------------
Producto Test 1
                        2.00 x $ 100.00 = $ 200.00
  IVA 21.00%

Producto Test 2
                        1.00 x $ 50.00 = $ 50.00
  IVA 21.00%

------------------------------------------------
                           Subtotal: $ 206.61
                                IVA: $ 43.39
------------------------------------------------
                             TOTAL: $ 250.00
================================================

      *** COMPROBANTE AUTORIZADO ***
     Esta administración federal no se
       responsabiliza por los datos
     ingresados en el detalle de la operación



```

### Ticket No Fiscal
```
           Mi Empresa S.A.
        CUIT: 20-34924990-2
      Av. Corrientes 1234, CABA

================================================
       *** TICKET NO FISCAL ***
     DOCUMENTO NO VÁLIDO COMO FACTURA
================================================
Venta: V-abc12345
Fecha: 28/06/2025 14:30:15
Vendedor: Juan Pérez
Medio Pago: EFECTIVO

DETALLE:
------------------------------------------------
Producto Test 1
                        2.00 x $ 100.00 = $ 200.00
  IVA 21.00%

------------------------------------------------
                             TOTAL: $ 250.00
================================================

        *** GRACIAS POR SU COMPRA ***



```

## Manejo de Errores

### Tipos de Excepciones
- **PrinterException**: Error general de impresión
- **PrinterConnectionException**: Error específico de conexión

### Logging
- Logs detallados de todas las operaciones de impresión
- Información de debug para troubleshooting
- Registro de errores con stack traces

### Recuperación
- La aplicación continúa funcionando aunque falle la impresión
- Errores se reportan al usuario sin interrumpir el flujo
- Posibilidad de reimpresión manual

## Testing

Se incluyen tests completos que verifican:
- ✅ Formateo correcto de tickets fiscales
- ✅ Formateo correcto de tickets no fiscales
- ✅ Manejo de diferentes tipos de IVA
- ✅ Formateo de CUIT y monedas
- ✅ Alineación y espaciado

## Próximos Pasos

1. **Configurar impresora física**: Verificar IP y puerto en .env
2. **Personalizar datos empresa**: Actualizar variables de entorno
3. **Probar conectividad**: Ejecutar impresión de prueba
4. **Ajustar formato**: Modificar TicketFormatter si es necesario

## Compatibilidad

- ✅ Compatible con impresoras ESC/POS estándar
- ✅ Conexión Ethernet TCP/IP
- ✅ Formato térmico 80mm (48 caracteres)
- ✅ Corte automático de papel
- ✅ Arquitectura hexagonal mantenida
