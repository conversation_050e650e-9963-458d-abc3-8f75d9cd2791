# Ejemplos de Uso - Cancelación de Ventas

## 📋 Ejemplos de API

### 1. <PERSON><PERSON><PERSON> Ventas (Excluyendo Canceladas por Defecto)

```http
GET /api/sales?page=1&size=20
```

**Comportamiento**: Por defecto excluye ventas canceladas.

**Respuesta**:
```json
{
  "sales": [
    {
      "id": 1,
      "numeroVenta": "V-12345678",
      "fechaVenta": "15/01/2025 10:30",
      "usuarioUsername": "vendedor1",
      "usuarioNombre": "<PERSON>",
      "montoTotal": "150.00",
      "medioPago": "EFECTIVO",
      "comprobanteEmitido": true,
      "cancelada": false,
      "fechaCancelacion": null,
      "usuarioCancelacion": null,
      "motivoCancelacion": null,
      "items": [...]
    }
  ],
  "totalElements": 25,
  "totalPages": 2,
  "page": 1,
  "size": 20
}
```

### 2. Obtener Ventas Incluyendo Canceladas

```http
GET /api/sales?incluirCanceladas=true&page=1&size=20
```

**Comportamiento**: Incluye tanto ventas activas como canceladas.

**Respuesta**:
```json
{
  "sales": [
    {
      "id": 1,
      "numeroVenta": "V-12345678",
      "fechaVenta": "15/01/2025 10:30",
      "usuarioUsername": "vendedor1",
      "usuarioNombre": "Juan Pérez",
      "montoTotal": "150.00",
      "medioPago": "EFECTIVO",
      "comprobanteEmitido": true,
      "cancelada": false,
      "fechaCancelacion": null,
      "usuarioCancelacion": null,
      "motivoCancelacion": null,
      "items": [...]
    },
    {
      "id": 2,
      "numeroVenta": "V-87654321",
      "fechaVenta": "14/01/2025 16:45",
      "usuarioUsername": "vendedor2",
      "usuarioNombre": "María García",
      "montoTotal": "200.00",
      "medioPago": "TARJETA",
      "comprobanteEmitido": true,
      "cancelada": true,
      "fechaCancelacion": "15/01/2025 09:15",
      "usuarioCancelacion": "admin",
      "motivoCancelacion": "Error en el producto - cliente solicitó devolución",
      "items": [...]
    }
  ],
  "totalElements": 30,
  "totalPages": 2,
  "page": 1,
  "size": 20
}
```

### 3. Cancelar una Venta (Sin Comprobante)

```http
POST /api/sales/123/cancel
Content-Type: application/json

{
  "usuarioCancelacion": "admin",
  "motivo": "Error en la carga - producto incorrecto",
  "generarNotaCreditoOnline": true
}
```

**Respuesta**:
```json
{
  "success": true,
  "message": "Venta cancelada exitosamente",
  "ventaId": 123,
  "numeroVenta": "V-12345678",
  "notaCreditoGenerada": false,
  "notaCreditoId": null,
  "notaCreditoNumero": null,
  "error": null
}
```

### 4. Cancelar una Venta (Con Comprobante - Genera Nota de Crédito)

```http
POST /api/sales/456/cancel
Content-Type: application/json

{
  "usuarioCancelacion": "supervisor",
  "motivo": "Cliente solicitó devolución por defecto en el producto",
  "generarNotaCreditoOnline": true
}
```

**Respuesta**:
```json
{
  "success": true,
  "message": "Venta cancelada y nota de crédito B generada exitosamente",
  "ventaId": 456,
  "numeroVenta": "V-87654321",
  "notaCreditoGenerada": true,
  "notaCreditoId": 789,
  "notaCreditoNumero": "0001-00000123",
  "error": null
}
```

### 5. Cancelar con Nota de Crédito Offline (CAEA)

```http
POST /api/sales/789/cancel
Content-Type: application/json

{
  "usuarioCancelacion": "admin",
  "motivo": "Cancelación fuera de horario - generar CAEA",
  "generarNotaCreditoOnline": false
}
```

**Respuesta**:
```json
{
  "success": true,
  "message": "Venta cancelada y nota de crédito B generada exitosamente",
  "ventaId": 789,
  "numeroVenta": "V-11223344",
  "notaCreditoGenerada": true,
  "notaCreditoId": 890,
  "notaCreditoNumero": "0001-00000124",
  "error": null
}
```

### 6. Obtener Solo Ventas Canceladas

```http
GET /api/sales/cancelled?limit=50
```

**Respuesta**:
```json
{
  "cancelledSales": [
    {
      "ventaId": 456,
      "numeroVenta": "V-87654321",
      "fechaVenta": "14/01/2025 16:45",
      "fechaCancelacion": "15/01/2025 09:15",
      "usuarioVenta": "María García",
      "usuarioCancelacion": "supervisor",
      "motivoCancelacion": "Cliente solicitó devolución por defecto en el producto",
      "montoTotal": "200.00",
      "teniaComprobante": true,
      "notaCreditoGenerada": true,
      "notaCreditoNumero": "0001-00000123"
    }
  ],
  "totalCancelaciones": 5
}
```

### 7. Obtener Estadísticas de Cancelaciones

```http
GET /api/sales/cancellation-stats
```

**Respuesta**:
```json
{
  "totalCancelaciones": 15,
  "cancelacionesConNotaCredito": 12,
  "cancelacionesSinNotaCredito": 3
}
```

## 🔍 Casos de Uso Prácticos

### Caso 1: Consulta Diaria de Ventas (Excluyendo Canceladas)
```http
GET /api/sales?fechaDesde=2025-01-15 00:00:00&fechaHasta=2025-01-15 23:59:59
```
- **Resultado**: Solo ventas válidas del día
- **Uso**: Reportes de ventas diarias

### Caso 2: Auditoría Completa (Incluyendo Canceladas)
```http
GET /api/sales?fechaDesde=2025-01-15 00:00:00&fechaHasta=2025-01-15 23:59:59&incluirCanceladas=true
```
- **Resultado**: Todas las operaciones del día (válidas y canceladas)
- **Uso**: Auditorías, control de gestión

### Caso 3: Filtro por Usuario Incluyendo Canceladas
```http
GET /api/sales?usuarios=vendedor1&incluirCanceladas=true
```
- **Resultado**: Todas las ventas de un vendedor específico
- **Uso**: Evaluación de desempeño, análisis de errores

### Caso 4: Solo Ventas con Comprobante (Excluyendo Canceladas)
```http
GET /api/sales?comprobanteEmitido=true
```
- **Resultado**: Solo ventas facturadas válidas
- **Uso**: Reportes fiscales, reconciliación con AFIP

## ⚠️ Casos de Error

### Error: Usuario No Encontrado
```http
POST /api/sales/123/cancel
{
  "usuarioCancelacion": "usuario_inexistente",
  "motivo": "Test",
  "generarNotaCreditoOnline": true
}
```

**Respuesta**:
```json
{
  "success": false,
  "message": "Usuario 'usuario_inexistente' no encontrado",
  "ventaId": 123,
  "numeroVenta": "",
  "notaCreditoGenerada": false,
  "error": "Usuario no encontrado"
}
```

### Error: Venta Ya Cancelada
```http
POST /api/sales/456/cancel
{
  "usuarioCancelacion": "admin",
  "motivo": "Intentar cancelar nuevamente",
  "generarNotaCreditoOnline": true
}
```

**Respuesta**:
```json
{
  "success": false,
  "message": "La venta V-87654321 ya está cancelada",
  "ventaId": 456,
  "numeroVenta": "V-87654321",
  "notaCreditoGenerada": false,
  "error": "Venta ya cancelada"
}
```

### Error: Venta No Encontrada
```http
POST /api/sales/99999/cancel
{
  "usuarioCancelacion": "admin",
  "motivo": "Test",
  "generarNotaCreditoOnline": true
}
```

**Respuesta**:
```json
{
  "success": false,
  "message": "Venta 99999 no encontrada",
  "ventaId": 99999,
  "numeroVenta": "",
  "notaCreditoGenerada": false,
  "error": "Venta no encontrada"
}
```

## 📊 Mejores Prácticas

### 1. Consultas de Reportes
- **Usar `incluirCanceladas=false`** (por defecto) para reportes de ventas
- **Usar `incluirCanceladas=true`** para auditorías completas

### 2. Cancelaciones
- **Siempre proporcionar motivo detallado** para auditoría
- **Usar `generarNotaCreditoOnline=true`** durante horario comercial
- **Usar `generarNotaCreditoOnline=false`** fuera de horario o sin conectividad

### 3. Monitoreo
- **Revisar estadísticas** regularmente con `/cancellation-stats`
- **Monitorear historial** con `/cancelled` para detectar patrones

### 4. Filtros Combinados
```http
GET /api/sales?fechaDesde=2025-01-01 00:00:00&usuarios=vendedor1,vendedor2&comprobanteEmitido=true&incluirCanceladas=true
```
- Permite análisis detallados por período, usuario y tipo de operación
