# API de Intentos de Comprobantes

## Descripción
Esta API permite consultar el historial de intentos de generación de comprobantes fiscales, tanto exitosos como fallidos, proporcionando trazabilidad completa del proceso de facturación.

## Endpoints

### GET /api/comprobantes/intentos/{ventaId}
Obtiene todos los intentos de comprobantes para una venta específica.

**Parámetros:**
- `ventaId` (path): ID de la venta

**Respuesta exitosa (200):**
```json
[
  {
    "id": 1,
    "ventaId": 123,
    "tipoComprobante": "FACTURA_B",
    "puntoVenta": 1,
    "tipoOperacion": "CAE_ONLINE",
    "estado": "EXITOSO",
    "fechaIntento": "27/06/2025 14:30",
    "tiempoRespuestaMs": 1250,
    "cae": "75123456789012",
    "numeroComprobante": 1001,
    "fechaVencimientoCae": "07/07/2025",
    "comprobanteId": 456,
    "codigoError": null,
    "mensajeError": null,
    "observacionesAfip": []
  },
  {
    "id": 2,
    "ventaId": 123,
    "tipoComprobante": "FACTURA_B",
    "puntoVenta": 1,
    "tipoOperacion": "CAE_ONLINE",
    "estado": "FALLIDO",
    "fechaIntento": "27/06/2025 14:25",
    "tiempoRespuestaMs": 2100,
    "cae": null,
    "numeroComprobante": null,
    "fechaVencimientoCae": null,
    "comprobanteId": null,
    "codigoError": "AFIP_REJECTED",
    "mensajeError": "Comprobante rechazado por AFIP",
    "observacionesAfip": [
      "Error en validación de CUIT",
      "Verificar datos del cliente"
    ]
  }
]
```

### GET /api/comprobantes/intentos-fallidos/{ventaId}
Obtiene solo los intentos fallidos para una venta específica.

**Parámetros:**
- `ventaId` (path): ID de la venta

**Respuesta exitosa (200):**
```json
[
  {
    "id": 2,
    "ventaId": 123,
    "tipoComprobante": "FACTURA_B",
    "puntoVenta": 1,
    "tipoOperacion": "CAE_ONLINE",
    "estado": "FALLIDO",
    "fechaIntento": "27/06/2025 14:25",
    "tiempoRespuestaMs": 2100,
    "cae": null,
    "numeroComprobante": null,
    "fechaVencimientoCae": null,
    "comprobanteId": null,
    "codigoError": "AFIP_REJECTED",
    "mensajeError": "Comprobante rechazado por AFIP",
    "observacionesAfip": [
      "Error en validación de CUIT",
      "Verificar datos del cliente"
    ]
  }
]
```

## Campos de Respuesta

### Campos Comunes
- `id`: ID único del intento
- `ventaId`: ID de la venta asociada
- `tipoComprobante`: Tipo de comprobante (FACTURA_B, NOTA_CREDITO_B, etc.)
- `puntoVenta`: Punto de venta utilizado
- `tipoOperacion`: CAE_ONLINE o CAEA_OFFLINE
- `estado`: EXITOSO, FALLIDO, o PENDIENTE
- `fechaIntento`: Fecha y hora del intento
- `tiempoRespuestaMs`: Tiempo de respuesta de AFIP en milisegundos

### Campos de Éxito (cuando estado = "EXITOSO")
- `cae`: Código de Autorización Electrónica
- `numeroComprobante`: Número del comprobante asignado por AFIP
- `fechaVencimientoCae`: Fecha de vencimiento del CAE
- `comprobanteId`: ID del comprobante generado

### Campos de Error (cuando estado = "FALLIDO")
- `codigoError`: Código de error del sistema
- `mensajeError`: Mensaje descriptivo del error
- `observacionesAfip`: Array de observaciones devueltas por AFIP

## Códigos de Error Comunes

### Errores de AFIP
- `AFIP_REJECTED`: Comprobante rechazado por AFIP
- Observaciones típicas:
  - "Error en validación de CUIT"
  - "Punto de venta no autorizado"
  - "Tipo de comprobante no válido"

### Errores de Sistema
- `SYSTEM_ERROR`: Error interno del sistema
- `NETWORK_ERROR`: Error de conectividad con AFIP
- `TIMEOUT_ERROR`: Timeout en la comunicación con AFIP

## Casos de Uso

### 1. Diagnóstico de Problemas de Facturación
```bash
# Consultar todos los intentos para una venta
GET /api/comprobantes/intentos/123

# Ver solo los errores para diagnóstico
GET /api/comprobantes/intentos-fallidos/123
```

### 2. Auditoría de Rendimiento
Los campos `tiempoRespuestaMs` permiten analizar el rendimiento del webservice de AFIP.

### 3. Reintento de Comprobantes Fallidos
Usar la información de errores para corregir problemas y reintentar la generación.

## Notas Técnicas

- Los intentos se ordenan por fecha descendente (más recientes primero)
- Las observaciones de AFIP se almacenan como JSON y se deserializan automáticamente
- Los tiempos de respuesta incluyen solo la comunicación con AFIP, no el procesamiento local
- Los intentos exitosos siempre tienen un `comprobanteId` asociado
