# Bulk Update con Valores Especiales

## Resumen

Se ha implementado funcionalidad para permitir limpiar/remover campos opcionales en operaciones de bulk update de productos usando valores especiales.

## Problema Resuelto

Anteriormente, en el bulk update de productos no era posible:
- Remover una categoría asignada (establecer `categoria = null`)
- Limpiar el stock actual (establecer `stockActual = null`)

Cuando se enviaba `categoriaId = null` o no se incluía el campo, el sistema simplemente no actualizaba ese campo, manteniendo el valor existente.

## Solución Implementada

### Valores Especiales

Se utilizan valores especiales para indicar que se desea limpiar un campo opcional:

#### Para Categoría (`categoriaId`)
- `categoriaId <= 0` → Establece `categoria = null` en la base de datos
- Ejemplos válidos: `0`, `-1`, `-5`, etc.

#### Para Stock Actual (`stockActual`)
- `stockActual < 0` → Establece `stockActual = null` en la base de datos  
- Ejemplos válidos: `-1`, `-10`, `-999`, etc.

### Comportamiento

| Valor enviado | Comportamiento |
|---------------|----------------|
| `categoriaId = null` | No actualiza la categoría (mantiene valor actual) |
| `categoriaId = 0` | Limpia la categoría (`categoria = null`) |
| `categoriaId = -1` | Limpia la categoría (`categoria = null`) |
| `categoriaId = 5` | Asigna categoría con ID 5 |
| `stockActual = null` | No actualiza el stock (mantiene valor actual) |
| `stockActual = -1` | Limpia el stock (`stockActual = null`) |
| `stockActual = 0` | Establece stock en 0 |
| `stockActual = 50` | Establece stock en 50 |

## Ejemplos de Uso

### Limpiar solo la categoría

```http
PUT /api/productos/bulk
{
  "codigos": [1001, 1002, 1003],
  "categoriaId": 0
}
```

### Limpiar solo el stock

```http
PUT /api/productos/bulk
{
  "codigos": [1001, 1002],
  "stockActual": -1
}
```

### Limpiar categoría y stock, actualizar precio

```http
PUT /api/productos/bulk
{
  "codigos": [1001],
  "categoriaId": 0,
  "stockActual": -1,
  "precioUnitario": 99.99
}
```

### Asignar categoría válida (comportamiento normal)

```http
PUT /api/productos/bulk
{
  "codigos": [1001, 1002],
  "categoriaId": 5,
  "stockActual": 100
}
```

## Implementación Técnica

### 1. Controller (`ProductoController.kt`)

El controller procesa los valores especiales antes de enviarlos al service:

```kotlin
val categoriaIdToUpdate = when {
    request.categoriaId == null -> null
    request.categoriaId <= 0 -> Id(-1) // Valor especial interno
    else -> Id(request.categoriaId)
}

val stockActualToUpdate = when {
    request.stockActual == null -> null
    request.stockActual < 0 -> -1 // Valor especial interno
    else -> request.stockActual
}
```

### 2. Repository JOOQ (`JooqProductoRepository.kt`)

El repositorio interpreta los valores especiales internos:

```kotlin
categoriaId?.let {
    if (it.value == -1) {
        // Valor especial para limpiar la categoría
        updateQuery = updateQuery.set(PRODUCTOS.CATEGORIA_ID, null as Int?)
    } else {
        updateQuery = updateQuery.set(PRODUCTOS.CATEGORIA_ID, it.value)
    }
}

stockActual?.let {
    if (it == -1) {
        // Valor especial para limpiar el stock
        updateQuery = updateQuery.set(PRODUCTOS.STOCK_ACTUAL, null as Int?)
    } else {
        updateQuery = updateQuery.set(PRODUCTOS.STOCK_ACTUAL, it)
    }
}
```

### 3. Repository Mock (`MockProductoRepository.kt`)

El repositorio mock maneja los mismos valores especiales para consistencia en tests.

## Tests

Se han agregado tests específicos para verificar:

- ✅ Limpiar categoría con `categoriaId = 0`
- ✅ Limpiar categoría con `categoriaId < 0`
- ✅ Limpiar stock con `stockActual < 0`
- ✅ Limpiar ambos campos simultáneamente
- ✅ Comportamiento normal con valores válidos

## Compatibilidad

Esta implementación es **completamente compatible** con el comportamiento anterior:

- Los valores `null` siguen funcionando igual (no actualizan el campo)
- Los valores positivos válidos siguen funcionando igual
- Solo se agrega la nueva funcionalidad para valores especiales

## Archivos Modificados

- `src/main/kotlin/com/gnico/majo/adapter/controller/rest/ProductoController.kt`
- `src/main/kotlin/com/gnico/majo/adapter/persistence/JooqProductoRepository.kt`
- `src/test/kotlin/com/gnico/majo/domain/repository/MockProductoRepository.kt`
- `src/test/kotlin/com/gnico/majo/api/controller/ProductoControllerBulkUpdateTest.kt`
- `docs/BULK_UPDATE_SPECIAL_VALUES.md` (este archivo)
