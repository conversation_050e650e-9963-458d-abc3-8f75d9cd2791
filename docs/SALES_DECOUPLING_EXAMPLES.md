# Ejemplos de Uso - Sistema de Ventas Desacoplado

## Configuración Previa

Antes de usar los nuevos servicios, configurar las variables de entorno en `.env`:

```env
# Configuración de Comprobantes
PUNTO_DE_VENTA=1                    # Punto de venta por defecto
TIPO_COMPROBANTE_DEFAULT=FACTURA_B  # Tipo de comprobante por defecto
```

**Tipos de comprobante válidos:**
- `FACTURA_A`, `FACTURA_B`, `FACTURA_C`
- `NOTA_CREDITO_A`, `NOTA_CREDITO_B`, `NOTA_CREDITO_C`
- `NOTA_DEBITO_A`, `NOTA_DEBITO_B`, `NOTA_DEBITO_C`

## Escenarios de Uso Prácticos

### 1. Venta Normal (Comportamiento Original)

```kotlin
// Crear venta con facturación e impresión inmediata
val ventaId = saleService.createSale(
    clienteId = 123,
    vendedor = "vendedor1",
    itemsRequest = listOf(
        SaleItemRequest(
            productoId = 1001,
            cantidad = 2.0,
            precioUnitario = 150.00,
            // ... otros campos
        )
    ),
    medioPago = "EFECTIVO",
    imprimirTicket = true,
    facturaOnline = true,
    facturaOffline = false
)
```

### 2. Venta Solo Registro (Sin Facturación Inmediata)

```kotlin
// Crear venta sin facturar (para procesar después)
val ventaId = saleService.createSale(
    clienteId = 123,
    vendedor = "vendedor1",
    itemsRequest = items,
    medioPago = "EFECTIVO",
    imprimirTicket = true,      // Solo ticket genérico
    facturaOnline = false,      // Sin facturación
    facturaOffline = false
)

// Más tarde, generar comprobante desfasado (usando valores por defecto)
val comprobanteId = comprobanteService.generarComprobanteOnline(
    ventaId = ventaId
    // tipoComprobante y puntoVenta son opcionales
    // Se usan valores desde .env: TIPO_COMPROBANTE_DEFAULT y PUNTO_DE_VENTA
)
```

### 3. Procesamiento Masivo de Ventas Pendientes

```kotlin
// Obtener ventas sin comprobante
val ventasPendientes = comprobanteService.obtenerVentasSinComprobante(100)

println("Procesando ${ventasPendientes.size} ventas pendientes...")

ventasPendientes.forEach { venta ->
    try {
        val comprobanteId = comprobanteService.generarComprobanteOnline(
            ventaId = venta.ventaId
            // Usando valores por defecto desde .env
        )
        
        println("✓ Comprobante generado para venta ${venta.numeroVenta}")
        
        // Opcionalmente imprimir
        printService.imprimirTicketComprobante(comprobanteId)
        
    } catch (e: Exception) {
        println("✗ Error en venta ${venta.numeroVenta}: ${e.message}")
    }
}
```

### 4. Reimpresión de Documentos

```kotlin
// Buscar ventas por fecha para reimprimir
val ventas = printService.buscarVentasParaReimpresion(
    fechaDesde = "2024-01-15 00:00:00",
    fechaHasta = "2024-01-15 23:59:59",
    usuario = null,
    numeroVenta = null
)

println("Encontradas ${ventas.size} ventas del 15/01/2024")

ventas.forEach { venta ->
    println("Venta: ${venta.numeroVenta} - ${venta.montoTotal}")
    
    if (venta.tieneComprobante) {
        // Reimprimir comprobante fiscal
        venta.comprobantes.forEach { comprobante ->
            printService.imprimirComprobanteFiscal(comprobante.comprobanteId)
        }
    } else {
        // Reimprimir solo ticket de venta
        printService.imprimirTicketVenta(venta.ventaId)
    }
}
```

### 5. Búsqueda y Reimpresión por Número de Comprobante

```kotlin
// Buscar comprobante específico
val comprobante = comprobanteService.buscarComprobantePorNumero(
    puntoVenta = 1,
    numeroComprobante = 12345
)

if (comprobante != null) {
    println("Comprobante encontrado: ${comprobante.tipoComprobante}")
    println("CAE: ${comprobante.cae}")
    println("Estado: ${comprobante.estado}")
    
    // Reimprimir
    printService.imprimirComprobanteFiscal(comprobante.id)
} else {
    println("Comprobante no encontrado")
}
```

### 6. Consulta de Ventas por Usuario

```kotlin
// Obtener todas las ventas de un usuario
val ventasUsuario = saleService.findSalesWithFilters(
    usuarios = listOf("usuario1")
)

println("Ventas de usuario1: ${ventasUsuario.content.size}")

ventasUsuario.content.forEach { venta ->
    println("${venta.numeroVenta} - ${venta.fechaVenta} - $${venta.montoTotal}")

    // Ver comprobantes asociados usando SaleService
    val ventaCompleta = saleService.findSaleById(Id(venta.id))
    if (ventaCompleta?.comprobanteEmitido == true) {
        println("  └─ Comprobante emitido")
    } else {
        println("  └─ Sin comprobante")
    }
}
```

### 7. Recuperación de Errores de Facturación

```kotlin
// Escenario: Una venta se creó pero falló la facturación por problemas de conectividad

// 1. Buscar la venta
val venta = saleService.findSaleByNumero("V-12345678")
if (venta != null && !venta.comprobanteEmitido) {
    
    try {
        // 2. Intentar facturación online (con valores específicos)
        val comprobanteId = comprobanteService.generarComprobanteOnline(
            ventaId = venta.id!!,
            tipoComprobante = "FACTURA_B",  // Especificar tipo
            puntoVenta = 1                  // Especificar punto de venta
        )
        
        println("✓ Facturación recuperada exitosamente")
        
        // 3. Imprimir comprobante
        printService.imprimirTicketComprobante(comprobanteId)
        
    } catch (e: Exception) {
        println("✗ Facturación online falló, intentando offline...")
        
        try {
            // 4. Fallback a facturación offline (usando valores por defecto)
            val comprobanteId = comprobanteService.generarComprobanteOffline(
                ventaId = venta.id!!
                // Usando configuración por defecto desde .env
            )
            
            println("✓ Facturación offline exitosa")
            printService.imprimirTicketComprobante(comprobanteId)
            
        } catch (e2: Exception) {
            println("✗ Ambas facturaciones fallaron: ${e2.message}")
        }
    }
}
```

### 8. Uso de Parámetros Opcionales vs Específicos

```kotlin
// Ejemplo 1: Usando valores por defecto (más común)
val comprobanteId1 = comprobanteService.generarComprobanteOnline(
    ventaId = Id(123)
    // tipoComprobante = null -> usa TIPO_COMPROBANTE_DEFAULT desde .env
    // puntoVenta = null -> usa PUNTO_DE_VENTA desde .env
)

// Ejemplo 2: Especificando tipo de comprobante pero usando punto de venta por defecto
val comprobanteId2 = comprobanteService.generarComprobanteOnline(
    ventaId = Id(124),
    tipoComprobante = "FACTURA_A"  // Para clientes con CUIT
    // puntoVenta = null -> usa PUNTO_DE_VENTA desde .env
)

// Ejemplo 3: Especificando ambos parámetros
val comprobanteId3 = comprobanteService.generarComprobanteOnline(
    ventaId = Id(125),
    tipoComprobante = "NOTA_CREDITO_B",
    puntoVenta = 2  // Punto de venta específico
)

// Ejemplo 4: Solo especificando punto de venta
val comprobanteId4 = comprobanteService.generarComprobanteOffline(
    ventaId = Id(126),
    puntoVenta = 3
    // tipoComprobante = null -> usa TIPO_COMPROBANTE_DEFAULT desde .env
)
```

### 9. Consulta de Ventas Realizadas

```kotlin
// Consultar una venta específica por ID
val venta = saleService.findSaleById(Id(456))
if (venta != null) {
    println("Venta: ${venta.numeroVenta}")
    println("Cliente: ${venta.cliente?.nombre ?: "Sin cliente"}")
    println("Total: $${venta.montoTotal}")
    println("Comprobante emitido: ${venta.comprobanteEmitido}")

    venta.items.forEach { item ->
        println("  - Producto ${item.productoCodigo}: ${item.cantidad} x $${item.precioUnitario}")
    }
}

// Buscar venta por número
val ventaPorNumero = saleService.findSaleByNumero("V-12345678")

// Consultar ventas de un período
val ventasDelMes = saleService.findSalesByDateRange(
    fechaDesde = "2024-01-01 00:00:00",
    fechaHasta = "2024-01-31 23:59:59"
)

println("Ventas del mes: ${ventasDelMes.size}")
val totalMes = ventasDelMes.sumOf { it.montoTotal }
println("Total facturado: $$totalMes")

// Consultar ventas por vendedor
val ventasVendedor = saleService.findSalesByUsuario("vendedor1")
println("Ventas de vendedor1: ${ventasVendedor.size}")

// Análisis de rendimiento por vendedor
val ventasPorVendedor = ventasDelMes.groupBy { it.usuario.username }
ventasPorVendedor.forEach { (vendedor, ventas) ->
    val totalVendedor = ventas.sumOf { it.montoTotal }
    println("$vendedor: ${ventas.size} ventas, $$totalVendedor")
}
```

### 10. Estadísticas y Monitoreo

```kotlin
// Obtener estadísticas de ventas sin comprobante
val stats = comprobanteController.obtenerEstadisticasVentasSinComprobante()
println("Ventas pendientes de facturar: ${stats.totalVentasSinComprobante}")

if (stats.totalVentasSinComprobante > 0) {
    // Obtener detalles de las primeras 10
    val ventasPendientes = comprobanteService.obtenerVentasSinComprobante(10)
    
    println("\nPrimeras 10 ventas pendientes:")
    ventasPendientes.forEach { venta ->
        println("${venta.numeroVenta} - ${venta.fechaVenta} - ${venta.usuarioNombre} - $${venta.montoTotal}")
    }
}
```

## Endpoints REST Disponibles

### ComprobanteController
```http
POST /api/comprobantes/online
{
  "ventaId": 123,
  "tipoComprobante": "FACTURA_B",  // Opcional
  "puntoVenta": 1                  // Opcional
}

POST /api/comprobantes/offline
{
  "ventaId": 123,
  "tipoComprobante": "FACTURA_B",  // Opcional
  "puntoVenta": 1                  // Opcional
}

# Ejemplo usando solo parámetros requeridos (valores por defecto)
POST /api/comprobantes/online
{
  "ventaId": 123
}

GET /api/comprobantes/venta/123
GET /api/comprobantes/1/12345
GET /api/ventas-sin-comprobante?limit=50
GET /api/estadisticas/ventas-sin-comprobante
```

### PrintController
```http
POST /api/print/ticket-venta/123
POST /api/print/ticket-comprobante/456
POST /api/print/comprobante-fiscal/456

POST /api/print/buscar-ventas
{
  "numeroVenta": "V-12345678",
  "fechaDesde": "2024-01-01 00:00:00",
  "fechaHasta": "2024-01-31 23:59:59",
  "usuario": "vendedor1"
}

POST /api/print/buscar-comprobantes
{
  "puntoVenta": 1,
  "numeroComprobante": 12345
}
```

## Beneficios Demostrados

1. **Flexibilidad**: Facturación independiente de la venta
2. **Recuperación**: Procesar ventas que fallaron inicialmente
3. **Reimpresión**: Fácil acceso a documentos históricos
4. **Monitoreo**: Visibilidad de ventas pendientes
5. **Escalabilidad**: Procesamiento masivo de documentos
