# Solución: Ventas Canceladas No Aparecen en GET /api/sales

## 🔍 Problema Identificado

**Síntoma**: Las ventas aparecen como canceladas en la base de datos, pero NO aparecen como canceladas en la respuesta del endpoint `GET /api/sales`.

**Causa**: El filtro por defecto está **EXCLUYENDO** las ventas canceladas de las consultas normales.

## ✅ Solución

### Para Ver Ventas Canceladas:

```http
GET /api/sales?incluirCanceladas=true
```

### Comportamiento Actual (Por Diseño):

| Endpoint | Comportamiento |
|----------|----------------|
| `GET /api/sales` | ❌ **Excluye** ventas canceladas (por defecto) |
| `GET /api/sales?incluirCanceladas=false` | ❌ **Excluye** ventas canceladas (explícito) |
| `GET /api/sales?incluirCanceladas=true` | ✅ **Incluye** ventas canceladas |

## 📋 Explicación Técnica

### 1. Filtro por Defecto
```kotlin
// En SaleFilterRequest.kt
data class SaleFilterRequest(
    // ... otros campos ...
    val incluirCanceladas: Boolean = false  // ← Por defecto FALSE
)
```

### 2. Lógica del Repositorio
```kotlin
// En JooqSaleRepository.kt
if (!criteria.incluirCanceladas) {
    conditions.add(VENTAS.CANCELADA.eq(false))  // ← Excluye canceladas
}
```

### 3. Parsing de Query Parameters
```kotlin
// En SaleRoutes.kt
val incluirCanceladas = call.request.queryParameters["incluirCanceladas"]?.toBooleanStrictOrNull() ?: false
//                                                                                                    ↑
//                                                                                            Por defecto FALSE
```

## 🎯 Casos de Uso

### Caso 1: Reportes Normales (Excluir Canceladas)
```http
GET /api/sales?fechaDesde=2025-01-15 00:00:00&fechaHasta=2025-01-15 23:59:59
```
**Resultado**: Solo ventas válidas (no canceladas)
**Uso**: Reportes de ventas, facturación, estadísticas comerciales

### Caso 2: Auditoría Completa (Incluir Canceladas)
```http
GET /api/sales?fechaDesde=2025-01-15 00:00:00&fechaHasta=2025-01-15 23:59:59&incluirCanceladas=true
```
**Resultado**: Todas las operaciones (válidas + canceladas)
**Uso**: Auditorías, control de gestión, análisis de errores

### Caso 3: Solo Ventas Canceladas
```http
GET /api/sales/cancelled?limit=50
```
**Resultado**: Solo ventas canceladas con detalles de cancelación
**Uso**: Revisión de cancelaciones, análisis de patrones

## 📊 Endpoints Específicos para Cancelaciones

### 1. Historial de Cancelaciones
```http
GET /api/sales/cancelled?limit=50
```

**Respuesta**:
```json
{
  "cancelledSales": [
    {
      "ventaId": 456,
      "numeroVenta": "V-87654321",
      "fechaVenta": "14/01/2025 16:45",
      "fechaCancelacion": "15/01/2025 09:15",
      "usuarioVenta": "María García",
      "usuarioCancelacion": "supervisor",
      "motivoCancelacion": "Cliente solicitó devolución",
      "montoTotal": "200.00",
      "teniaComprobante": true,
      "notaCreditoGenerada": true,
      "notaCreditoNumero": "0001-00000123"
    }
  ],
  "totalCancelaciones": 5
}
```

### 2. Estadísticas de Cancelaciones
```http
GET /api/sales/cancellation-stats
```

**Respuesta**:
```json
{
  "totalCancelaciones": 15,
  "cancelacionesConNotaCredito": 12,
  "cancelacionesSinNotaCredito": 3
}
```

## 🔧 Verificación en Base de Datos

### Consulta SQL para Verificar Ventas Canceladas:
```sql
SELECT 
    id,
    numero_venta,
    cancelada,
    fecha_cancelacion,
    usuario_cancelacion,
    motivo_cancelacion
FROM ventas 
WHERE cancelada = true
ORDER BY fecha_cancelacion DESC;
```

### Consulta SQL para Ver Todas las Ventas:
```sql
SELECT 
    id,
    numero_venta,
    cancelada,
    fecha_venta,
    monto_total
FROM ventas 
ORDER BY fecha_venta DESC
LIMIT 20;
```

## ⚠️ Consideraciones Importantes

### 1. Diseño Intencional
- **Las ventas canceladas se excluyen por defecto** para evitar confusión en reportes normales
- **Los usuarios deben solicitar explícitamente** ver ventas canceladas
- **Esto mejora la experiencia del usuario** en consultas cotidianas

### 2. Casos de Error Comunes
```http
# ❌ INCORRECTO - No verás ventas canceladas
GET /api/sales

# ✅ CORRECTO - Para ver ventas canceladas
GET /api/sales?incluirCanceladas=true
```

### 3. Filtros Combinados
```http
# Ver todas las ventas de un usuario (incluyendo canceladas)
GET /api/sales?usuarios=vendedor1&incluirCanceladas=true

# Ver solo ventas válidas de un período
GET /api/sales?fechaDesde=2025-01-01 00:00:00&fechaHasta=2025-01-31 23:59:59

# Ver todas las operaciones de un período (incluyendo canceladas)
GET /api/sales?fechaDesde=2025-01-01 00:00:00&fechaHasta=2025-01-31 23:59:59&incluirCanceladas=true
```

## 🚀 Resumen de la Solución

1. **Para ver ventas canceladas**: Usar `?incluirCanceladas=true`
2. **Para reportes normales**: Usar el endpoint sin parámetros adicionales
3. **Para auditorías**: Usar `?incluirCanceladas=true`
4. **Para solo canceladas**: Usar `/api/sales/cancelled`

El comportamiento actual es **correcto por diseño** y sigue las mejores prácticas para sistemas de ventas donde las operaciones canceladas deben tratarse por separado de las operaciones válidas.
