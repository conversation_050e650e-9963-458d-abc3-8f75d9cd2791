# API de Búsqueda de Ventas con Filtros y Paginación

## Endpoint Principal

### GET `/api/sales`
Busca ventas con filtros avanzados y paginación.

## Parámetros de Query

### Filtros de Fecha
- `fechaDesde` (opcional): Fecha desde en formato `yyyy-MM-dd HH:mm:ss`
- `fechaHasta` (opcional): Fecha hasta en formato `yyyy-MM-dd HH:mm:ss`

### Filtros de Usuario
- `usuarios` (opcional): Lista de usernames. Se puede repetir el parámetro para múltiples usuarios
  - Ejemplo: `?usuarios=vendedor1&usuarios=vendedor2`

### Filtros de Comprobante
- `comprobanteEmitido` (opcional): `true`, `false` o no especificar (todos)

### Filtros de Medio de Pago
- `mediosPago` (opcional): Lista de medios de pago. Se puede repetir el parámetro
  - Valores válidos: `EFECTIVO`, `TARJETA_DEBITO`, `TARJETA_CREDITO`, `QR`, `TRANSFERENCIA`
  - Ejemplo: `?mediosPago=EFECTIVO&mediosPago=TARJETA_CREDITO`

### Paginación
- `page` (opcional): Número de página (empezando en 1). Por defecto: 1
- `size` (opcional): Tamaño de página (1-100). Por defecto: 20

## Ejemplos de Uso

### 1. Ventas del día actual
```http
GET /api/sales?fechaDesde=2024-01-15 00:00:00&fechaHasta=2024-01-15 23:59:59
```

### 2. Ventas de un usuario específico
```http
GET /api/sales?usuarios=vendedor1
```

### 3. Ventas sin comprobante emitido
```http
GET /api/sales?comprobanteEmitido=false
```

### 4. Ventas por medio de pago
```http
GET /api/sales?mediosPago=EFECTIVO&mediosPago=TARJETA_CREDITO
```

### 5. Búsqueda compleja con paginación
```http
GET /api/sales?fechaDesde=2024-01-01 00:00:00&fechaHasta=2024-01-31 23:59:59&usuarios=vendedor1&usuarios=vendedor2&comprobanteEmitido=true&mediosPago=EFECTIVO&page=2&size=50
```

### 6. Todas las ventas (con paginación por defecto)
```http
GET /api/sales
```

## Respuesta

### Estructura de Respuesta
```json
{
  "content": [
    {
      "id": 456,
      "numeroVenta": "V-12345678",
      "fechaVenta": "15/01/2024 14:30",
      "clienteId": 123,
      "clienteNombre": "Cliente Ejemplo",
      "usuarioUsername": "vendedor1",
      "usuarioNombre": "Vendedor 1",
      "montoTotal": "1,250.00",
      "medioPago": "EFECTIVO",
      "comprobanteEmitido": true,
      "codigoTicketBalanza": "TKT001",
      "idTicketBalanza": "ID001",
      "items": [
        {
          "productoCodigo": "1001",
          "cantidad": 2.0,
          "precioUnitario": "625.00",
          "subtotal": "1,250.00",
          "tipoIvaId": 5
        }
      ]
    }
  ],
  "page": 1,
  "size": 20,
  "totalElements": 150,
  "totalPages": 8,
  "hasNext": true,
  "hasPrevious": false
}
```

### Campos de Metadatos de Paginación
- `content`: Array de ventas en la página actual
- `page`: Número de página actual
- `size`: Tamaño de página utilizado
- `totalElements`: Total de elementos que coinciden con los filtros
- `totalPages`: Total de páginas disponibles
- `hasNext`: Indica si hay una página siguiente
- `hasPrevious`: Indica si hay una página anterior

## Códigos de Respuesta

### 200 OK
Búsqueda exitosa (puede retornar array vacío si no hay resultados)

### 400 Bad Request
- Parámetros inválidos
- Formato de fecha incorrecto
- Página menor a 1
- Tamaño de página fuera del rango 1-100
- Rango de fechas inválido (fechaDesde posterior a fechaHasta)

### 500 Internal Server Error
Error interno del servidor

## Casos de Uso Comunes

### Ventas del Día
Para obtener las ventas del día actual:
```http
GET /api/sales?fechaDesde=2024-01-15 00:00:00&fechaHasta=2024-01-15 23:59:59
```

### Ventas Pendientes de Facturación
Para obtener ventas sin comprobante emitido:
```http
GET /api/sales?comprobanteEmitido=false&size=100
```

### Reporte por Vendedor
Para obtener ventas de vendedores específicos en un período:
```http
GET /api/sales?fechaDesde=2024-01-01 00:00:00&fechaHasta=2024-01-31 23:59:59&usuarios=vendedor1&usuarios=vendedor2
```

### Análisis por Medio de Pago
Para analizar ventas por medios de pago específicos:
```http
GET /api/sales?mediosPago=TARJETA_CREDITO&mediosPago=TARJETA_DEBITO&fechaDesde=2024-01-01 00:00:00&fechaHasta=2024-01-31 23:59:59
```

## Notas Importantes

1. **Formato de Fechas**: Siempre usar el formato `yyyy-MM-dd HH:mm:ss`
2. **Paginación**: El sistema limita el tamaño máximo de página a 100 elementos
3. **Filtros Múltiples**: Se pueden combinar todos los filtros
4. **Rendimiento**: Para consultas con muchos resultados, usar paginación apropiada
5. **Ordenamiento**: Los resultados se ordenan por fecha de venta descendente (más recientes primero)

## Migración desde Endpoints Anteriores

### Reemplazo de POST `/api/sales/buscar-por-fecha`
**Antes:**
```http
POST /api/sales/buscar-por-fecha
{
  "fechaDesde": "2024-01-01 00:00:00",
  "fechaHasta": "2024-01-31 23:59:59"
}
```

**Ahora:**
```http
GET /api/sales?fechaDesde=2024-01-01 00:00:00&fechaHasta=2024-01-31 23:59:59
```

### Reemplazo de GET `/api/sales/usuario/{username}`
**Antes:**
```http
GET /api/sales/usuario/vendedor1
```

**Ahora:**
```http
GET /api/sales?usuarios=vendedor1
```
