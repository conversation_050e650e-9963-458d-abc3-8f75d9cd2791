# Implementación de Seguimiento de Notas de Crédito

## 📋 Resumen

Se ha implementado un sistema para rastrear si se ha generado exitosamente la nota de crédito para ventas canceladas que tenían comprobante emitido. Esto permite identificar ventas canceladas que tienen pendiente la generación de su nota de crédito debido a fallos en el proceso.

## 🗄️ Cambios en Base de Datos

### Migración V22: Campo `nota_credito_generada`

**Archivo**: `src/main/resources/db/migration/V22__add_nota_credito_generada_field.sql`

- **Nuevo campo**: `nota_credito_generada BOOLEAN NOT NULL DEFAULT FALSE`
- **Constraint de consistencia**: Solo ventas canceladas con comprobante pueden tener nota de crédito generada
- **Índice optimizado**: Para consultas de ventas pendientes de nota de crédito
- **Migración de datos**: Actualiza automáticamente ventas existentes que ya tienen nota de crédito

```sql
-- Constraint para asegurar consistencia lógica
ALTER TABLE ventas ADD CONSTRAINT chk_nota_credito_consistente 
    CHECK (
        (nota_credito_generada = FALSE) OR
        (nota_credito_generada = TRUE AND cancelada = TRUE AND comprobante_emitido = TRUE)
    );
```

## 🏗️ Cambios en Modelo de Dominio

### Clase `Sale`

**Archivo**: `src/main/kotlin/com/gnico/majo/application/domain/model/Sale.kt`

#### Nuevos campos y métodos:
- **Campo**: `notaCreditoGenerada: Boolean = false`
- **Método**: `markNotaCreditoGenerada()` - Marca que se generó la nota de crédito
- **Método**: `needsNotaCredito()` - Verifica si necesita nota de crédito
- **Actualización**: Todos los factory methods incluyen el nuevo campo

```kotlin
/**
 * Verifica si esta venta cancelada necesita nota de crédito
 */
fun needsNotaCredito(): Boolean {
    return cancelada && comprobanteEmitido && !notaCreditoGenerada
}
```

## 🗃️ Cambios en Repositorio

### Interfaz `SaleRepositoryPort`

**Archivo**: `src/main/kotlin/com/gnico/majo/application/port/out/SalePort.kt`

#### Nuevos métodos:
- `markNotaCreditoGenerada(ventaId: Id): Boolean`
- `findSalesPendingNotaCredito(limit: Int): List<Sale>`
- `countSalesPendingNotaCredito(): Int`

### Implementación `JooqSaleRepository`

**Archivo**: `src/main/kotlin/com/gnico/majo/adapter/persistence/JooqSaleRepository.kt`

#### Cambios realizados:
- **Todas las consultas SELECT** actualizadas para incluir `VENTAS.NOTA_CREDITO_GENERADA`
- **Todas las llamadas** a `Sale.fromPersistence` incluyen el nuevo campo
- **Método `saveSale`** actualizado para persistir el campo
- **Nuevos métodos** implementados para gestión de notas de crédito pendientes

## 🔧 Cambios en Servicios

### `SaleCancellationServiceImpl`

**Archivo**: `src/main/kotlin/com/gnico/majo/application/usecase/SaleCancellationServiceImpl.kt`

#### Funcionalidad agregada:
- **Marcado automático**: Cuando se genera exitosamente una nota de crédito, se marca el campo en la BD
- **Nuevos métodos**: `getSalesPendingNotaCredito()` y `countSalesPendingNotaCredito()`
- **Manejo de errores**: Si falla la generación de nota de crédito, el campo permanece en `false`

```kotlin
// Marcar en la base de datos que se generó la nota de crédito
val marcadoExitoso = saleRepository.markNotaCreditoGenerada(ventaId)
if (!marcadoExitoso) {
    println("⚠️ Advertencia: No se pudo marcar nota_credito_generada para venta ${ventaId.value}")
}
```

### Interfaz `SaleCancellationService`

**Archivo**: `src/main/kotlin/com/gnico/majo/application/port/in/SaleCancellationService.kt`

#### Nuevos métodos:
- `getSalesPendingNotaCredito(limit: Int): List<CancelledSaleInfo>`
- `countSalesPendingNotaCredito(): Int`

## 🌐 Cambios en API

### Controlador `SaleCancellationController`

**Archivo**: `src/main/kotlin/com/gnico/majo/adapter/controller/rest/SaleCancellationController.kt`

#### Nuevo método:
- `getSalesPendingNotaCredito(limit: Int): CancelledSalesHistoryResponse`

### Rutas

**Archivo**: `src/main/kotlin/com/gnico/majo/infrastructure/routes/SaleRoutes.kt`

#### Nuevo endpoint:
```
GET /api/sales/pending-nota-credito?limit=100
```

Retorna ventas canceladas que tienen comprobante emitido pero no se ha generado la nota de crédito.

### DTOs

**Archivo**: `src/main/kotlin/com/gnico/majo/adapter/controller/dto/SaleDto.kt`

#### Campo agregado:
- `SaleResponse.notaCreditoGenerada: Boolean`

## 📊 Casos de Uso

### 1. Consultar Ventas Pendientes de Nota de Crédito

```http
GET /api/sales/pending-nota-credito?limit=50
```

**Respuesta**:
```json
{
  "cancelledSales": [
    {
      "ventaId": 123,
      "numeroVenta": "V-12345678",
      "fechaVenta": "15/07/2024 10:30",
      "fechaCancelacion": "15/07/2024 14:20",
      "usuarioVenta": "Juan Pérez",
      "usuarioCancelacion": "admin",
      "motivoCancelacion": "Error en facturación",
      "montoTotal": "1,250.00",
      "teniaComprobante": true,
      "notaCreditoGenerada": false,
      "notaCreditoNumero": null
    }
  ],
  "totalCancelaciones": 5
}
```

### 2. Proceso de Cancelación Mejorado

1. **Cancelar venta** → `cancelada = true`
2. **Generar nota de crédito** → Si es exitosa: `nota_credito_generada = true`
3. **Si falla la nota de crédito** → `nota_credito_generada = false` (permite reintento)

### 3. Consultas de Estadísticas

Las estadísticas de cancelación ahora pueden distinguir entre:
- Ventas canceladas sin comprobante (no necesitan nota de crédito)
- Ventas canceladas con comprobante y nota de crédito generada
- Ventas canceladas con comprobante pero sin nota de crédito (pendientes)

## 🔍 Consultas SQL Útiles

### Ventas pendientes de nota de crédito:
```sql
SELECT * FROM ventas 
WHERE cancelada = true 
  AND comprobante_emitido = true 
  AND nota_credito_generada = false
ORDER BY fecha_cancelacion DESC;
```

### Estadísticas de notas de crédito:
```sql
SELECT 
  COUNT(*) as total_canceladas,
  COUNT(CASE WHEN comprobante_emitido = true THEN 1 END) as con_comprobante,
  COUNT(CASE WHEN nota_credito_generada = true THEN 1 END) as con_nota_credito,
  COUNT(CASE WHEN comprobante_emitido = true AND nota_credito_generada = false THEN 1 END) as pendientes
FROM ventas 
WHERE cancelada = true;
```

## ✅ Beneficios

1. **Trazabilidad completa**: Se puede rastrear el estado de generación de notas de crédito
2. **Identificación de fallos**: Fácil detección de ventas que necesitan reintento de nota de crédito
3. **Reportes precisos**: Estadísticas detalladas sobre el proceso de cancelación
4. **Integridad de datos**: Constraints que aseguran consistencia lógica
5. **Performance optimizada**: Índices específicos para consultas frecuentes

## 🚀 Próximos Pasos

1. **Reintento automático**: Implementar proceso para reintentar generación de notas de crédito fallidas
2. **Alertas**: Notificaciones cuando hay muchas ventas pendientes de nota de crédito
3. **Dashboard**: Panel de control para monitorear el estado de las cancelaciones
4. **Auditoría**: Log detallado de intentos de generación de notas de crédito
