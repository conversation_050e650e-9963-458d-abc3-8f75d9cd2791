# Funcionalidad de Cancelación de Ventas

## Descripción General

El sistema ahora permite cancelar ventas realizadas, manteniendo un registro completo de la operación. Cuando una venta que tiene un comprobante emitido es cancelada, se genera automáticamente una **Nota de Crédito B** por el total de la venta.

## Características Principales

### ✅ Cancelación de Ventas
- Cancelación de ventas con registro de usuario, fecha y motivo
- Validación para evitar cancelar ventas ya canceladas
- Generación automática de Nota de Crédito B si la venta tenía comprobante

### ✅ Notas de Crédito Desfasadas
- Las notas de crédito B pueden generarse tanto **online (CAE)** como **offline (CAEA)**
- Soporte para generación desfasada similar a las facturas B existentes
- Integración completa con el sistema AFIP

### ✅ Auditoría Completa
- Registro de fecha y hora de cancelación
- Usuario que realizó la cancelación
- Motivo de la cancelación
- Historial de ventas canceladas
- Estadísticas de cancelaciones

## Endpoints de API

### Cancelar una Venta
```http
POST /api/sales/{id}/cancel
Content-Type: application/json

{
  "usuarioCancelacion": "admin",
  "motivo": "Error en la venta - producto incorrecto",
  "generarNotaCreditoOnline": true
}
```

**Respuesta:**
```json
{
  "success": true,
  "message": "Venta cancelada y nota de crédito B generada exitosamente",
  "ventaId": 123,
  "numeroVenta": "V-12345678",
  "notaCreditoGenerada": true,
  "notaCreditoId": 456,
  "notaCreditoNumero": "0001-00000123"
}
```

### Obtener Historial de Cancelaciones
```http
GET /api/sales/cancelled?limit=50
```

### Obtener Estadísticas de Cancelaciones
```http
GET /api/sales/cancellation-stats
```

## Estructura de Base de Datos

### Nuevos Campos en Tabla `ventas`
```sql
ALTER TABLE ventas ADD COLUMN cancelada BOOLEAN NOT NULL DEFAULT FALSE;
ALTER TABLE ventas ADD COLUMN fecha_cancelacion TIMESTAMP NULL;
ALTER TABLE ventas ADD COLUMN usuario_cancelacion VARCHAR(50) NULL;
ALTER TABLE ventas ADD COLUMN motivo_cancelacion TEXT NULL;
```

### Constraints de Integridad
- Constraint para asegurar consistencia de datos de cancelación
- Foreign key hacia tabla `usuarios` para `usuario_cancelacion`
- Índices para optimizar consultas de ventas canceladas

## Reglas de Negocio

### Validaciones
1. **Solo se pueden cancelar ventas no canceladas previamente**
2. **El usuario que cancela debe existir en el sistema**
3. **El motivo de cancelación es obligatorio**

### Generación de Nota de Crédito
1. **Si la venta tenía comprobante emitido** → Se genera automáticamente Nota de Crédito B
2. **Si la venta no tenía comprobante** → Solo se marca como cancelada
3. **Tipo de generación**: Online (CAE) u Offline (CAEA) según parámetro

### Filtros en Consultas
- **Por defecto**: Las ventas canceladas se excluyen de las consultas
- **Parámetro `incluirCanceladas=true`**: Incluye ventas canceladas en resultados
- **Endpoint específico**: `/api/sales/cancelled` para obtener solo canceladas

## Casos de Uso

### Caso 1: Cancelación Simple (Sin Comprobante)
```
Venta sin comprobante → Cancelación → Solo se marca como cancelada
```

### Caso 2: Cancelación con Nota de Crédito (Con Comprobante)
```
Venta con comprobante → Cancelación → Nota de Crédito B automática
```

### Caso 3: Generación Desfasada de Nota de Crédito
```
Cancelación → Nota de Crédito offline (CAEA) → Posterior envío a AFIP
```

## Integración con Sistema Existente

### Servicios Modificados
- `SaleService`: Nuevo método `cancelSale()`
- `ComprobanteService`: Soporte para NOTA_CREDITO_B desfasada
- `SaleRepository`: Métodos de cancelación y consulta

### Nuevos Servicios
- `SaleCancellationService`: Lógica específica de cancelación
- `SaleCancellationController`: Endpoints de cancelación

### DTOs Actualizados
- `SaleResponse`: Incluye campos de cancelación
- `SaleFilterRequest`: Filtro `incluirCanceladas`
- Nuevos DTOs específicos para cancelación

## Consideraciones Técnicas

### Transaccionalidad
- Cancelación y generación de nota de crédito en transacción única
- Rollback automático si falla la generación de nota de crédito

### Manejo de Errores
- Si falla la nota de crédito, la cancelación se mantiene con error reportado
- Logs detallados para auditoría y debugging

### Performance
- Índices optimizados para consultas de ventas canceladas
- Filtro por defecto para excluir canceladas mejora performance

## Migración

La migración `V21__add_sale_cancellation_fields.sql` agrega:
- Campos de cancelación a tabla existente
- Constraints de integridad
- Índices para performance
- Valores por defecto para registros existentes

## Testing

Se recomienda probar:
1. Cancelación de venta sin comprobante
2. Cancelación de venta con comprobante (online y offline)
3. Validaciones de negocio (venta ya cancelada, usuario inexistente)
4. Filtros en consultas (incluir/excluir canceladas)
5. Generación desfasada de notas de crédito
