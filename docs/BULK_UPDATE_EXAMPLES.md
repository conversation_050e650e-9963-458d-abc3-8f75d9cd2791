# Ejemplos Prácticos de Bulk Update con Valores Especiales

## Casos de Uso Comunes

### Caso 1: Remover categorías de productos descontinuados

**Situación:** Tienes productos que pertenecían a una categoría que ya no usas y quieres remover la categoría de todos esos productos.

**Antes (no era posible):**
```http
PUT /api/productos/bulk
{
  "codigos": [1001, 1002, 1003],
  "categoriaId": null  // ❌ No actualiza nada, mantiene categoría actual
}
```

**Ahora (funciona):**
```http
PUT /api/productos/bulk
{
  "codigos": [1001, 1002, 1003],
  "categoriaId": 0  // ✅ Limpia la categoría (categoria = null)
}
```

### Caso 2: Limpiar stock de productos sin inventario definido

**Situación:** Algunos productos no tienen control de stock y quieres limpiar el campo `stockActual`.

```http
PUT /api/productos/bulk
{
  "codigos": [2001, 2002, 2003],
  "stockActual": -1  // ✅ Limpia el stock (stockActual = null)
}
```

### Caso 3: Reorganización masiva - limpiar categoría y stock

**Situación:** Estás reorganizando tu inventario y necesitas limpiar tanto categorías como stock de ciertos productos.

```http
PUT /api/productos/bulk
{
  "codigos": [3001, 3002, 3003, 3004],
  "categoriaId": 0,     // Limpiar categoría
  "stockActual": -1,    // Limpiar stock
  "precioUnitario": 0   // Establecer precio en 0
}
```

### Caso 4: Migración de categorías

**Situación:** Quieres mover productos de una categoría antigua a una nueva, pero primero limpiar la categoría de algunos productos problemáticos.

**Paso 1 - Limpiar categorías problemáticas:**
```http
PUT /api/productos/bulk
{
  "codigos": [4001, 4002],
  "categoriaId": -5  // Cualquier valor <= 0 limpia la categoría
}
```

**Paso 2 - Asignar nueva categoría a productos válidos:**
```http
PUT /api/productos/bulk
{
  "codigos": [4003, 4004, 4005],
  "categoriaId": 10  // Asignar nueva categoría
}
```

## Comparación de Comportamientos

| Valor enviado | Resultado en BD | Descripción |
|---------------|-----------------|-------------|
| `"categoriaId": null` | Sin cambios | No actualiza el campo |
| `"categoriaId": 0` | `categoria = null` | **NUEVO:** Limpia la categoría |
| `"categoriaId": -1` | `categoria = null` | **NUEVO:** Limpia la categoría |
| `"categoriaId": 5` | `categoria = 5` | Asigna categoría ID 5 |
| `"stockActual": null` | Sin cambios | No actualiza el campo |
| `"stockActual": -1` | `stockActual = null` | **NUEVO:** Limpia el stock |
| `"stockActual": 0` | `stockActual = 0` | Stock en cero (válido) |
| `"stockActual": 50` | `stockActual = 50` | Stock en 50 unidades |

## Respuestas de la API

### Éxito al limpiar campos
```json
{
  "actualizados": 3,
  "mensaje": "Se actualizaron 3 producto(s) correctamente"
}
```

### Error - No se proporcionaron campos
```json
{
  "actualizados": 0,
  "mensaje": "No se proporcionaron campos para actualizar"
}
```

### Error - Lista vacía
```json
{
  "actualizados": 0,
  "mensaje": "No se proporcionaron códigos para actualizar"
}
```

## Casos Edge

### Limpiar solo categoría, mantener otros campos
```http
PUT /api/productos/bulk
{
  "codigos": [5001],
  "categoriaId": 0
  // precioUnitario, stockActual, unidadMedidaId no se tocan
}
```

### Limpiar solo stock, mantener otros campos
```http
PUT /api/productos/bulk
{
  "codigos": [5002],
  "stockActual": -999  // Cualquier valor negativo limpia
}
```

### Actualización mixta
```http
PUT /api/productos/bulk
{
  "codigos": [6001, 6002, 6003],
  "categoriaId": 0,           // Limpiar categoría
  "precioUnitario": 99.99,    // Actualizar precio
  "unidadMedidaId": 2,        // Cambiar unidad de medida
  "stockActual": -1           // Limpiar stock
}
```

## Verificación de Resultados

Después de ejecutar un bulk update con valores especiales, puedes verificar los resultados consultando los productos:

```http
GET /api/productos/1001
```

**Respuesta esperada después de limpiar categoría y stock:**
```json
{
  "codigo": 1001,
  "nombre": "Producto Ejemplo",
  "descripcion": "Descripción del producto",
  "categoria": null,        // ✅ Categoría limpiada
  "precioUnitario": 99.99,
  "stockActual": null,      // ✅ Stock limpiado
  "unidadMedidaId": 1,
  "activo": true
}
```

## Notas Importantes

1. **Compatibilidad:** Esta funcionalidad es completamente compatible con el comportamiento anterior
2. **Valores especiales:** Solo afectan a `categoriaId` y `stockActual`
3. **Flexibilidad:** Puedes usar cualquier valor `<= 0` para categoría y `< 0` para stock
4. **Transaccional:** Todas las actualizaciones se realizan en una sola transacción
5. **Validación:** El sistema valida que al menos un campo esté presente para actualizar
