# Impresión de Reportes de Ventas

Esta funcionalidad permite imprimir reportes de ventas directamente en la impresora de tickets térmicos.

## Endpoint

```
POST /api/print/report
```

## Parámetros de Query

| Parámetro | Tipo | Requerido | Descripción | Valores |
|-----------|------|-----------|-------------|---------|
| `periodo` | String | No | Período predefinido del reporte | `today`, `yesterday`, `current_week`, `current_month`, `last_month` |
| `fechaDesde` | String | Condicional | Fecha de inicio (requerido si periodo=custom) | ISO format: `yyyy-MM-ddTHH:mm:ss` |
| `fechaHasta` | String | Condicional | Fecha de fin (requerido si periodo=custom) | ISO format: `yyyy-MM-ddTHH:mm:ss` |
| `usuarios` | Array[String] | No | Lista de usuarios a filtrar | Usernames |
| `mediosPago` | Array[String] | No | Lista de medios de pago a filtrar | `EFECTIVO`, `TARJETA_DEBITO`, etc. |
| `incluirCanceladas` | Boolean | No | Si incluir ventas canceladas | `true`, `false` (default: `false`) |
| `soloConComprobante` | Boolean | No | Solo ventas con comprobante | `true`, `false` |
| `soloMediosPagoElectronicos` | Boolean | No | Solo medios electrónicos | `true`, `false` (default: `false`) |

## Ejemplos de Uso

### 1. Reporte del día actual
```bash
curl -X POST "http://localhost:8080/api/print/report?periodo=today"
```

### 2. Reporte de ayer
```bash
curl -X POST "http://localhost:8080/api/print/report?periodo=yesterday"
```

### 3. Reporte de la semana actual
```bash
curl -X POST "http://localhost:8080/api/print/report?periodo=current_week"
```

### 4. Reporte del mes actual
```bash
curl -X POST "http://localhost:8080/api/print/report?periodo=current_month"
```

### 5. Reporte del mes pasado
```bash
curl -X POST "http://localhost:8080/api/print/report?periodo=last_month"
```

### 6. Reporte personalizado por fechas
```bash
curl -X POST "http://localhost:8080/api/print/report?fechaDesde=2025-01-01T00:00:00&fechaHasta=2025-01-31T23:59:59"
```

### 7. Reporte con filtros específicos
```bash
curl -X POST "http://localhost:8080/api/print/report?periodo=today&usuarios=admin&usuarios=vendedor1&mediosPago=EFECTIVO&incluirCanceladas=false"
```

### 8. Reporte solo de medios electrónicos
```bash
curl -X POST "http://localhost:8080/api/print/report?periodo=today&soloMediosPagoElectronicos=true"
```

## Respuesta

### Éxito (200 OK)
```json
{
  "success": true,
  "message": "Reporte de ventas impreso exitosamente"
}
```

### Error de parámetros (400 Bad Request)
```json
{
  "success": false,
  "message": "Error en parámetros del reporte",
  "error": "Para período personalizado se requieren fechaDesde y fechaHasta"
}
```

### Error interno (500 Internal Server Error)
```json
{
  "success": false,
  "message": "Error al imprimir reporte de ventas",
  "error": "Error de conexión con la impresora"
}
```

## Ejemplos de Formato de Período

Dependiendo del tipo de período seleccionado, el ticket mostrará:

- **TODAY**: `Período: HOY - 27/07/2025`
- **YESTERDAY**: `Período: AYER - 26/07/2025`
- **CURRENT_WEEK**: `Período: ESTA SEMANA - 21/07/2025 a 27/07/2025`
- **CURRENT_MONTH**: `Período: ESTE MES - 01/07/2025 a 27/07/2025`
- **LAST_MONTH**: `Período: MES PASADO - 01/06/2025 a 30/06/2025`
- **Personalizado (un día)**: `Período: PERÍODO - 15/07/2025`
- **Personalizado (rango)**: `Período: PERÍODO - 01/07/2025 a 15/07/2025`

## Contenido del Ticket

El ticket impreso incluye:

### Encabezado
- Nombre de la empresa
- Título "REPORTE DE VENTAS"
- Fecha y hora de generación
- Período del reporte con fechas específicas:
  - Para períodos de un día (HOY, AYER): muestra la fecha específica
  - Para períodos de rango (ESTA SEMANA, ESTE MES): muestra el rango de fechas
  - Para períodos personalizados: muestra fecha única o rango según corresponda

### Resumen General
- Total de ventas (cantidad)
- Ventas canceladas (si las hay)
- **Monto total de ventas** (sin canceladas)
- **Monto total facturado** (facturas - notas de crédito)
- **Monto total de IVA** (IVA facturado - IVA de notas de crédito)
- Ventas con comprobante vs sin comprobante

### Medios de Pago
- **Efectivo**: cantidad, monto y porcentaje
- **Electrónico**: cantidad, monto y porcentaje (agrupa todos los medios no efectivo)

### Pie
- Mensaje de reporte generado automáticamente

## Configuración de Impresora

La funcionalidad utiliza la misma configuración de impresora que los tickets de ventas:

- `PRINTER_ENABLED`: Habilitar/deshabilitar impresión
- `PRINTER_IP`: Dirección IP de la impresora
- `PRINTER_PORT`: Puerto de la impresora (default: 9100)
- `COMPANY_NAME`: Nombre de la empresa para el encabezado

## Notas Técnicas

- **Consistencia de parámetros**: Los parámetros son idénticos a los del endpoint `/api/reports/sales` para garantizar compatibilidad
- La impresión se realiza de forma síncrona
- Si la impresora no está disponible, se retorna un error pero la aplicación continúa funcionando
- El reporte se genera en tiempo real basado en los datos actuales de la base de datos
- Los cálculos de montos facturados consideran las notas de crédito automáticamente
- Los medios de pago se clasifican automáticamente en efectivo vs electrónicos

## Integración con Frontend

Para integrar con un frontend, simplemente realizar una petición POST al endpoint con los parámetros deseados:

```javascript
// Ejemplo con fetch API
const imprimirReporte = async (periodo = 'hoy') => {
  try {
    const response = await fetch(`/api/print/report?periodo=${periodo}`, {
      method: 'POST'
    });
    
    const result = await response.json();
    
    if (result.success) {
      alert('Reporte impreso exitosamente');
    } else {
      alert(`Error: ${result.message}`);
    }
  } catch (error) {
    alert('Error de conexión');
  }
};
```
