# 📊 API de Reportes - Documentación de Uso

Esta documentación describe el endpoint unificado para generar reportes y estadísticas del sistema de ventas.

## 🌐 Base URL
```
http://localhost:8080/api/reports
```

## 🎯 Endpoint Único Simplificado

**La API de reportes ha sido simplificada a un solo endpoint que proporciona toda la información necesaria:**

1. [Reporte de Ventas Completo](#reporte-de-ventas-completo)

---

## Reporte de Ventas Completo

**Endpoint:** `GET /api/reports/sales`

**Descripción:** Obtiene un reporte completo de ventas con todos los desgloses, filtros personalizables y toda la información necesaria para dashboards, alertas, rankings y estadísticas fiscales.

### Parámetros de consulta

| Parámetro | Tipo | Descripción | Valores posibles | Default |
|-----------|------|-------------|------------------|---------|
| `periodo` | string | Período predefinido | `today`, `yesterday`, `current_week`, `current_month`, `last_month` | `today` |
| `fechaDesde` | string | Fecha inicio (ISO) | `2025-07-01T00:00:00` | - |
| `fechaHasta` | string | Fecha fin (ISO) | `2025-07-31T23:59:59` | - |
| `usuarios` | string | Usuarios (separados por coma) | `leidimar,nelson,dario` | todos |
| `mediosPago` | string | Medios de pago (separados por coma) | `EFECTIVO,TARJETA_DEBITO` | todos |
| `soloMediosPagoElectronicos` | boolean | Solo pagos electrónicos | `true`, `false` | `false` |
| `soloConComprobante` | boolean | Filtrar por comprobantes | `true` (solo con comprobante), `false` (todas) | todas |
| `incluirCanceladas` | boolean | Incluir ventas canceladas | `true`, `false` | `false` |

### Respuesta Completa

La respuesta incluye **TODA** la información necesaria para dashboards, alertas, rankings y estadísticas:

```json
{
  "periodo": {
    "desde": "2025-07-20T00:00:00",
    "hasta": "2025-07-20T20:20:46.7057336",
    "descripcion": "Hoy"
  },
  "resumenGeneral": {
    "totalVentas": 4,
    "montoTotal": 64212.0,
    "ticketPromedio": 16053.0,
    "ventasConComprobante": 1,
    "ventasSinComprobante": 3,
    "montoConComprobante": 11712.0,
    "montoSinComprobante": 52500.0,
    "ventasCanceladas": 0,
    "montoCancelado": 0.0,
    "ventasElectronicasPendientes": 1,
    "montoElectronicosPendientes": 9000.0
  },
  "desglosePorMedioPago": [
    {
      "medioPago": "EFECTIVO",
      "descripcion": "Efectivo",
      "cantidadVentas": 2,
      "montoTotal": 43500.0,
      "porcentajeDelTotal": 67.74,
      "ticketPromedio": 21750.0,
      "esElectronico": false
    }
  ],
  "desglosePorVendedor": [
    {
      "username": "leidimar",
      "nombreDisplay": "Leidimar R.",
      "cantidadVentas": 2,
      "montoTotal": 34000.0,
      "ticketPromedio": 17000.0,
      "porcentajeDelTotal": 52.95,
      "ventasConComprobante": 0,
      "ventasSinComprobante": 2
    }
  ],
  "productosTopVentas": [
    {
      "productoNombre": "Croqueta de arroz y queso",
      "cantidadVendida": 1.22,
      "unidadMedida": "un",
      "montoTotal": 11712.0,
      "cantidadTransacciones": 1
    }
  ],
  "estadisticasComprobantes": {
    "totalComprobantes": 1,
    "montoTotalFacturado": 11712.0,
    "montoTotalIva": 2032.66,
    "desglosePorTipo": [
      {
        "tipoComprobante": "FACTURA_B",
        "cantidad": 1,
        "montoTotal": 11712.0,
        "montoIva": 2032.66
      }
    ],
    "notasCredito": {
      "cantidadNotas": 0,
      "montoTotal": 0.0,
      "montoIva": 0.0,
      "ventasPendientesNotaCredito": 0
    }
  },
  "alertas": {
    "ventasSinComprobante": 3,
    "montoSinComprobante": 52500.0,
    "ventasElectronicasSinComprobante": 1,
    "montoElectronicosSinComprobante": 9000.0,
    "ventasCanceladasPendientesNC": 0
  }
}
```

## 🚀 Ejemplos de Uso

### Reporte del día actual (Dashboard)
```bash
curl -X GET "http://localhost:8080/api/reports/sales?periodo=today"
```

### Reporte con filtro de usuarios específicos
```bash
curl -X GET "http://localhost:8080/api/reports/sales?periodo=current_month&usuarios=leidimar,nelson"
```

### Reporte del día anterior completo
```bash
curl -X GET "http://localhost:8080/api/reports/sales?periodo=yesterday"
```

### Reporte del mes anterior completo
```bash
curl -X GET "http://localhost:8080/api/reports/sales?periodo=last_month"
```

### Solo medios de pago electrónicos (para alertas AFIP)
```bash
curl -X GET "http://localhost:8080/api/reports/sales?periodo=today&soloMediosPagoElectronicos=true"
```

### Solo ventas con comprobante (estadísticas fiscales)
```bash
curl -X GET "http://localhost:8080/api/reports/sales?periodo=current_month&soloConComprobante=true"
```

### Todas las ventas (comportamiento por defecto)
```bash
curl -X GET "http://localhost:8080/api/reports/sales?periodo=current_month&soloConComprobante=false"
# Equivalente a no especificar el parámetro:
curl -X GET "http://localhost:8080/api/reports/sales?periodo=current_month"
```

### Rango de fechas personalizado
```bash
curl -X GET "http://localhost:8080/api/reports/sales?fechaDesde=2025-07-01T00:00:00&fechaHasta=2025-07-31T23:59:59"
```

### Filtros combinados
```bash
curl -X GET "http://localhost:8080/api/reports/sales?periodo=current_week&usuarios=leidimar&mediosPago=EFECTIVO,TARJETA_DEBITO&incluirCanceladas=false"
```

---

## 📊 Información Incluida en la Respuesta

El endpoint `/sales` incluye **TODA** la información que antes estaba distribuida en múltiples endpoints:

### 🎯 **Dashboard Metrics** (en `resumenGeneral`)
- Total de ventas y montos
- Ticket promedio
- Ventas con/sin comprobante
- Ventas canceladas
- Alertas de medios electrónicos

### 🚨 **Alertas** (en `alertas`)
- Ventas sin comprobante
- Ventas electrónicas pendientes
- Ventas canceladas sin nota de crédito

### 🏆 **Top Sellers** (en `desglosePorVendedor`)
- Ranking de vendedores ordenado por monto
- Performance individual de cada vendedor
- Porcentajes del total

### 📦 **Top Products** (en `productosTopVentas`)
- Productos más vendidos por cantidad
- Montos totales por producto
- Unidades de medida correctas

### 🧾 **Estadísticas Fiscales** (en `estadisticasComprobantes`)
- Comprobantes emitidos por tipo
- Montos facturados e IVA
- Notas de crédito

---

## 🔧 Códigos de Respuesta

| Código | Descripción |
|--------|-------------|
| `200` | Éxito - Datos devueltos correctamente |
| `400` | Error de validación en parámetros |
| `500` | Error interno del servidor |

## 📝 Notas Importantes

### Períodos Predefinidos
- `today`: Desde las 00:00:00 hasta ahora del día actual
- `yesterday`: Todo el día anterior completo (00:00:00 a 23:59:59)
- `current_week`: Desde el lunes de la semana actual hasta ahora
- `current_month`: Desde el día 1 del mes actual hasta ahora
- `last_month`: Todo el mes anterior completo (desde el día 1 hasta el último día)

### Filtros de Usuario
- Los nombres de usuario deben separarse por comas
- No distingue entre mayúsculas y minúsculas
- Si no se especifica, incluye todos los usuarios

### Medios de Pago Electrónicos
Los siguientes medios se consideran electrónicos:
- `TARJETA_DEBITO`
- `TARJETA_CREDITO`
- `TRANSFERENCIA`
- `QR_MERCADOPAGO`

### Estados de Comprobantes
Los comprobantes válidos tienen estado:
- `AUTORIZADO` (CAE online)
- `AUTORIZADO_OFFLINE` (CAEA offline)

### Filtro `soloConComprobante`
**Comportamiento importante:**
- `soloConComprobante=true` → Devuelve **SOLO** ventas que tienen comprobante emitido
- `soloConComprobante=false` → Devuelve **TODAS** las ventas (con y sin comprobante)
- Sin especificar el parámetro → Devuelve **TODAS** las ventas (comportamiento por defecto)

**Nota:** Si necesitas solo las ventas SIN comprobante, usa los datos de `alertas.ventasSinComprobante` en la respuesta.

## 🚀 Ejemplos de Integración

### JavaScript/Fetch
```javascript
// Obtener reporte completo del día
const response = await fetch('http://localhost:8080/api/reports/sales?periodo=today');
const report = await response.json();

// Dashboard metrics
console.log('Ventas del día:', report.resumenGeneral.totalVentas);
console.log('Monto total:', report.resumenGeneral.montoTotal);

// Top vendedores (ya incluido en la respuesta)
report.desglosePorVendedor.forEach(seller => {
  console.log(`${seller.nombreDisplay}: $${seller.montoTotal}`);
});

// Alertas (ya incluidas en la respuesta)
if (report.alertas.ventasSinComprobante > 0) {
  console.log(`⚠️ ${report.alertas.ventasSinComprobante} ventas sin comprobante`);
}

// Top productos (ya incluidos en la respuesta)
report.productosTopVentas.forEach(product => {
  console.log(`${product.productoNombre}: ${product.cantidadVendida} ${product.unidadMedida}`);
});
```

### Python/Requests
```python
import requests

# Obtener reporte completo con filtros
params = {
    'periodo': 'last_month',  # Mes anterior completo
    'usuarios': 'leidimar,nelson',
    'soloMediosPagoElectronicos': 'true'
}
response = requests.get('http://localhost:8080/api/reports/sales', params=params)
report = response.json()

# Dashboard metrics
print(f"Ventas del mes: {report['resumenGeneral']['totalVentas']}")
print(f"Monto total: ${report['resumenGeneral']['montoTotal']}")

# Alertas
alerts = report['alertas']
if alerts['ventasSinComprobante'] > 0:
    print(f"⚠️ {alerts['ventasSinComprobante']} ventas sin comprobante")

# Top vendedores
for seller in report['desglosePorVendedor'][:3]:  # Top 3
    print(f"{seller['nombreDisplay']}: ${seller['montoTotal']}")

# Estadísticas fiscales
fiscal = report['estadisticasComprobantes']
print(f"Comprobantes emitidos: {fiscal['totalComprobantes']}")
print(f"Total facturado: ${fiscal['montoTotalFacturado']}")
```

### PowerShell
```powershell
# Obtener reporte completo con filtros
$params = @{
    Uri = 'http://localhost:8080/api/reports/sales'
    Method = 'Get'
    Body = @{
        periodo = 'today'
        soloMediosPagoElectronicos = 'true'
        usuarios = 'leidimar,nelson'
    }
}
$report = Invoke-RestMethod @params

# Mostrar métricas principales
Write-Host "=== DASHBOARD ==="
Write-Host "Ventas: $($report.resumenGeneral.totalVentas)"
Write-Host "Monto: $($report.resumenGeneral.montoTotal)"

# Mostrar alertas
Write-Host "`n=== ALERTAS ==="
if ($report.alertas.ventasSinComprobante -gt 0) {
    Write-Host "⚠️ $($report.alertas.ventasSinComprobante) ventas sin comprobante" -ForegroundColor Yellow
}

# Mostrar top vendedores
Write-Host "`n=== TOP VENDEDORES ==="
$report.desglosePorVendedor | ForEach-Object {
    Write-Host "$($_.nombreDisplay): $($_.montoTotal)"
}

# Mostrar estadísticas fiscales
Write-Host "`n=== FISCAL ==="
Write-Host "Comprobantes: $($report.estadisticasComprobantes.totalComprobantes)"
Write-Host "Facturado: $($report.estadisticasComprobantes.montoTotalFacturado)"
```

---

## 📞 Soporte

Para dudas o problemas con la API de reportes, consultar:
- Documentación técnica en `/docs/`
- Logs del servidor para errores específicos
- Código fuente en `src/main/kotlin/com/gnico/majo/infrastructure/routes/ReportRoutes.kt`
