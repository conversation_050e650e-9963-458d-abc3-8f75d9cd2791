# Integración AFIP - Arquitectura Hexagonal

## Resumen

Se ha integrado completamente los webservices de AFIP en la arquitectura hexagonal del proyecto, unificando los servicios de facturación online (CAE) y offline (CAEA) en un único servicio con modelo de dominio rico.

## Arquitectura

### Capa de Dominio
- **AfipResponse**: Modelo rico que encapsula respuestas de AFIP con lógica de negocio
- **AfipCredentials**: Credenciales de autenticación con validaciones
- **TipoComprobanteAfip**: Enum con tipos de comprobante y mapeo a códigos AFIP
- **AfipCAERequest**: Solicitud de CAE con validaciones y conversiones
- **IvaDetail**: Detalles de IVA con mapeo a códigos AFIP

### Puertos (Interfaces)
- **AfipService**: Puerto unificado para servicios AFIP (online y offline)

### Adaptadores
- **AfipServiceAdapter**: Implementación unificada que maneja CAE y CAEA

### Infraestructura
- **WsaaClientImpl**: Cliente mejorado para autenticación WSAA
- **WsfeClientImpl**: Cliente mejorado para facturación WSFE
- **AfipConfigurationService**: Servicio de configuración centralizada

## Características Principales

### 1. Modelo de Dominio Rico
```kotlin
val response = AfipResponse.createApprovedCAE(
    cae = "12345678901234",
    fechaVencimiento = LocalDate.now().plusDays(10),
    numeroComprobante = 123L
)

// Lógica de negocio encapsulada
if (response.isValid()) {
    println("Estado: ${response.getEstadoDescriptivo()}")
}
```

### 2. Unificación de Servicios
```kotlin
// Un solo servicio para ambos modos
interface AfipService {
    suspend fun solicitarCAE(sale: Sale, tipoComprobante: String, puntoVenta: Int): AfipResponse
    suspend fun crearComprobanteConCAEA(sale: Sale, tipoComprobante: String, puntoVenta: Int): AfipResponse
}
```

### 3. Configuración Centralizada
```kotlin
// Variables de entorno soportadas:
// AFIP_CUIT=20349249902
// AFIP_CERTIFICATE_PATH=afip/Certificado.p12
// AFIP_CERTIFICATE_PASSWORD=password
// AFIP_PRODUCTION=false
```

### 4. Manejo de Errores Robusto
- Excepciones específicas: `AfipWebserviceException`, `AfipAuthenticationException`
- Respuestas de dominio que encapsulan errores
- Logging detallado para debugging

### 5. Cache de Credenciales
- Cache automático de tokens WSAA
- Renovación automática antes del vencimiento
- Manejo de errores de autenticación

## Uso

### Facturación Online (CAE)
```kotlin
val response = afipService.solicitarCAE(
    sale = sale,
    tipoComprobante = "FACTURA_B",
    puntoVenta = 1
)

if (response.isApproved()) {
    // Crear comprobante con CAE
    val comprobante = createComprobante(sale, response)
}
```

### Facturación Offline (CAEA)
```kotlin
val response = afipService.crearComprobanteConCAEA(
    sale = sale,
    tipoComprobante = "FACTURA_B",
    puntoVenta = 1
)

if (response.isApproved()) {
    // Guardar para envío posterior a AFIP
    saveOfflineComprobante(sale, response)
}
```

### Verificación de Disponibilidad
```kotlin
if (afipService.isServiceAvailable()) {
    // Usar modo online
    solicitarCAE(...)
} else {
    // Usar modo offline
    crearComprobanteConCAEA(...)
}
```

## Configuración

### Certificado AFIP
1. Colocar el certificado `.p12` en `src/main/resources/afip/`
2. Configurar variables de entorno o usar valores por defecto
3. El servicio validará automáticamente la configuración

### Dependency Injection (Koin)
```kotlin
single<AfipService> { AfipServiceAdapter() }
```

## Testing

Se incluyen tests completos que verifican:
- Creación y validación de modelos de dominio
- Mapeo de tipos de comprobante
- Cálculo de detalles de IVA
- Manejo de errores
- Simulación de respuestas AFIP

## Migración

### Cambios Realizados
1. **Eliminados**: `AfipOnlineService`, `AfipOfflineService`, `AfipOnlineServiceAdapter`, `AfipOfflineServiceAdapter`
2. **Unificado**: Nuevo `AfipService` con implementación única
3. **Mejorado**: Modelos de dominio ricos con lógica de negocio
4. **Reorganizado**: Webservices movidos a capa de infraestructura

### Compatibilidad
- La API pública del `SaleService` permanece igual
- Los parámetros `facturaOnline` y `facturaOffline` funcionan como antes
- La lógica de impresión de tickets no cambia

## Próximos Pasos

1. **Implementar CAEA real**: Actualmente usa simulación
2. **Persistencia de comprobantes offline**: Para envío posterior
3. **Monitoreo**: Métricas de éxito/fallo de AFIP
4. **Configuración avanzada**: Múltiples puntos de venta, certificados por ambiente
5. **Retry logic**: Reintentos automáticos en caso de fallas temporales

## Beneficios

- **Mantenibilidad**: Código más limpio y organizado
- **Testabilidad**: Modelos de dominio fáciles de testear
- **Extensibilidad**: Fácil agregar nuevos tipos de comprobante
- **Robustez**: Mejor manejo de errores y configuración
- **Performance**: Cache de credenciales reduce llamadas a WSAA
