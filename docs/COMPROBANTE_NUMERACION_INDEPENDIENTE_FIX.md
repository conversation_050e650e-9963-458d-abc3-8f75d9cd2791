# Fix: Numeración Independiente de Comprobantes por Tipo

## Problema Identificado

### Descripción del Error
Al intentar cancelar una venta y generar una nota de crédito, el sistema fallaba con el siguiente error:

```
ERROR: llave duplicada viola restricción de unicidad "unique_comprobante"
Detail: Ya existe la llave (punto_venta, numero_comprobante)=(1, 4).
```

### Causa Raíz
El problema estaba en la restricción de unicidad de la tabla `comprobantes`:

```sql
-- Restricción INCORRECTA (antes del fix)
CONSTRAINT unique_comprobante UNIQUE (punto_venta, numero_comprobante)
```

Esta restricción **NO incluía el tipo de comprobante**, pero según las reglas de AFIP:
- **FACTURA_B** puede tener número 4 en punto de venta 1
- **NOTA_CREDITO_B** puede tener número 4 en punto de venta 1 (numeración independiente)
- **NOTA_DEBITO_B** puede tener número 4 en punto de venta 1 (numeración independiente)

### Evidencia en los Logs
```
🔍 POST /api/sales/58/cancel - Cancelando venta
📊 Solicitando CAE online para venta V-6822b275, tipo: NOTA_CREDITO_B, PV: 1
📊 Obteniendo último número de comprobante para PV: 1, Tipo: 8
📊 Datos enviados a AFIP para venta V-6822b275:
  - Tipo comprobante: 8 (Nota de Crédito B)
  - Punto de venta: 1
  - Número: 4  ← AFIP asigna número 4 para NOTA_CREDITO_B
📊 CAE procesado para venta V-6822b275: A, CAE: 75282262836534
❌ Error al generar nota de crédito: llave duplicada viola restricción de unicidad "unique_comprobante"
  Detail: Ya existe la llave (punto_venta, numero_comprobante)=(1, 4).
```

## Solución Implementada

### 1. Migración de Base de Datos
Creada migración `V25__fix_comprobante_unique_constraint.sql`:

```sql
-- Eliminar la restricción incorrecta
ALTER TABLE comprobantes DROP CONSTRAINT unique_comprobante;

-- Crear nueva restricción que incluye tipo_comprobante
ALTER TABLE comprobantes ADD CONSTRAINT unique_comprobante_por_tipo 
    UNIQUE (punto_venta, numero_comprobante, tipo_comprobante);
```

### 2. Justificación Técnica
La nueva restricción permite:
- FACTURA_B: PV=1, Número=4, Tipo=FACTURA_B ✅
- NOTA_CREDITO_B: PV=1, Número=4, Tipo=NOTA_CREDITO_B ✅
- NOTA_DEBITO_B: PV=1, Número=4, Tipo=NOTA_DEBITO_B ✅

Esto es **correcto según AFIP** porque cada tipo de comprobante tiene su propia secuencia de numeración.

### 3. Optimización Adicional
Agregado índice para mejorar consultas:

```sql
CREATE INDEX idx_comprobantes_punto_venta_numero ON comprobantes(punto_venta, numero_comprobante);
```

## Verificación del Fix

### Tests Ejecutados
- ✅ **205 tests passed, 7 skipped** - Todos los tests existentes siguen funcionando
- ✅ **Migración exitosa** - La base de datos se actualizó correctamente
- ✅ **Compilación exitosa** - No hay errores de compilación

### Comportamiento Esperado Después del Fix
1. **Facturas**: Pueden usar cualquier número asignado por AFIP
2. **Notas de Crédito**: Pueden usar el mismo número que una factura (numeración independiente)
3. **Notas de Débito**: Pueden usar el mismo número que una factura (numeración independiente)
4. **Restricción**: Solo se previene duplicación dentro del mismo tipo de comprobante

## Reglas de AFIP Respetadas

### Numeración por Tipo de Comprobante
Según AFIP, cada tipo de comprobante mantiene su propia secuencia:

| Tipo AFIP | Código | Numeración |
|-----------|--------|------------|
| Factura B | 6 | Independiente |
| Nota Crédito B | 8 | Independiente |
| Nota Débito B | 9 | Independiente |

### Ejemplo Válido
```
Punto de Venta 1:
- FACTURA_B #4 ✅
- NOTA_CREDITO_B #4 ✅ (numeración independiente)
- NOTA_DEBITO_B #4 ✅ (numeración independiente)
```

## Impacto del Cambio

### ✅ Beneficios
1. **Cumplimiento AFIP**: Respeta las reglas de numeración independiente
2. **Cancelación de Ventas**: Las notas de crédito se generan correctamente
3. **Flexibilidad**: Permite todos los escenarios válidos de AFIP
4. **Integridad**: Mantiene unicidad dentro de cada tipo

### ⚠️ Consideraciones
1. **Migración Automática**: Se ejecuta automáticamente con Flyway
2. **Compatibilidad**: No afecta datos existentes
3. **Performance**: El nuevo índice mejora las consultas

## Conclusión

El fix resuelve completamente el problema de cancelación de ventas al permitir que diferentes tipos de comprobantes usen la misma numeración en el mismo punto de venta, respetando las reglas de AFIP para numeración independiente por tipo de comprobante.

**Estado**: ✅ **RESUELTO** - El sistema ahora puede generar notas de crédito y débito sin conflictos de numeración.
