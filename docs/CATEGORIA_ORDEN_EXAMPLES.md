# Ejemplos de Uso - Campo Orden en Categorías

## Casos de Uso Comunes

### 1. Reorganización Inicial del Menú

**Situación**: Tienes categorías ordenadas alfabéticamente pero quieres mostrar las más importantes primero.

**Categorías actuales** (orden alfabético):
- Bebidas
- Comidas  
- Postres

**Orden deseado** (por importancia):
1. <PERSON><PERSON>das (más vendido)
2. <PERSON>bidas (segundo más vendido)
3. <PERSON>res (menos vendido)

**Solución**:
```http
PUT /api/categorias/orden
Content-Type: application/json

{
  "categorias": [
    {"id": 2, "orden": 1},  // Comidas
    {"id": 1, "orden": 2},  // Bebidas  
    {"id": 3, "orden": 3}   // Postres
  ]
}
```

### 2. Agregar Nueva Categoría Promocional

**Situación**: Quieres agregar una categoría "Ofertas del Día" que aparezca primera.

```http
POST /api/categorias
Content-Type: application/json

{
  "nombre": "Ofertas del Día",
  "descripcion": "Productos en promoción especial",
  "color": "ff0000",
  "orden": 1,
  "activo": true
}
```

Luego reorganizar las existentes:
```http
PUT /api/categorias/orden
Content-Type: application/json

{
  "categorias": [
    {"id": 2, "orden": 2},  // Comidas
    {"id": 1, "orden": 3},  // Bebidas
    {"id": 3, "orden": 4}   // Postres
  ]
}
```

### 3. Organización por Horario de Consumo

**Orden deseado** para un café:
1. Desayunos (orden: 1)
2. Bebidas Calientes (orden: 2)  
3. Bebidas Frías (orden: 3)
4. Almuerzos (orden: 4)
5. Postres (orden: 5)
6. Meriendas (orden: 6)

```http
PUT /api/categorias/orden
Content-Type: application/json

{
  "categorias": [
    {"id": 10, "orden": 1},  // Desayunos
    {"id": 11, "orden": 2},  // Bebidas Calientes
    {"id": 12, "orden": 3},  // Bebidas Frías
    {"id": 13, "orden": 4},  // Almuerzos
    {"id": 14, "orden": 5},  // Postres
    {"id": 15, "orden": 6}   // Meriendas
  ]
}
```

### 4. Organización Estacional

**Situación**: En verano quieres priorizar bebidas frías, en invierno bebidas calientes.

**Configuración de Verano**:
```http
PUT /api/categorias/orden
{
  "categorias": [
    {"id": 12, "orden": 1},  // Bebidas Frías
    {"id": 16, "orden": 2},  // Helados
    {"id": 17, "orden": 3},  // Ensaladas
    {"id": 11, "orden": 4},  // Bebidas Calientes
    {"id": 13, "orden": 5}   // Comidas Calientes
  ]
}
```

**Configuración de Invierno**:
```http
PUT /api/categorias/orden
{
  "categorias": [
    {"id": 11, "orden": 1},  // Bebidas Calientes
    {"id": 13, "orden": 2},  // Comidas Calientes
    {"id": 18, "orden": 3},  // Sopas
    {"id": 12, "orden": 4},  // Bebidas Frías
    {"id": 16, "orden": 5}   // Helados
  ]
}
```

### 5. Insertar Categoría en Posición Específica

**Situación**: Quieres insertar "Smoothies" entre "Bebidas Frías" (orden 2) y "Postres" (orden 3).

**Opción 1** - Usar orden decimal (recomendado):
```http
POST /api/categorias
{
  "nombre": "Smoothies",
  "orden": 25,  // Entre 20 (Bebidas Frías) y 30 (Postres)
  "color": "00ff00"
}
```

**Opción 2** - Reorganizar todo:
```http
PUT /api/categorias/orden
{
  "categorias": [
    {"id": 1, "orden": 1},   // Bebidas Calientes
    {"id": 2, "orden": 2},   // Bebidas Frías
    {"id": 19, "orden": 3},  // Smoothies (nueva)
    {"id": 3, "orden": 4},   // Postres
    {"id": 4, "orden": 5}    // Otros
  ]
}
```

## Respuestas de la API

### Obtener Categorías Ordenadas
```http
GET /api/categorias
```

**Response**:
```json
{
  "categorias": [
    {
      "id": 4,
      "nombre": "Ofertas del Día",
      "descripcion": "Productos en promoción especial",
      "color": "ff0000",
      "orden": 1,
      "activo": true
    },
    {
      "id": 2,
      "nombre": "Comidas",
      "descripcion": "Platos principales",
      "color": null,
      "orden": 2,
      "activo": true
    },
    {
      "id": 1,
      "nombre": "Bebidas",
      "descripcion": "Bebidas frías y calientes",
      "color": "0066cc",
      "orden": 3,
      "activo": true
    },
    {
      "id": 3,
      "nombre": "Postres",
      "descripcion": "Postres y dulces",
      "color": "ff99cc",
      "orden": 4,
      "activo": true
    }
  ],
  "total": 4
}
```

### Respuesta de Bulk Update Exitoso
```json
{
  "actualizadas": 4,
  "mensaje": "Se actualizó el orden de 4 categoría(s) correctamente"
}
```

### Respuesta de Error - Orden Inválido
```json
{
  "actualizadas": 0,
  "mensaje": "El orden debe ser un número positivo. Categoría ID 2 tiene orden 0"
}
```

## Mejores Prácticas

### 1. Usar Espacios entre Órdenes
En lugar de usar 1, 2, 3, 4... usar 10, 20, 30, 40...
Esto facilita insertar nuevas categorías sin reorganizar todo.

### 2. Agrupar por Decenas
- 10-19: Promociones/Ofertas
- 20-29: Bebidas
- 30-39: Comidas principales  
- 40-49: Postres
- 50-59: Otros

### 3. Documentar el Sistema de Orden
Mantener un documento que explique qué rangos de números se usan para qué tipos de categorías.

### 4. Automatización Estacional
Crear scripts que cambien automáticamente el orden según la temporada o eventos especiales.

## Integración con Frontend

### JavaScript/TypeScript
```javascript
// Obtener categorías ordenadas
const response = await fetch('/api/categorias');
const data = await response.json();

// Las categorías ya vienen ordenadas por la API
const categorias = data.categorias; // Ya ordenadas por orden ASC, nombre ASC

// Reorganizar orden
const nuevoOrden = [
  {id: 1, orden: 3},
  {id: 2, orden: 1}, 
  {id: 3, orden: 2}
];

await fetch('/api/categorias/orden', {
  method: 'PUT',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify({categorias: nuevoOrden})
});
```

### React Hook Ejemplo
```javascript
const useCategorias = () => {
  const [categorias, setCategorias] = useState([]);
  
  const reorderCategorias = async (nuevoOrden) => {
    const response = await fetch('/api/categorias/orden', {
      method: 'PUT',
      headers: {'Content-Type': 'application/json'},
      body: JSON.stringify({categorias: nuevoOrden})
    });
    
    if (response.ok) {
      // Recargar categorías para obtener el nuevo orden
      loadCategorias();
    }
  };
  
  return {categorias, reorderCategorias};
};
```
