# Diagnóstico: Campos de Cancelación Aparecen como NULL/false

## 🔍 Problema Específico

**Síntoma**: Al usar `GET /api/sales?incluirCanceladas=true`, las ventas que deberían estar canceladas aparecen con:
```json
{
  "cancelada": false,
  "fechaCancelacion": null,
  "usuarioCancelacion": null,
  "motivoCancelacion": null
}
```

## 🎯 Posibles Causas y Soluciones

### 1. ❌ **Causa Más Probable: Datos No Actualizados en BD**

**Verificación**:
```sql
-- Verificar directamente en la base de datos
SELECT id, numero_venta, cancelada, fecha_cancelacion, usuario_cancelacion, motivo_cancelacion
FROM ventas 
WHERE cancelada = true;
```

**Si la consulta devuelve 0 filas**:
- ✅ **Solución**: Las ventas NO están realmente canceladas en la BD
- 🔧 **Acción**: Usar el endpoint de cancelación correctamente

### 2. ❌ **Causa: Migración No Ejecutada**

**Verificación**:
```sql
-- Verificar si los campos existen
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'ventas' 
AND column_name IN ('cancelada', 'fecha_cancelacion', 'usuario_cancelacion', 'motivo_cancelacion');
```

**Si faltan campos**:
- ✅ **Solución**: Ejecutar la migración
- 🔧 **Acción**: `./gradlew flywayMigrate`

### 3. ❌ **Causa: Endpoint de Cancelación No Funciona**

**Verificación**:
```http
POST /api/sales/{id}/cancel
Content-Type: application/json

{
  "usuarioCancelacion": "admin",
  "motivo": "Test de cancelación",
  "generarNotaCreditoOnline": false
}
```

**Respuesta esperada**:
```json
{
  "success": true,
  "message": "Venta cancelada exitosamente",
  "ventaId": 123,
  "numeroVenta": "**********"
}
```

**Si success=false**:
- ✅ **Solución**: Revisar el error en la respuesta
- 🔧 **Acción**: Corregir el problema reportado

### 4. ❌ **Causa: Venta Ya Cancelada**

**Verificación**:
```http
POST /api/sales/{id}/cancel
```

**Respuesta si ya está cancelada**:
```json
{
  "success": false,
  "message": "La venta ********** ya está cancelada",
  "error": "Venta ya cancelada"
}
```

**Si recibes este error**:
- ✅ **Solución**: La venta YA está cancelada
- 🔧 **Acción**: Verificar por qué no aparece como cancelada en GET

### 5. ❌ **Causa: Usuario No Existe**

**Verificación**:
```sql
-- Verificar que el usuario existe
SELECT username, nombre, activo FROM usuarios WHERE username = 'admin';
```

**Si el usuario no existe**:
- ✅ **Solución**: Crear el usuario o usar uno existente
- 🔧 **Acción**: Verificar usuarios disponibles

## 🔧 Pasos de Diagnóstico Completo

### Paso 1: Verificar Estado Actual
```sql
-- Ver todas las ventas y su estado
SELECT 
    id, 
    numero_venta, 
    cancelada, 
    fecha_cancelacion,
    usuario_cancelacion,
    motivo_cancelacion,
    fecha_venta
FROM ventas 
ORDER BY id DESC 
LIMIT 10;
```

### Paso 2: Intentar Cancelar una Venta
```bash
# 1. Obtener una venta no cancelada
curl -X GET "http://localhost:8080/api/sales?incluirCanceladas=true&size=1"

# 2. Cancelar la venta (usar el ID de la respuesta anterior)
curl -X POST "http://localhost:8080/api/sales/123/cancel" \
  -H "Content-Type: application/json" \
  -d '{
    "usuarioCancelacion": "admin",
    "motivo": "Test de diagnóstico",
    "generarNotaCreditoOnline": false
  }'

# 3. Verificar que aparece como cancelada
curl -X GET "http://localhost:8080/api/sales?incluirCanceladas=true&size=5"
```

### Paso 3: Verificar en Base de Datos
```sql
-- Después de la cancelación, verificar en BD
SELECT 
    id, 
    numero_venta, 
    cancelada, 
    fecha_cancelacion,
    usuario_cancelacion,
    motivo_cancelacion
FROM ventas 
WHERE id = 123; -- Usar el ID que cancelaste
```

### Paso 4: Verificar Logs de Aplicación
Buscar en los logs de la aplicación:
```
✅ Venta ********** cancelada exitosamente por admin
```

## 🚨 Errores Comunes

### Error 1: "Usuario no encontrado"
```json
{
  "success": false,
  "message": "Usuario 'admin' no encontrado",
  "error": "Usuario no encontrado"
}
```
**Solución**: Verificar usuarios existentes:
```sql
SELECT username FROM usuarios WHERE activo = true;
```

### Error 2: "Venta no encontrada"
```json
{
  "success": false,
  "message": "Venta 999 no encontrada",
  "error": "Venta no encontrada"
}
```
**Solución**: Verificar que el ID existe:
```sql
SELECT id, numero_venta FROM ventas WHERE id = 999;
```

### Error 3: Campos NULL en Respuesta
Si ves `"cancelada": false` pero en BD está `cancelada = true`:
- 🔧 **Problema**: Error en el mapeo de JOOQ
- 🔧 **Solución**: Regenerar código JOOQ: `./gradlew jooqCodegen`

## ✅ Verificación Final

Después de cancelar una venta correctamente, deberías ver:

**En la API**:
```json
{
  "id": 123,
  "numeroVenta": "**********",
  "cancelada": true,
  "fechaCancelacion": "15/01/2025 14:30",
  "usuarioCancelacion": "admin",
  "motivoCancelacion": "Test de diagnóstico"
}
```

**En la Base de Datos**:
```sql
id | numero_venta | cancelada | fecha_cancelacion   | usuario_cancelacion | motivo_cancelacion
123| **********   | true      | 2025-01-15 14:30:00 | admin              | Test de diagnóstico
```

## 🎯 Resumen de Acciones

1. **Verificar BD directamente** con consultas SQL
2. **Probar endpoint de cancelación** con datos válidos
3. **Verificar logs** de la aplicación
4. **Regenerar JOOQ** si es necesario
5. **Verificar usuarios** existentes en el sistema

Si después de estos pasos el problema persiste, el issue está en el mapeo de JOOQ o en la lógica del repositorio.
