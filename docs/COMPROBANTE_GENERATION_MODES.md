# Modos de Generación de Comprobantes

## Resumen

Este documento describe las mejoras implementadas para manejar correctamente la generación de comprobantes online (CAE) y offline (CAEA), especialmente para notas de crédito y débito que deben respetar el método de autorización del comprobante original.

## Problemas Resueltos

### 1. **Falta de indicación de modo en el request**
- **Problema**: El `GenerarComprobanteRequest` no incluía un campo para especificar si generar online o offline
- **Solución**: Agregado campo `modoGeneracion` opcional con valores `AUTO`, `ONLINE`, `OFFLINE`

### 2. **Notas de crédito/débito no respetaban el método original**
- **Problema**: Las notas se generaban según el parámetro del usuario, sin validar consistencia con el comprobante original
- **Solución**: Implementada validación automática que verifica el `tipoAutorizacion` del comprobante original

### 3. **Falta de endpoint unificado**
- **Problema**: Solo existían endpoints separados `/online` y `/offline`
- **Solución**: Reemplazados por endpoint unificado `/api/comprobantes` que determina automáticamente el modo

### 4. **Campo puntoVenta innecesario y conflictivo**
- **Problema**: Permitir especificar `puntoVenta` en el request podía causar conflictos con la configuración del `.env`
- **Solución**: Eliminado campo `puntoVenta` del request. El punto de venta se obtiene automáticamente del archivo `.env`

## Nuevas Funcionalidades

### Campo `modoGeneracion`

```kotlin
@Serializable
data class GenerarComprobanteRequest(
    val ventaId: Int,
    val tipoComprobante: String? = null,  // FACTURA_B, NOTA_CREDITO_B, NOTA_DEBITO_B
    val modoGeneracion: String? = null    // AUTO, ONLINE, OFFLINE
)
```

#### Valores permitidos:

- **`AUTO`** (default): Determina automáticamente el modo
  - Para facturas: usa ONLINE por defecto
  - Para notas de crédito/débito: usa el mismo método que el comprobante original
- **`ONLINE`**: Fuerza generación online (CAE)
- **`OFFLINE`**: Fuerza generación offline (CAEA)

**Nota**: El punto de venta se obtiene automáticamente del archivo `.env` y no puede ser especificado en el request.

### Endpoint Unificado

```
POST /api/comprobantes
```

Este es el **único endpoint** para generar comprobantes. Características:

1. Recibe el `modoGeneracion` del request
2. Para notas de crédito/débito, valida consistencia con el comprobante original
3. Genera el comprobante usando el método apropiado

#### Ventajas del Diseño Unificado:

1. **Simplicidad**: Un solo endpoint para todos los casos
2. **Flexibilidad**: Permite modo automático, online y offline en el mismo endpoint
3. **Consistencia**: Misma estructura de request para todos los modos
4. **Mantenibilidad**: Menos código duplicado en rutas y controladores
5. **Escalabilidad**: Fácil agregar nuevos modos sin crear endpoints adicionales
6. **Claridad**: No hay confusión sobre qué endpoint usar

### Validaciones Implementadas

#### Para Notas de Crédito/Débito:

1. **Modo AUTO**: Detecta automáticamente el tipo de autorización del comprobante original
2. **Modo ONLINE/OFFLINE**: Valida que sea consistente con el comprobante original

#### Ejemplo de Validación:

```
Si la factura original fue generada con CAE (online):
- ✅ NOTA_CREDITO_B con modo ONLINE → Permitido
- ✅ NOTA_CREDITO_B con modo AUTO → Usa ONLINE automáticamente
- ❌ NOTA_CREDITO_B con modo OFFLINE → Error de validación

Si la factura original fue generada con CAEA (offline):
- ✅ NOTA_CREDITO_B con modo OFFLINE → Permitido
- ✅ NOTA_CREDITO_B con modo AUTO → Usa OFFLINE automáticamente
- ❌ NOTA_CREDITO_B con modo ONLINE → Error de validación
```

## Cambios en el Servicio de Cancelación

El servicio de cancelación de ventas ahora:

1. Recibe el parámetro `generarNotaCreditoOnline` del usuario
2. Lo convierte a modo explícito (`ONLINE` o `OFFLINE`)
3. El sistema valida automáticamente la consistencia con el comprobante original

## API Simplificada

### Endpoint Único

Solo existe un endpoint para generar comprobantes:
- `POST /api/comprobantes` → Endpoint unificado con `modoGeneracion`

### Requests Sin `modoGeneracion`

Si no se especifica `modoGeneracion`, se usa `AUTO` por defecto.

### Migración Completada

Los endpoints específicos `/online` y `/offline` han sido **eliminados** para simplificar la API.

## Ejemplos de Uso

### Generar Factura (modo automático)
```json
POST /api/comprobantes
{
    "ventaId": 123,
    "tipoComprobante": "FACTURA_B"
}
```
→ Se genera online (CAE) por defecto usando punto de venta del `.env`

### Generar Nota de Crédito (modo automático)
```json
POST /api/comprobantes
{
    "ventaId": 123,
    "tipoComprobante": "NOTA_CREDITO_B"
}
```
→ Se detecta automáticamente el método de la factura original

### Generar Comprobante (modo explícito online)
```json
POST /api/comprobantes
{
    "ventaId": 123,
    "tipoComprobante": "FACTURA_B",
    "modoGeneracion": "ONLINE"
}
```
→ Se genera online (CAE) forzadamente

### Generar Comprobante (modo explícito offline)
```json
POST /api/comprobantes
{
    "ventaId": 123,
    "tipoComprobante": "FACTURA_B",
    "modoGeneracion": "OFFLINE"
}
```
→ Se genera offline (CAEA) forzadamente

## Mensajes de Error

### Modo Inválido
```
"Modo de generación no válido: INVALID. Valores permitidos: AUTO, ONLINE, OFFLINE"
```

### Inconsistencia con Comprobante Original
```
"Las notas de crédito/débito deben generarse con el mismo método que el comprobante original. 
Comprobante original: ONLINE (CAE), Solicitado: OFFLINE (CAEA)"
```

## Beneficios

1. **Cumplimiento AFIP**: Las notas de crédito/débito respetan automáticamente el método del comprobante original
2. **Flexibilidad**: Permite especificar el modo o usar detección automática
3. **Simplicidad**: Un solo endpoint para todos los casos de uso
4. **Validación**: Previene errores de inconsistencia en tiempo de ejecución
5. **Mantenibilidad**: Menos código duplicado y mayor claridad en la API
