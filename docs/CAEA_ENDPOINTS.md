# Endpoints CAEA - Gestión de Códigos de Autorización Electrónico Anticipado

## Resumen

Se han implementado endpoints REST para gestionar los CAEA (Códigos de Autorización Electrónico Anticipado) de AFIP, incluyendo solicitud de códigos e información de movimientos.

## Endpoints Disponibles

### 1. Solicitar CAEA

**POST** `/api/caea/solicitar`

Solicita un nuevo CAEA a AFIP para un período específico.

**Request Body:**
```json
{
  "puntoVenta": 2,
  "periodo": "202507",
  "orden": 1
}
```

**Response (Éxito):**
```json
{
  "success": true,
  "caea": "21234567890123",
  "fechaVencimiento": "2025-07-31",
  "observaciones": ["CAEA obtenido exitosamente"],
  "error": null
}
```

**Response (Error):**
```json
{
  "success": false,
  "caea": null,
  "fechaVencimiento": null,
  "observaciones": ["Error de validación"],
  "error": "Período inválido"
}
```

### 2. Informar CAEA Automáticamente

**POST** `/api/caea/informar`

Informa movimientos CAEA a AFIP de forma automática. El sistema determina automáticamente si usar `FECAEASinMovimientoInformar` o `FECAEARegInformativo` basándose en si existen comprobantes emitidos con el CAEA.

**Request Body:**
```json
{
  "caeaCode": "21234567890123",
  "puntoVenta": 2
}
```

**Response (Sin Movimientos):**
```json
{
  "success": true,
  "mensaje": "CAEA informado exitosamente (sin movimientos)",
  "observaciones": ["FECAEASinMovimientoInformar procesado correctamente"],
  "error": null
}
```

**Response (Con Movimientos):**
```json
{
  "success": true,
  "mensaje": "CAEA informado exitosamente (con movimientos)",
  "observaciones": ["FECAEARegInformativo procesado - 15 comprobantes informados"],
  "error": null
}
```

### 3. Consultar CAEAs por Punto de Venta

**GET** `/api/caea/punto-venta/{puntoVenta}`

Obtiene todos los CAEAs asociados a un punto de venta específico.

**Response:**
```json
{
  "caeas": [
    {
      "id": 1,
      "caea": "21234567890123",
      "puntoVenta": 2,
      "periodo": "202507",
      "fechaDesde": "2025-07-01",
      "fechaHasta": "2025-07-31",
      "orden": 1,
      "estado": "ACTIVO",
      "ultimoNumeroFacturaB": 150,
      "ultimoNumeroNotaCreditoB": 5,
      "ultimoNumeroNotaDebitoB": 2
    }
  ],
  "total": 1
}
```

### 4. Consultar Comprobantes Emitidos con CAEA

**GET** `/api/caea/{caeaCode}/comprobantes`

Obtiene todos los comprobantes que fueron emitidos utilizando un CAEA específico.

**Response:**
```json
{
  "caeaCode": "21234567890123",
  "comprobantes": [
    {
      "numeroComprobante": 145,
      "tipoComprobante": "FACTURA_B",
      "fechaEmision": "2025-07-15",
      "montoTotal": "1250.00",
      "caeaUtilizado": "21234567890123"
    }
  ],
  "total": 1
}
```

## Flujo de Trabajo Recomendado

### 1. Solicitud de CAEA

1. **Antes del período**: Solicitar CAEA para el próximo período usando `/api/caea/solicitar`
2. **Verificar respuesta**: Confirmar que el CAEA fue obtenido exitosamente
3. **Almacenamiento**: El sistema automáticamente guarda el CAEA en la base de datos

### 2. Uso del CAEA

1. **Facturación offline**: Los comprobantes se generan automáticamente con el CAEA activo
2. **Tracking**: El sistema registra qué CAEA se utilizó para cada comprobante

### 3. Información de Movimientos

Al finalizar el período de validez del CAEA:

```bash
POST /api/caea/informar
{
  "caeaCode": "21234567890123",
  "puntoVenta": 2
}
```

El sistema automáticamente:
- Verifica si hay comprobantes emitidos con el CAEA
- Si NO hay comprobantes → usa `FECAEASinMovimientoInformar`
- Si SÍ hay comprobantes → usa `FECAEARegInformativo`

## Validaciones y Restricciones

### Solicitud de CAEA
- **Período**: Debe tener formato YYYYMM (ej: "202507")
- **Orden**: Debe ser 1 o 2
- **Punto de venta**: Debe ser mayor a 0
- **Credenciales**: Deben estar válidas en AFIP

### Información de Movimientos
- **CAEA existente**: El código debe existir en la base de datos
- **No informado previamente**: No se puede informar el mismo CAEA dos veces
- **Período vencido**: Se recomienda informar después del período de validez

## Códigos de Error Comunes

| Código | Descripción | Solución |
|--------|-------------|----------|
| 400 | Datos de entrada inválidos | Verificar formato de período, orden y punto de venta |
| 500 | Error de comunicación con AFIP | Verificar conectividad y credenciales |
| 400 | CAEA ya informado | No se puede informar el mismo CAEA múltiples veces |
| 404 | CAEA no encontrado | Verificar que el código CAEA existe en la base de datos |

## Consideraciones de Implementación

### Base de Datos
- Los CAEAs se almacenan en la tabla `caea_codes`
- Los comprobantes offline incluyen referencia al CAEA utilizado
- Se mantiene tracking de informes realizados

### Seguridad
- Todos los endpoints requieren credenciales válidas de AFIP
- Se valida la existencia y estado de los CAEAs antes de operar

### Logging
- Todas las operaciones se registran en logs para auditoría
- Se incluyen detalles de respuestas de AFIP para debugging

## Automatización Futura

Los endpoints están diseñados para invocación manual, pero pueden ser automatizados:

1. **Solicitud automática**: Programar solicitudes de CAEA antes de cada período
2. **Información automática**: Programar informes al finalizar cada período
3. **Monitoreo**: Alertas cuando CAEAs están próximos a vencer

## Ejemplos de Uso con curl

```bash
# Solicitar CAEA
curl -X POST http://localhost:8080/api/caea/solicitar \
  -H "Content-Type: application/json" \
  -d '{"puntoVenta": 2, "periodo": "202507", "orden": 1}'

# Informar automáticamente
curl -X POST http://localhost:8080/api/caea/informar \
  -H "Content-Type: application/json" \
  -d '{"caeaCode": "21234567890123", "puntoVenta": 2}'

# Consultar CAEAs
curl http://localhost:8080/api/caea/punto-venta/2
```
