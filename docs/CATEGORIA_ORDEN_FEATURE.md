# Campo Orden en Categorías

## Resumen

Se ha agregado un campo `orden` a las categorías para facilitar la visualización ordenada de productos en el frontend. Las categorías ahora se pueden ordenar según un valor numérico específico en lugar de solo alfabéticamente.

## Cambios Realizados

### 1. Base de Datos
- **Migración V23**: Agregado campo `orden INTEGER` a la tabla `categorias`
- **Inicialización**: El orden se inicializa basado en el orden alfabético actual para preservar la visualización existente
- **Constraint**: El orden debe ser un número positivo (`orden > 0`)
- **Ordenamiento**: Las categorías se ordenan primero por `orden` y luego por `nombre`

### 2. Modelo de Dominio
- **Categoria.kt**: Agregado campo `orden: Int?` opcional
- **Validación**: El orden debe ser un número positivo si se proporciona
- **Factory method**: Actualizado `Categoria.create()` para incluir el parámetro orden

### 3. DTOs
- **CategoriaDto**: Agregado campo `orden: Int?` para requests
- **CategoriaResponse**: Agregado campo `orden: Int?` para responses
- **BulkUpdateCategoriasOrdenRequest**: Nuevo DTO para actualización bulk de orden
- **CategoriaOrdenItem**: DTO para item individual de orden (id + orden)
- **BulkUpdateCategoriasOrdenResponse**: DTO de respuesta para bulk update

### 4. Repositorio
- **JooqCategoriaRepository**: 
  - Actualizado para manejar el campo orden en operaciones CRUD
  - Modificado `findAll()` para ordenar por `orden ASC, nombre ASC`
  - Agregado método `updateOrdenMultiple()` para bulk update

### 5. Servicio
- **CategoriaService**: Agregado método `updateOrdenCategorias()`
- **CategoriaServiceImpl**: Implementación con validación de existencia de categorías

### 6. Controlador
- **CategoriaController**: 
  - Actualizado mapeo para incluir orden
  - Agregado método `updateOrdenCategorias()` con validaciones

### 7. Rutas
- **Nuevo endpoint**: `PUT /api/categorias/orden` para bulk update de orden

## API Endpoints

### Obtener Categorías (Modificado)
```http
GET /api/categorias
```

**Response** (ahora incluye orden):
```json
{
  "categorias": [
    {
      "id": 1,
      "nombre": "Bebidas",
      "descripcion": "Bebidas frías y calientes",
      "color": "ff0000",
      "orden": 1,
      "activo": true
    },
    {
      "id": 2,
      "nombre": "Postres",
      "descripcion": "Postres y dulces",
      "color": null,
      "orden": 2,
      "activo": true
    }
  ],
  "total": 2
}
```

### Crear/Actualizar Categoría (Modificado)
```http
POST /api/categorias
PUT /api/categorias/{id}
```

**Request** (orden es opcional):
```json
{
  "nombre": "Nueva Categoría",
  "descripcion": "Descripción opcional",
  "color": "00ff00",
  "orden": 5,
  "activo": true
}
```

### Actualizar Orden de Categorías (Nuevo)
```http
PUT /api/categorias/orden
```

**Request**:
```json
{
  "categorias": [
    {"id": 1, "orden": 3},
    {"id": 2, "orden": 1},
    {"id": 3, "orden": 2}
  ]
}
```

**Response**:
```json
{
  "actualizadas": 3,
  "mensaje": "Se actualizó el orden de 3 categoría(s) correctamente"
}
```

## Validaciones

### Campo Orden
- **Opcional**: El campo orden puede ser `null`
- **Positivo**: Si se proporciona, debe ser mayor a 0
- **Único**: No hay restricción de unicidad (múltiples categorías pueden tener el mismo orden)

### Bulk Update
- **Lista no vacía**: Debe proporcionar al menos una categoría
- **Orden positivo**: Todos los órdenes deben ser > 0
- **Categorías existentes**: Todas las categorías deben existir y estar activas

## Comportamiento de Ordenamiento

1. **Orden primario**: Por campo `orden` ascendente
2. **Orden secundario**: Por `nombre` ascendente (para categorías con mismo orden o sin orden)
3. **Categorías sin orden**: Se muestran después de las que tienen orden

## Migración de Datos

La migración V23 inicializa automáticamente el orden basado en el orden alfabético actual:
- Las categorías existentes mantienen su orden visual actual
- Se asigna orden secuencial (1, 2, 3...) basado en orden alfabético
- Las categorías inactivas reciben orden 999

## Ejemplos de Uso

### Reorganizar categorías para mostrar "Promociones" primero
```json
PUT /api/categorias/orden
{
  "categorias": [
    {"id": 5, "orden": 1},  // Promociones
    {"id": 1, "orden": 2},  // Bebidas
    {"id": 2, "orden": 3},  // Comidas
    {"id": 3, "orden": 4}   // Postres
  ]
}
```

### Crear nueva categoría con orden específico
```json
POST /api/categorias
{
  "nombre": "Ofertas Especiales",
  "orden": 1,
  "color": "ff0000"
}
```

## Testing

Se incluyen tests unitarios en `CategoriaControllerOrdenTest.kt` que verifican:
- Actualización exitosa de múltiples categorías
- Validación de lista vacía
- Validación de órdenes no positivos
- Actualización de categoría individual
