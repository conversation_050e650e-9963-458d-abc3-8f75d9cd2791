# Campo Color en Categorías

## Resumen

Se ha agregado un campo opcional `color` a las categorías para permitir que el frontend asigne colores visuales a cada categoría.

## Cambios Realizados

### 1. Base de Datos
- **Migración V17**: Agregado campo `color VARCHAR(6)` opcional a la tabla `categorias`
- El campo almacena valores hex de 6 caracteres (rrggbb) sin el prefijo '#'

### 2. Modelo de Dominio
- **Categoria.kt**: Agregado campo `color: String?` opcional
- **Validación**: El color debe ser un valor hex válido de 6 caracteres (0-9, a-f, A-F)
- **Factory method**: Actualizado `Categoria.create()` para incluir el parámetro color

### 3. DTOs
- **CategoriaDto**: Agregado campo `color: String?` para requests
- **CategoriaResponse**: Agregado campo `color: String?` para responses

### 4. Repositorio
- **JooqCategoriaRepository**: Actualizado para manejar el campo color en operaciones CRUD
- **Código JOOQ**: Regenerado para incluir el nuevo campo

### 5. Controlador
- **CategoriaController**: Actualizado mapeo entre DTOs y modelo de dominio

## Ejemplos de Uso

### Crear Categoría con Color

```json
POST /api/categorias
{
    "nombre": "Bebidas",
    "descripcion": "Bebidas frías y calientes",
    "color": "ff0000",
    "activo": true
}
```

### Crear Categoría sin Color

```json
POST /api/categorias
{
    "nombre": "Postres",
    "descripcion": "Postres y dulces",
    "activo": true
}
```

### Respuesta con Color

```json
{
    "id": 1,
    "nombre": "Bebidas",
    "descripcion": "Bebidas frías y calientes",
    "color": "ff0000",
    "activo": true
}
```

### Respuesta sin Color

```json
{
    "id": 2,
    "nombre": "Postres",
    "descripcion": "Postres y dulces",
    "color": null,
    "activo": true
}
```

## Validaciones

### Colores Válidos
- `"000000"` - Negro
- `"ffffff"` - Blanco
- `"ff0000"` - Rojo
- `"00ff00"` - Verde
- `"0000ff"` - Azul
- `"FF0000"` - Rojo (mayúsculas también válidas)

### Colores Inválidos
- `"gggggg"` - Caracteres inválidos
- `"12345"` - Muy corto
- `"1234567"` - Muy largo
- `"#ff0000"` - Con prefijo #
- `""` - Vacío
- `" ff0000"` - Con espacios

## Compatibilidad

- **Backward Compatible**: Las categorías existentes tendrán `color: null`
- **Frontend**: Puede usar el color para styling visual o usar un color por defecto si es null
- **API**: Todos los endpoints existentes siguen funcionando igual

## Tests

Se agregaron tests para:
- Validación de formato hex en el modelo de dominio
- Mapeo correcto en controladores
- Operaciones CRUD con y sin color
- Casos de error para colores inválidos

## Uso en Frontend

```javascript
// Ejemplo de uso en frontend
const categoria = {
    id: 1,
    nombre: "Bebidas",
    color: "ff0000"
};

// Aplicar color si existe, sino usar color por defecto
const backgroundColor = categoria.color ? `#${categoria.color}` : '#cccccc';
```
