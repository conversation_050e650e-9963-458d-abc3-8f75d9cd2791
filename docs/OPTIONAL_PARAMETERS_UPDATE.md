# Actualización: Parámetros Opcionales para Comprobantes

## Resumen de Cambios

Se han implementado parámetros opcionales para el punto de venta y tipo de comprobante, con valores por defecto configurables desde variables de entorno.

## Cambios Implementados

### 1. Nuevo Servicio de Configuración

**Archivo**: `ComprobanteConfigurationService.kt`

- Maneja valores por defecto desde variables de entorno
- Valida parámetros de entrada
- Resuelve configuración aplicando valores por defecto

### 2. Variables de Entorno

**Nuevas variables en `.env`**:
```env
PUNTO_DE_VENTA=1                    # Punto de venta por defecto
TIPO_COMPROBANTE_DEFAULT=FACTURA_B  # Tipo de comprobante por defecto
```

### 3. Interfaces Actualizadas

**ComprobanteService**:
```kotlin
// Antes
suspend fun generarComprobanteOnline(
    ventaId: Id,
    tipoComprobante: String,
    puntoVenta: Int
): Id

// Ahora
suspend fun generarComprobanteOnline(
    ventaId: Id,
    tipoComprobante: String? = null,  // Opcional
    puntoVenta: Int? = null           // Opcional
): Id
```

### 4. DTOs Actualizados

**GenerarComprobanteRequest**:
```kotlin
@Serializable
data class GenerarComprobanteRequest(
    val ventaId: Int,
    val tipoComprobante: String? = null,  // Opcional
    val modoGeneracion: String? = null    // Opcional: AUTO, ONLINE, OFFLINE
)
```

**Nota**: El campo `puntoVenta` fue eliminado. El punto de venta se obtiene automáticamente del archivo `.env`.

### 5. Validaciones

- **Punto de venta**: Debe ser > 0 y <= 9999
- **Tipo de comprobante**: Debe ser uno de los tipos válidos:
  - `FACTURA_A`, `FACTURA_B`, `FACTURA_C`
  - `NOTA_CREDITO_A`, `NOTA_CREDITO_B`, `NOTA_CREDITO_C`
  - `NOTA_DEBITO_A`, `NOTA_DEBITO_B`, `NOTA_DEBITO_C`

## Ejemplos de Uso

### 1. Usando Valores por Defecto
```kotlin
// Solo especificar venta ID
val comprobanteId = comprobanteService.generarComprobanteOnline(
    ventaId = Id(123)
)
// Usa TIPO_COMPROBANTE_DEFAULT y PUNTO_DE_VENTA desde .env
```

### 2. Especificando Solo Tipo de Comprobante
```kotlin
val comprobanteId = comprobanteService.generarComprobanteOnline(
    ventaId = Id(123),
    tipoComprobante = "FACTURA_A"
    // puntoVenta usa valor por defecto desde .env
)
```

### 3. Especificando Solo Punto de Venta
```kotlin
val comprobanteId = comprobanteService.generarComprobanteOnline(
    ventaId = Id(123),
    puntoVenta = 2
    // tipoComprobante usa valor por defecto desde .env
)
```

### 4. Especificando Ambos Parámetros
```kotlin
val comprobanteId = comprobanteService.generarComprobanteOnline(
    ventaId = Id(123),
    tipoComprobante = "NOTA_CREDITO_B",
    puntoVenta = 3
)
```

## Endpoints REST

### Usando Valores por Defecto
```http
POST /api/comprobantes/online
{
  "ventaId": 123
}
```

### Especificando Parámetros
```http
POST /api/comprobantes/online
{
  "ventaId": 123,
  "tipoComprobante": "FACTURA_A",
  "puntoVenta": 2
}
```

## Compatibilidad

- ✅ **Retrocompatible**: Código existente sigue funcionando
- ✅ **Valores por defecto**: Se aplican automáticamente cuando no se especifican
- ✅ **Validación**: Se mantienen todas las validaciones existentes
- ✅ **Configuración**: Centralizada en variables de entorno

## Beneficios

1. **Simplicidad**: Menos parámetros requeridos en casos comunes
2. **Flexibilidad**: Configuración centralizada en .env
3. **Mantenibilidad**: Fácil cambio de valores por defecto
4. **Consistencia**: Mismos valores por defecto en toda la aplicación
5. **Validación**: Parámetros validados automáticamente

## Archivos Modificados

- `ComprobanteService.kt` - Interfaces actualizadas
- `ComprobanteServiceImpl.kt` - Implementación con valores por defecto
- `ComprobanteController.kt` - DTOs actualizados
- `SaleServiceImpl.kt` - Uso de configuración por defecto
- `ComprobanteConfigurationService.kt` - Nuevo servicio de configuración
- `.env.example` - Documentación de variables
- Documentación actualizada

## Configuración Recomendada

Para la mayoría de casos de uso, configurar en `.env`:

```env
PUNTO_DE_VENTA=1
TIPO_COMPROBANTE_DEFAULT=FACTURA_B
```

Esto permite que la mayoría de operaciones se realicen especificando solo el ID de venta.
