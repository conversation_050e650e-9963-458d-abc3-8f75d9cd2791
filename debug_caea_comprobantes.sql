-- Script para debuggear el problema de comprobantes CAEA no detectados
-- CAEA problemático: 35302698650247

-- 1. Verificar el CAEA en la tabla caea_codes
SELECT 
    id, caea, punto_venta, periodo, fecha_desde, fecha_hasta, 
    orden, estado, informado, tipo_informe
FROM caea_codes 
WHERE caea = '35302698650247';

-- 2. <PERSON><PERSON> comprobantes que podrían estar asociados a este CAEA
-- Verificar si hay comprobantes con caea_utilizado
SELECT 
    id, numero_comprobante, tipo_comprobante, fecha_emision, 
    estado, caea_utilizado, imp_total, imp_neto, imp_iva
FROM comprobantes 
WHERE caea_utilizado = '35302698650247';

-- 3. Verificar si hay comprobantes en el período del CAEA (202508-1: 2025-08-01 al 2025-08-15)
SELECT 
    id, numero_comprobante, tipo_comprobante, fecha_emision, 
    estado, caea_utilizado, imp_total, imp_neto, imp_iva
FROM comprobantes 
WHERE fecha_emision BETWEEN '2025-08-01' AND '2025-08-15'
ORDER BY fecha_emision DESC;

-- 4. Verificar todos los comprobantes recientes para ver si alguno debería tener este CAEA
SELECT 
    id, numero_comprobante, tipo_comprobante, fecha_emision, 
    estado, caea_utilizado, imp_total, imp_neto, imp_iva,
    punto_venta
FROM comprobantes 
WHERE fecha_emision >= '2025-08-01'
ORDER BY fecha_emision DESC, numero_comprobante DESC;

-- 5. Verificar si hay comprobantes con punto_venta = 2 (mismo PV que el CAEA)
SELECT 
    id, numero_comprobante, tipo_comprobante, fecha_emision, 
    estado, caea_utilizado, imp_total, imp_neto, imp_iva,
    punto_venta
FROM comprobantes 
WHERE punto_venta = 2 
  AND fecha_emision >= '2025-08-01'
ORDER BY fecha_emision DESC, numero_comprobante DESC;
