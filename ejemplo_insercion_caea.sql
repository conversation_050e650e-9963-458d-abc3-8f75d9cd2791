-- Ejemplo de inserción manual de códigos CAEA para testing
-- Ejecutar estos comandos en la base de datos para probar la funcionalidad offline

-- CAEA para julio 2025 (orden 1)
INSERT INTO caea_codes (
    caea, 
    punto_venta, 
    periodo, 
    fecha_desde, 
    fecha_hasta, 
    orden,
    estado
) VALUES (
    '21234567890123',  -- CAEA de 14 dígitos
    2,                 -- Punto de venta offline (configurado en .env)
    '202507',          -- Período julio 2025
    '2025-07-01',      -- Válido desde 1 de julio
    '2025-07-31',      -- V<PERSON>lid<PERSON> hasta 31 de julio
    1,                 -- Primer CAEA del período
    'ACTIVO'           -- Estado activo
);

-- CAEA para julio 2025 (orden 2) - backup
INSERT INTO caea_codes (
    caea, 
    punto_venta, 
    periodo, 
    fecha_desde, 
    fecha_hasta, 
    orden,
    estado
) VALUES (
    '21234567890124',  -- CAEA de 14 dígitos (diferente)
    2,                 -- Punto de venta offline
    '202507',          -- Período julio 2025
    '2025-07-01',      -- Válido desde 1 de julio
    '2025-07-31',      -- Válido hasta 31 de julio
    2,                 -- Segundo CAEA del período
    'ACTIVO'           -- Estado activo
);

-- CAEA para agosto 2025 (orden 1)
INSERT INTO caea_codes (
    caea, 
    punto_venta, 
    periodo, 
    fecha_desde, 
    fecha_hasta, 
    orden,
    estado
) VALUES (
    '21234567890125',  -- CAEA de 14 dígitos
    2,                 -- Punto de venta offline
    '202508',          -- Período agosto 2025
    '2025-08-01',      -- Válido desde 1 de agosto
    '2025-08-31',      -- Válido hasta 31 de agosto
    1,                 -- Primer CAEA del período
    'ACTIVO'           -- Estado activo
);

-- Verificar las inserciones
SELECT 
    id,
    caea,
    punto_venta,
    periodo,
    fecha_desde,
    fecha_hasta,
    orden,
    estado,
    ultimo_numero_factura_b,
    ultimo_numero_nota_credito_b,
    ultimo_numero_nota_debito_b,
    creado_en
FROM caea_codes 
ORDER BY periodo DESC, orden ASC;

-- Ejemplo de consulta para obtener CAEA activo
SELECT * FROM caea_codes 
WHERE punto_venta = 2 
  AND estado = 'ACTIVO'
  AND fecha_desde <= CURRENT_DATE 
  AND fecha_hasta >= CURRENT_DATE
ORDER BY orden ASC
LIMIT 1;
